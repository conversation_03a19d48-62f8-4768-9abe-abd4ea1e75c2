import { describe, it, expect } from 'vitest';
import {
	tenderSchema,
	tenderLineItemSchema,
	tenderWbsMappingSchema,
	createTenderSchema,
	createLineItemSchema,
	editLineItemSchema,
	updateNormalizationSchema,
} from '$lib/schemas/tender';

describe('Tender Schema Validation', () => {
	describe('tenderSchema', () => {
		it('should validate a complete tender object', () => {
			const validTender = {
				tender_id: '123e4567-e89b-12d3-a456-426614174000',
				project_id: '123e4567-e89b-12d3-a456-426614174001',
				vendor_id: '123e4567-e89b-12d3-a456-426614174002',
				tender_name: 'Test Tender',
				description: 'A test tender',
				submission_date: new Date('2024-01-01'),
				currency_code: 'USD',
				status: 'submitted' as const,
				notes: 'Test notes',
				created_at: '2024-01-01T00:00:00Z',
				updated_at: '2024-01-01T00:00:00Z',
				created_by_user_id: '123e4567-e89b-12d3-a456-426614174003',
			};

			const result = tenderSchema.safeParse(validTender);
			if (!result.success) {
				console.log('Validation errors:', result.error.issues);
			}
			expect(result.success).toBe(true);
		});

		it('should reject invalid status values', () => {
			const invalidTender = {
				tender_id: '123e4567-e89b-12d3-a456-426614174000',
				project_id: '123e4567-e89b-12d3-a456-426614174001',
				vendor_id: '123e4567-e89b-12d3-a456-426614174002',
				tender_name: 'Test Tender',
				submission_date: '2024-01-01',
				currency_code: 'USD',
				status: 'invalid_status',
			};

			const result = tenderSchema.safeParse(invalidTender);
			expect(result.success).toBe(false);
		});
	});

	describe('createTenderSchema', () => {
		it('should validate tender creation data', () => {
			const validCreateData = {
				tender_name: 'New Tender',
				vendor_id: '123e4567-e89b-12d3-a456-426614174002',
				submission_date: new Date('2024-01-01'),
				currency_code: 'USD',
				status: 'submitted' as const,
				description: 'A new tender',
				notes: 'Creation notes',
			};

			const result = createTenderSchema.safeParse(validCreateData);
			expect(result.success).toBe(true);
		});

		it('should require mandatory fields', () => {
			const invalidCreateData = {
				tender_name: 'New Tender',
				// Missing vendor_id and submission_date
			};

			const result = createTenderSchema.safeParse(invalidCreateData);
			expect(result.success).toBe(false);
		});
	});

	describe('tenderLineItemSchema', () => {
		it('should validate a complete line item', () => {
			const validLineItem = {
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174000',
				tender_revision_id: '123e4567-e89b-12d3-a456-426614174001',
				line_number: 1,
				description: 'Test line item',
				quantity: 10,
				unit: 'each',
				material_rate: 100.5,
				labor_rate: 50.25,
				productivity_factor: 8,
				unit_rate: 150.75,
				subtotal: 1507.5,
				normalization_type: 'amount' as const,
				normalization_amount: 1500.0,
				normalization_percentage: null,
				notes: 'Line item notes',
				created_at: '2024-01-01T00:00:00Z',
				updated_at: '2024-01-01T00:00:00Z',
				created_by_user_id: '123e4567-e89b-12d3-a456-426614174003',
			};

			const result = tenderLineItemSchema.safeParse(validLineItem);
			expect(result.success).toBe(true);
		});

		it('should validate normalization types', () => {
			const lineItemWithPercentage = {
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174000',
				tender_revision_id: '123e4567-e89b-12d3-a456-426614174001',
				line_number: 1,
				description: 'Test line item',
				subtotal: 1507.5,
				normalization_type: 'percentage' as const,
				normalization_amount: null,
				normalization_percentage: 95.5,
			};

			const result = tenderLineItemSchema.safeParse(lineItemWithPercentage);
			expect(result.success).toBe(true);
		});
	});

	describe('tenderWbsMappingSchema', () => {
		it('should validate WBS mapping', () => {
			const validMapping = {
				tender_wbs_mapping_id: '123e4567-e89b-12d3-a456-426614174000',
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174001',
				wbs_library_item_id: '123e4567-e89b-12d3-a456-426614174002',
				coverage_percentage: 100,
				coverage_quantity: 10,
				notes: 'Mapping notes',
				created_at: '2024-01-01T00:00:00Z',
				updated_at: '2024-01-01T00:00:00Z',
				created_by_user_id: '123e4567-e89b-12d3-a456-426614174003',
			};

			const result = tenderWbsMappingSchema.safeParse(validMapping);
			expect(result.success).toBe(true);
		});

		it('should validate coverage percentage range', () => {
			const invalidMapping = {
				tender_wbs_mapping_id: '123e4567-e89b-12d3-a456-426614174000',
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174001',
				wbs_library_item_id: '123e4567-e89b-12d3-a456-426614174002',
				coverage_percentage: 150, // Invalid: > 100
			};

			const result = tenderWbsMappingSchema.safeParse(invalidMapping);
			expect(result.success).toBe(false);
		});
	});

	describe('updateNormalizationSchema', () => {
		it('should validate amount-based normalization', () => {
			const validNormalization = {
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174000',
				normalization_type: 'amount' as const,
				normalization_amount: 1500.0,
				normalization_percentage: null,
			};

			const result = updateNormalizationSchema.safeParse(validNormalization);
			expect(result.success).toBe(true);
		});

		it('should validate percentage-based normalization', () => {
			const validNormalization = {
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174000',
				normalization_type: 'percentage' as const,
				normalization_amount: null,
				normalization_percentage: 95.5,
			};

			const result = updateNormalizationSchema.safeParse(validNormalization);
			expect(result.success).toBe(true);
		});

		it('should reject invalid normalization combinations', () => {
			const invalidNormalization = {
				tender_line_item_id: '123e4567-e89b-12d3-a456-426614174000',
				normalization_type: 'amount' as const,
				normalization_amount: null, // Should have amount when type is 'amount'
				normalization_percentage: 95.5,
			};

			const result = updateNormalizationSchema.safeParse(invalidNormalization);
			expect(result.success).toBe(false);
		});
	});

	describe('createLineItemSchema', () => {
		it('should validate line item creation data', () => {
			const validCreateData = {
				description: 'New line item',
				line_number: 1,
				normalization_type: 'amount' as const,
				quantity: 10,
				unit: 'each',
				material_rate: 100.5,
				labor_rate: 50.25,
				productivity_factor: 8,
				unit_rate: 150.75,
				subtotal: 1507.5,
				normalization_amount: 1500.0,
				notes: 'Creation notes',
			};

			const result = createLineItemSchema.safeParse(validCreateData);
			expect(result.success).toBe(true);
		});

		it('should require mandatory fields for creation', () => {
			const invalidCreateData = {
				description: 'New line item',
				// Missing line_number and normalization_type
			};

			const result = createLineItemSchema.safeParse(invalidCreateData);
			expect(result.success).toBe(false);
		});
	});

	describe('editLineItemSchema', () => {
		it('should validate line item edit data', () => {
			const validEditData = {
				description: 'Updated line item',
				quantity: 15,
				unit_rate: 200.0,
				subtotal: 3000.0,
			};

			const result = editLineItemSchema.safeParse(validEditData);
			expect(result.success).toBe(true);
		});

		it('should allow partial updates', () => {
			const partialEditData = {
				description: 'Updated description',
				quantity: 20,
			};

			const result = editLineItemSchema.safeParse(partialEditData);
			if (!result.success) {
				console.log('Validation errors:', result.error.issues);
			}
			expect(result.success).toBe(true);
		});
	});
});
