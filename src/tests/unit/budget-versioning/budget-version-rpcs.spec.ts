/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '$lib/database.types';

// Mock Supabase client
const mockSupabase = {
	rpc: vi.fn(),
	from: vi.fn(),
	auth: {
		getUser: vi.fn(),
	},
} as unknown as SupabaseClient<Database>;

describe('Budget Versioning RPC Functions', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('create_budget_version', () => {
		it('should create a new budget version with default parameters', async () => {
			const mockVersionId = '550e8400-e29b-41d4-a716-************';
			const projectId = '550e8400-e29b-41d4-a716-************';

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockVersionId,
				error: null,
			});

			const result = await mockSupabase.rpc('create_budget_version', {
				p_project_id: projectId,
			});

			expect(mockSupabase.rpc).toHaveBeenCalledWith('create_budget_version', {
				p_project_id: projectId,
			});
			expect(result.data).toBe(mockVersionId);
			expect(result.error).toBeNull();
		});

		it('should create a stage version with stage_id', async () => {
			const mockVersionId = '550e8400-e29b-41d4-a716-************';
			const projectId = '550e8400-e29b-41d4-a716-************';
			const stageId = '550e8400-e29b-41d4-a716-446655440003';

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockVersionId,
				error: null,
			});

			const result = await mockSupabase.rpc('create_budget_version', {
				p_project_id: projectId,
				p_label: 'Stage Completion Test',
				p_kind: 'stage',
				p_stage_id: stageId,
			});

			expect(mockSupabase.rpc).toHaveBeenCalledWith('create_budget_version', {
				p_project_id: projectId,
				p_label: 'Stage Completion Test',
				p_kind: 'stage',
				p_stage_id: stageId,
			});
			expect(result.data).toBe(mockVersionId);
		});

		it('should handle authentication errors', async () => {
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: null,
				error: { message: 'Unauthorized: auth.uid() is NULL' },
			});

			const result = await mockSupabase.rpc('create_budget_version', {
				p_project_id: '550e8400-e29b-41d4-a716-************',
			});

			expect(result.error?.message).toBe('Unauthorized: auth.uid() is NULL');
		});

		it('should handle permission errors', async () => {
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: null,
				error: { message: 'Forbidden: no modify access to project' },
			});

			const result = await mockSupabase.rpc('create_budget_version', {
				p_project_id: '550e8400-e29b-41d4-a716-************',
			});

			expect(result.error?.message).toBe('Forbidden: no modify access to project');
		});
	});

	describe('activate_budget_version', () => {
		it('should activate a budget version successfully', async () => {
			const versionId = '550e8400-e29b-41d4-a716-************';

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: null,
				error: null,
			});

			const result = await mockSupabase.rpc('activate_budget_version', {
				p_version_id: versionId,
				p_reason: 'Test activation',
			});

			expect(mockSupabase.rpc).toHaveBeenCalledWith('activate_budget_version', {
				p_version_id: versionId,
				p_reason: 'Test activation',
			});
			expect(result.error).toBeNull();
		});

		it('should handle version not found errors', async () => {
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: null,
				error: { message: 'Budget version not found' },
			});

			const result = await mockSupabase.rpc('activate_budget_version', {
				p_version_id: 'non-existent-id',
				p_reason: 'Test activation',
			});

			expect(result.error?.message).toBe('Budget version not found');
		});
	});

	describe('list_budget_versions', () => {
		it('should return paginated version list', async () => {
			const mockVersions = [
				{
					budget_version_id: '550e8400-e29b-41d4-a716-************',
					label: 'Import Version',
					kind: 'import',
					is_active: true,
					item_count: 150,
					total_cost: 1500000,
					created_at: '2024-01-01T00:00:00Z',
				},
				{
					budget_version_id: '550e8400-e29b-41d4-a716-************',
					label: 'Stage Version',
					kind: 'stage',
					is_active: false,
					item_count: 120,
					total_cost: 1200000,
					created_at: '2023-12-01T00:00:00Z',
				},
			];

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockVersions,
				error: null,
			});

			const result = await mockSupabase.rpc('list_budget_versions', {
				p_project_id: '550e8400-e29b-41d4-a716-446655440003',
				p_limit: 50,
			});

			expect(result.data).toEqual(mockVersions);
			expect(result.data).toHaveLength(2);
			expect(result.data![0].is_active).toBe(true);
		});

		it('should handle empty version list', async () => {
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: [],
				error: null,
			});

			const result = await mockSupabase.rpc('list_budget_versions', {
				p_project_id: '550e8400-e29b-41d4-a716-446655440003',
			});

			expect(result.data).toEqual([]);
		});
	});

	describe('diff_budget_versions', () => {
		it('should return diff between two versions', async () => {
			const mockDiff = {
				added: [
					{
						code: '1.1.1',
						description: 'New Item',
						quantity: 10,
						unit_rate: 100,
						factor: 1,
					},
				],
				removed: [
					{
						code: '1.1.2',
						description: 'Removed Item',
						quantity: 5,
						unit_rate: 50,
						factor: 1,
					},
				],
				changed: [
					{
						code: '1.1.3',
						description: 'Changed Item',
						diff: {
							quantity: { old: 10, new: 15 },
							unit_rate: { old: 100, new: 120 },
						},
					},
				],
				summary: {
					version_a: '550e8400-e29b-41d4-a716-************',
					version_b: '550e8400-e29b-41d4-a716-************',
					total_cost_a: 1000000,
					total_cost_b: 1200000,
					total_cost_delta: 200000,
				},
			};

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockDiff,
				error: null,
			});

			const result = await mockSupabase.rpc('diff_budget_versions', {
				p_version_a: '550e8400-e29b-41d4-a716-************',
				p_version_b: '550e8400-e29b-41d4-a716-************',
			});

			expect(result.data).toEqual(mockDiff);
			expect(result.data.summary.total_cost_delta).toBe(200000);
		});
	});

	describe('diff_active_vs_items', () => {
		it('should return diff between active version and import items', async () => {
			const importItems = [
				{
					code: '1.1.1',
					description: 'Import Item',
					quantity: 10,
					unit_rate: 100,
					factor: 1,
				},
			];

			const mockDiff = {
				added: importItems,
				removed: [],
				changed: [],
				summary: {
					total_cost_a: 0,
					total_cost_b: 1000,
					total_cost_delta: 1000,
				},
				new_wbs_codes: ['1.1.1'],
			};

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockDiff,
				error: null,
			});

			const result = await mockSupabase.rpc('diff_active_vs_items', {
				p_project_id: '550e8400-e29b-41d4-a716-446655440003',
				p_items: importItems,
			});

			expect(result.data).toEqual(mockDiff);
			expect(result.data.new_wbs_codes).toContain('1.1.1');
		});
	});

	describe('find_existing_import', () => {
		it('should find existing import by hash', async () => {
			const mockExistingImport = [
				{
					exists: true,
					budget_import_id: '550e8400-e29b-41d4-a716-************',
					version_id: '550e8400-e29b-41d4-a716-************',
					created_at: '2024-01-01T00:00:00Z',
					filename: 'test-import.xlsx',
				},
			];

			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: mockExistingImport,
				error: null,
			});

			const result = await mockSupabase.rpc('find_existing_import', {
				p_project_id: '550e8400-e29b-41d4-a716-446655440003',
				p_source_hash: 'abc123hash',
			});

			expect(result.data).toEqual(mockExistingImport);
			expect(result.data![0].exists).toBe(true);
		});

		it('should return empty result for non-existent import', async () => {
			mockSupabase.rpc = vi.fn().mockResolvedValue({
				data: [],
				error: null,
			});

			const result = await mockSupabase.rpc('find_existing_import', {
				p_project_id: '550e8400-e29b-41d4-a716-446655440003',
				p_source_hash: 'nonexistent',
			});

			expect(result.data).toEqual([]);
		});
	});
});
