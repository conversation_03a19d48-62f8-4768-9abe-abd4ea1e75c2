import { createBrowserClient, createServer<PERSON>lient, isBrowser } from '@supabase/ssr';
import {
	PUBLIC_SUPABASE_ANON_KEY,
	PUBLIC_SUPABASE_URL,
	PUBLIC_POSTHOG_KEY,
	PUBLIC_POSTHOG_HOST,
} from '$env/static/public';
import posthog from 'posthog-js';
import { browser, dev } from '$app/environment';
import type { LayoutLoad } from './$types';
import type { Database } from '$lib/database.types';

export const load: LayoutLoad = async ({ fetch, data, depends }) => {
	// Initialize PostHog on the client
	if (browser && !dev) {
		posthog.init(PUBLIC_POSTHOG_KEY, {
			api_host: PUBLIC_POSTHOG_HOST,
			ui_host: 'https://eu.posthog.com',
			defaults: '2025-05-24',
			capture_pageview: false,
			capture_pageleave: false,
			capture_exceptions: false, // Error Tracking
		});
	}

	depends('supabase:auth');

	const supabase = isBrowser()
		? createBrowserClient<Database>(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: {
					fetch,
				},
			})
		: createServerClient<Database>(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: {
					fetch,
				},
				cookies: {
					getAll() {
						return data?.cookies ?? [];
					},
				},
			});

	/**
	 * It's fine to use `getSession` here, because on the client, `getSession` is
	 * safe, and on the server, it reads `session` from the `LayoutData`, which
	 * safely checked the session using `safeGetSession`.
	 */
	const session = isBrowser()
		? (await supabase.auth.getSession()).data.session
		: (data?.session ?? null);

	return {
		supabase,
		session,
		user: session?.user ?? data?.user ?? null,
		profile: data?.profile ?? null,
		sidebarClients: data?.sidebarClients ?? [],
	};
};
