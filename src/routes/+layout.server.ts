import type { LayoutServerLoad } from './$types';
import { loadFlash } from 'sveltekit-flash-message/server';
import { ORG_COOKIE_NAME } from '$lib/current-org.svelte';
import { error } from '@sveltejs/kit';

export const load: LayoutServerLoad = loadFlash(async ({ locals, depends, cookies }) => {
	depends('sidebar:clients');
	const { supabase, orgId, user, session } = locals;

	// Verify orgId from cookie if user is authenticated
	if (user && orgId) {
		// Check if the user still has access to the organization
		const { data: hasAccess, error: accessError } = await supabase.rpc(
			'current_user_has_entity_access',
			{
				entity_type_param: 'organization',
				entity_id_param: orgId,
			},
		);

		// If user no longer has access or there was an error, clear the cookie
		if (accessError || !hasAccess) {
			locals.log.info('Clearing org cookie - user no longer has access or org not found');
			cookies.delete(ORG_COOKIE_NAME, { path: '/' });
			// Update locals to reflect the cleared orgId
			locals.orgId = null;
		}
	}

	const { data: profile, error: profileError } = user
		? await supabase.from('profile').select('*').eq('user_id', user.id).single()
		: { data: null, error: null };

	if (profileError) {
		throw error(500, 'Something went wrong fetching your profile. Please try again.');
	}

	if (orgId) {
		// Fetch all clients for the sidebar
		const { data: clients, error } = await supabase
			.from('client')
			.select('client_id, name, organization(name)')
			.eq('org_id', orgId)
			.order('name');
		if (error) {
			locals.log.error({ msg: 'Error fetching clients for sidebar:', error });
		}

		return {
			session,
			user,
			profile,
			cookies: cookies.getAll(),
			sidebarClients: clients || [],
		};
	} else {
		return {
			session,
			user,
			profile,
			cookies: cookies.getAll(),
			sidebarClients: [],
		};
	}
});
