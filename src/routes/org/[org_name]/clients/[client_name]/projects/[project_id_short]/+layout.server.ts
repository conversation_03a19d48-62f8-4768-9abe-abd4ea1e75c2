import type { ServerLoad } from '@sveltejs/kit';
import type { Tables } from '$lib/database.types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: ServerLoad = async ({ locals, cookies, url }) => {
	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, project_stage!project_stage_project_id_fkey(*)')
		.eq('project_id', project_id)
		.order('stage_order', {
			referencedTable: 'project_stage',
			ascending: true,
		})
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		locals.log.error({ msg: 'Error fetching project', projectError });
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Fetch WBS items relevant to this project (standard + client/project custom)
	let wbsItems: Tables<'wbs_library_item'>[] = [];
	let wbsItemsMap: Record<string, { code: string; description: string }> = {};
	const orFilter = [
		'and(wbs_library_id.eq.' + project.wbs_library_id + ',item_type.eq.Standard)',
		'and(client_id.eq.' + project.client_id + ',item_type.eq.Custom,project_id.is.null)',
		'and(client_id.eq.' +
			project.client_id +
			',item_type.eq.Custom,project_id.eq.' +
			project.project_id +
			')',
	].join(',');

	const { data, error: wbsItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.or(orFilter)
		.order('code');

	if (wbsItemsError) {
		locals.log.error({ msg: 'Error fetching WBS items for layout', wbsItemsError });
	} else if (data) {
		wbsItems = data;
		wbsItemsMap = Object.fromEntries(
			data.map((i) => [i.wbs_library_item_id, { code: i.code, description: i.description }]),
		);
	}

	// If there are no WBS items yet, redirect users to the budget import flow
	// except when they are already on an allowed import route to avoid loops.
	if (wbsItems.length === 0) {
		const isOnImportRoute = url.pathname.includes('/budget/import');
		if (!isOnImportRoute) {
			return redirect(
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/budget/import`,
				{ type: 'error', message: 'Finish importing your budget to setup the project.' },
				cookies,
			);
		}
	}

	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project_id,
		},
	);

	if (canEditProjectError) {
		locals.log.error({ msg: 'Error checking project edit permissions', canEditProjectError });
	}

	const firstIncompleteStage = project.project_stage.find((stage) => !stage.date_completed);

	// Find the current active stage (first incomplete stage)
	// If all stages are completed, then there is no current stage (return undefined)
	const currentStage = project.project_stage.every((stage) => stage.date_completed)
		? undefined
		: firstIncompleteStage || project.project_stage[0];

	const isConstructionStage = currentStage?.project_stage_id === project.construction_stage_id;

	return {
		client_name: client_name,
		org_name: org_name,
		project_id_short: project_id_short,
		project,
		projectStages: project.project_stage,
		currentStage,
		isConstructionStage,
		canEditProject: canEditProject || false,
		wbsItems,
		wbsItemsMap,
	};
};
