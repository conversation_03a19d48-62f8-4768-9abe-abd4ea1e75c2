<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		FileText,
		Upload,
		User,
		TrendingUp,
		TrendingDown,
		Minus,
		Eye,
		CircleCheck,
		Undo2,
	} from '@lucide/svelte';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { toast } from 'svelte-sonner';
	import { superForm } from 'sveltekit-superforms';
	import { invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { DotsThreeVertical } from 'phosphor-svelte';
	import { formatCurrency, formatDate } from '$lib/utils';
	import type { PageData } from './$types';
	import type { Tables, Enums } from '$lib/database.types';

	type BudgetVersionKind = Enums<'budget_version_kind'>;
	type BudgetImportRow = Tables<'budget_import'>;

	interface BudgetVersion {
		budget_version_id: string;
		label: string | null;
		kind: BudgetVersionKind;
		is_active: boolean;
		item_count: number;
		total_cost: number;
		created_at: string;
	}

	interface BudgetVersionDiffSummary {
		version_a: string;
		version_b: string;
		total_cost_a: number;
		total_cost_b: number;
		total_cost_delta: number;
	}

	interface BudgetVersionDiffItem {
		wbs_library_item_id: string;
		code?: string;
		description?: string;
		quantity?: number;
		unit?: string;
		material_rate?: number;
		labor_rate?: number;
		productivity_per_hour?: number;
		unit_rate_manual_override?: number;
		unit_rate?: number;
		factor?: number;
		remarks?: string;
		cost_certainty?: string;
		design_certainty?: string;
		diff?: Record<
			string,
			{ from: string | number | boolean | null; to: string | number | boolean | null }
		>;
	}

	interface BudgetVersionDiff {
		added: BudgetVersionDiffItem[];
		removed: BudgetVersionDiffItem[];
		changed: BudgetVersionDiffItem[];
		summary: BudgetVersionDiffSummary;
	}

	let { data }: { data: PageData } = $props();

	const diffData = $derived(data.diffData as unknown as BudgetVersionDiff);

	// Superforms for actions
	const activateFormHandler = superForm(data.activateForm, {
		resetForm: false,
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					invalidate('project:budget-versions');
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { enhance: enhanceActivate, submitting: activateSubmitting } = activateFormHandler;

	const undoFormHandler = superForm(data.undoForm, {
		resetForm: false,
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					invalidate('project:budget-versions');
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { enhance: enhanceUndo, submitting: undoSubmitting } = undoFormHandler;

	function getVersionLabel(version: BudgetVersion): string {
		if (version.label) return version.label;

		switch (version.kind) {
			case 'stage':
				return 'Stage Version';
			case 'import':
				return 'Import Version';
			case 'manual':
				return 'Manual Version';
			case 'system':
				return 'System Version';
			default:
				return 'Unknown Version';
		}
	}

	function getVersionBadgeVariant(
		version: BudgetVersion,
	): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (version.is_active) return 'default';

		switch (version.kind) {
			case 'import':
				return 'secondary';
			case 'manual':
				return 'outline';
			default:
				return 'outline';
		}
	}

	function getVersionIcon(kind: string) {
		switch (kind) {
			case 'import':
				return Upload;
			case 'stage':
				return User;
			default:
				return FileText;
		}
	}

	function formatDiffValue(value: string | number | boolean | null | unknown): string {
		if (typeof value === 'number') {
			return value.toLocaleString();
		}
		return String(value || '');
	}

	// Newer RPC returns `diff` shaped as `{ from: {...}, to: {...} }`.
	// Normalize to an array of field-level changes for rendering.
	function extractFieldDiffs(
		item: BudgetVersionDiffItem,
	): Array<[field: string, change: { from: unknown; to: unknown }]> {
		const d = (
			item as unknown as { diff?: { from?: Record<string, unknown>; to?: Record<string, unknown> } }
		).diff;
		if (!d) return [];
		const from = d.from || {};
		const to = d.to || {};
		const fields = new Set([...Object.keys(from), ...Object.keys(to)]);
		const out: Array<[string, { from: unknown; to: unknown }]> = [];
		for (const f of fields) {
			const a = (from as Record<string, unknown>)[f];
			const b = (to as Record<string, unknown>)[f];
			// Only include fields whose values differ
			if (a !== b) out.push([f, { from: a, to: b }]);
		}
		return out;
	}

	function costFromFields(obj: Record<string, unknown> | undefined | null): number {
		const quantity = typeof obj?.quantity === 'number' ? (obj.quantity as number) : 0;
		const unit_rate = typeof obj?.unit_rate === 'number' ? (obj.unit_rate as number) : 0;
		const factor = typeof obj?.factor === 'number' ? (obj.factor as number) : 1;
		return quantity * unit_rate * factor;
	}

	function calculateItemCost(item: BudgetVersionDiffItem): number {
		const quantity = item.quantity || 0;
		const unitRate = item.unit_rate || 0;
		const factor = item.factor || 1;
		return quantity * unitRate * factor;
	}

	const versionsUrl = $derived(
		`/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(data.client_name)}/projects/${encodeURIComponent(data.project_id_short)}/budget/versions`,
	);

	// Helper to get import record for a version (used by both views)
	function getImportRecord(versionId: string) {
		return data.imports.find((imp: BudgetImportRow) => imp.new_version_id === versionId);
	}

	// Listing mode helpers/actions
	function findImportForVersion(versionId: string) {
		return data.imports.find((imp: BudgetImportRow) => imp.new_version_id === versionId);
	}

	// WBS lookup helpers from layout-provided map
	// Note: the project layout exposes both `wbsItems` (array) and `wbsItemsMap` (id->meta map).
	// We must use the map for O(1) lookups by `wbs_library_item_id`.
	const wbsMap = $derived(
		(page.data?.wbsItemsMap || {}) as Record<string, { code: string; description: string }>,
	);

	function getItemCode(item: BudgetVersionDiffItem): string {
		return item.code ?? wbsMap[item.wbs_library_item_id]?.code ?? '—';
	}

	function getItemDescription(item: BudgetVersionDiffItem): string {
		return item.description ?? wbsMap[item.wbs_library_item_id]?.description ?? '—';
	}
</script>

<svelte:head>
	<title>
		{data.compareVersion && data.withVersion
			? 'Budget Version Comparison'
			: 'Budget Version History'}
		- {data.project.name}
	</title>
</svelte:head>

{#if data.compareVersion && data.withVersion}
	<div class="space-y-6 px-4 py-16 md:px-8">
		<!-- Version Details -->
		<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
			<!-- Compare Version (A) -->
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						{@const Icon = getVersionIcon(data.compareVersion.kind)}
						<Icon class="h-5 w-5" />
						{getVersionLabel(data.compareVersion)}
					</CardTitle>
				</CardHeader>
				<CardContent class="space-y-3">
					{@const importRecord = getImportRecord(data.compareVersion.budget_version_id)}
					<div class="flex items-center gap-2">
						<Badge variant={getVersionBadgeVariant(data.compareVersion)}>
							{data.compareVersion.is_active ? 'Active' : data.compareVersion.kind}
						</Badge>
						{#if importRecord?.is_undone}
							<Badge variant="destructive">Undone</Badge>
						{/if}
						{#if importRecord}
							<div class="text-muted-foreground text-sm">
								<FileText class="mr-1 inline h-3 w-3" />
								{importRecord.source_filename}
							</div>
						{/if}
						<div class="space-y-1">
							<div class="text-sm">
								<span class="font-medium">Items:</span>
								{data.compareVersion.item_count.toLocaleString()}
							</div>
							<div class="text-sm">
								<span class="font-medium">Total Cost:</span>
								{formatCurrency(data.compareVersion.total_cost)}
							</div>
							<div class="text-sm">
								<span class="font-medium">Created:</span>
								{formatDate(data.compareVersion.created_at, 'MMM dd, yyyy')}
							</div>
						</div>
					</div></CardContent
				>
			</Card>

			<!-- With Version (B) -->
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						{@const Icon = getVersionIcon(data.withVersion.kind)}
						<Icon class="h-5 w-5" />
						{getVersionLabel(data.withVersion)}
					</CardTitle>
				</CardHeader>
				<CardContent class="space-y-3">
					{@const importRecord = getImportRecord(data.withVersion.budget_version_id)}
					<div class="flex items-center gap-2">
						<Badge variant={getVersionBadgeVariant(data.withVersion)}>
							{data.withVersion.is_active ? 'Active' : data.withVersion.kind}
						</Badge>
						{#if importRecord?.is_undone}
							<Badge variant="destructive">Undone</Badge>
						{/if}
					</div>
					{#if importRecord}
						<div class="text-muted-foreground text-sm">
							<FileText class="mr-1 inline h-3 w-3" />
							{importRecord.source_filename}
						</div>
					{/if}
					<div class="space-y-1">
						<div class="text-sm">
							<span class="font-medium">Items:</span>
							{data.withVersion.item_count.toLocaleString()}
						</div>
						<div class="text-sm">
							<span class="font-medium">Total Cost:</span>
							{formatCurrency(data.withVersion.total_cost)}
						</div>
						<div class="text-sm">
							<span class="font-medium">Created:</span>
							{formatDate(data.withVersion.created_at, 'MMM dd, yyyy')}
						</div>
					</div>
				</CardContent>
			</Card>
		</div>

		<!-- Diff Summary -->
		{#if diffData?.summary}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						{#if diffData.summary.total_cost_delta > 0}
							<TrendingUp class="h-5 w-5 text-green-600" />
						{:else if diffData.summary.total_cost_delta < 0}
							<TrendingDown class="h-5 w-5 text-red-600" />
						{:else}
							<Minus class="h-5 w-5 text-gray-600" />
						{/if}
						Comparison Summary
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="grid grid-cols-2 gap-4 md:grid-cols-4">
						<div class="text-center">
							<div class="text-2xl font-bold text-green-600">
								{diffData.added && Array.isArray(diffData.added) ? diffData.added.length : 0}
							</div>
							<div class="text-muted-foreground text-sm">Added Items</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-red-600">
								{diffData.removed && Array.isArray(diffData.removed) ? diffData.removed.length : 0}
							</div>
							<div class="text-muted-foreground text-sm">Removed Items</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-blue-600">
								{diffData.changed && Array.isArray(diffData.changed) ? diffData.changed.length : 0}
							</div>
							<div class="text-muted-foreground text-sm">Changed Items</div>
						</div>
						<div class="text-center">
							<div
								class="text-2xl font-bold {diffData.summary.total_cost_delta >= 0
									? 'text-green-600'
									: 'text-red-600'}"
							>
								{diffData.summary.total_cost_delta >= 0 ? '+' : ''}{formatCurrency(
									diffData.summary.total_cost_delta,
								)}
							</div>
							<div class="text-muted-foreground text-sm">Cost Change</div>
						</div>
					</div>
				</CardContent>
			</Card>
		{/if}

		<!-- Detailed Changes -->
		{#if diffData}
			<!-- Added Items -->
			{#if diffData.added && Array.isArray(diffData.added) && diffData.added.length > 0}
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2 text-green-600">
							<TrendingUp class="h-5 w-5" />
							Added Items ({diffData.added.length})
						</CardTitle>
					</CardHeader>
					<CardContent>
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Code</TableHead>
									<TableHead>Description</TableHead>
									<TableHead class="text-right">Quantity</TableHead>
									<TableHead>Unit</TableHead>
									<TableHead class="text-right">Unit Rate</TableHead>
									<TableHead class="text-right">Factor</TableHead>
									<TableHead class="text-right">Total Cost</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{#each diffData.added as item (item)}
									<TableRow>
										<TableCell class="font-mono text-sm">{getItemCode(item)}</TableCell>
										<TableCell class="max-w-36 whitespace-normal"
											><p>{getItemDescription(item)}</p></TableCell
										>
										<TableCell class="text-right"
											>{item.quantity?.toLocaleString() || '—'}</TableCell
										>
										<TableCell>{item.unit || '—'}</TableCell>
										<TableCell class="text-right"
											>{item.unit_rate ? formatCurrency(item.unit_rate) : '—'}</TableCell
										>
										<TableCell class="text-right">{item.factor?.toLocaleString() || '—'}</TableCell>
										<TableCell class="text-right font-medium"
											>{formatCurrency(calculateItemCost(item))}</TableCell
										>
									</TableRow>
								{/each}
							</TableBody>
						</Table>
					</CardContent>
				</Card>
			{/if}
		{/if}

		<!-- Removed Items -->
		{#if diffData.removed && Array.isArray(diffData.removed) && diffData.removed.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-red-600">
						<TrendingDown class="h-5 w-5" />
						Removed Items ({diffData.removed.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Code</TableHead>
								<TableHead>Description</TableHead>
								<TableHead class="text-right">Quantity</TableHead>
								<TableHead>Unit</TableHead>
								<TableHead class="text-right">Unit Rate</TableHead>
								<TableHead class="text-right">Factor</TableHead>
								<TableHead class="text-right">Total Cost</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each diffData.removed as item (item)}
								<TableRow>
									<TableCell class="font-mono text-sm">{getItemCode(item)}</TableCell>
									<TableCell class="max-w-36 whitespace-normal">
										<p>{getItemDescription(item)}</p>
									</TableCell>
									<TableCell class="text-right">{item.quantity?.toLocaleString() || '—'}</TableCell>
									<TableCell>{item.unit || '—'}</TableCell>
									<TableCell class="text-right"
										>{item.unit_rate ? formatCurrency(item.unit_rate) : '—'}</TableCell
									>
									<TableCell class="text-right">{item.factor?.toLocaleString() || '—'}</TableCell>
									<TableCell class="text-right font-medium"
										>{formatCurrency(calculateItemCost(item))}</TableCell
									>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		{/if}

		<!-- Changed Items -->
		{#if diffData.changed && Array.isArray(diffData.changed) && diffData.changed.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-blue-600">
						<FileText class="h-5 w-5" />
						Changed Items ({diffData.changed.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Code</TableHead>
								<TableHead>Description</TableHead>
								<TableHead>Field</TableHead>
								<TableHead>From</TableHead>
								<TableHead>To</TableHead>
								<TableHead class="text-right">Cost Impact</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each diffData.changed as item (item.wbs_library_item_id)}
								{#each extractFieldDiffs(item) as [field, change] (`${item.wbs_library_item_id}-${field}`)}
									<TableRow>
										<TableCell class="font-mono text-sm">{getItemCode(item)}</TableCell>
										<TableCell class="max-w-36 whitespace-normal"
											><p>{getItemDescription(item)}</p></TableCell
										>
										<TableCell class="capitalize">{field.replace(/_/g, ' ')}</TableCell>
										<TableCell class="text-red-600">{formatDiffValue(change.from)}</TableCell>
										<TableCell class="text-green-600">{formatDiffValue(change.to)}</TableCell>
										<TableCell class="text-right">
											{#if field === 'quantity' || field === 'unit_rate' || field === 'factor'}
												{@const oldCost = costFromFields(item.diff?.from)}
												{@const newCost = costFromFields(item.diff?.to)}
												{@const costDiff = newCost - oldCost}
												<span class={costDiff >= 0 ? 'text-green-600' : 'text-red-600'}>
													{costDiff >= 0 ? '+' : ''}{formatCurrency(costDiff)}
												</span>
											{:else}
												—
											{/if}
										</TableCell>
									</TableRow>
								{/each}
							{/each}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		{/if}
	</div>
{:else}
	<!-- Listing Mode (Version History) -->

	{#if Array.isArray(data.versions) && data.versions.length > 0}
		<div class="px-4 py-16 md:px-8">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Version</TableHead>
						<TableHead>Type</TableHead>
						<TableHead class="text-right">Items</TableHead>
						<TableHead class="text-right">Total Cost</TableHead>
						<TableHead>As of</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{@const active = (data.versions || []).find((v) => v.is_active)}
					{#each data.versions as version (version.budget_version_id)}
						{@const importRecord = findImportForVersion(version.budget_version_id)}
						<TableRow>
							<TableCell>
								<div class="flex items-center gap-2">
									<span class="font-medium">{getVersionLabel(version)}</span>
									{#if version.is_active}
										<Badge variant="default">Active</Badge>
									{/if}
									{#if importRecord?.is_undone}
										<Badge variant="destructive">Undone</Badge>
									{/if}
								</div>
								{#if importRecord}
									<div class="text-muted-foreground mt-1 text-sm">
										<FileText class="mr-1 inline h-3 w-3" />
										{importRecord.source_filename}
									</div>
								{/if}
							</TableCell>
							<TableCell>
								<div class="flex items-center gap-1">
									{#if version.kind === 'import'}
										<Upload class="h-4 w-4" />
									{:else if version.kind === 'stage'}
										<User class="h-4 w-4" />
									{:else}
										<FileText class="h-4 w-4" />
									{/if}
									<span class="capitalize">{version.kind}</span>
								</div>
							</TableCell>
							<TableCell class="text-right">{version.item_count.toLocaleString()}</TableCell>
							<TableCell class="text-right">{formatCurrency(version.total_cost)}</TableCell>
							<TableCell
								>{formatDate(
									version.effective_at ?? version.created_at,
									'MMM dd, yyyy HH:mm',
								)}</TableCell
							>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" size="sm">
												<DotsThreeVertical class="h-4 w-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item disabled={version.is_active}>
											{#if active}
												<a
													href={`${versionsUrl}?compare=${encodeURIComponent(active.budget_version_id)}&with=${encodeURIComponent(version.budget_version_id)}`}
												>
													<div class="flex items-center">
														<Eye class="h-4 w-4" />
														<span class="px-2"> Compare to Active </span>
													</div>
												</a>
											{/if}
										</DropdownMenu.Item>
										{#if data.canEditProject && !version.is_active}
											<form
												id={`activate-${version.budget_version_id}`}
												method="POST"
												action="?/activate"
												use:enhanceActivate
											>
												<input type="hidden" name="versionId" value={version.budget_version_id} />
											</form>
											<DropdownMenu.Item
												onclick={() =>
													(
														document.getElementById(
															`activate-${version.budget_version_id}`,
														) as HTMLFormElement
													)?.requestSubmit()}
												disabled={$activateSubmitting}
											>
												<CircleCheck class="h-4 w-4" />
												Activate Version
											</DropdownMenu.Item>
										{/if}
										{#if data.canEditProject && importRecord && !importRecord.is_undone && version.kind === 'import'}
											<DropdownMenu.Separator />
											<form
												id={`undo-${importRecord.budget_import_id}`}
												method="POST"
												action="?/undoImport"
												use:enhanceUndo
											>
												<input
													type="hidden"
													name="importId"
													value={importRecord.budget_import_id}
												/>
												<input type="hidden" name="filename" value={importRecord.source_filename} />
											</form>
											<DropdownMenu.Item
												onclick={() =>
													(
														document.getElementById(
															`undo-${importRecord.budget_import_id}`,
														) as HTMLFormElement
													)?.requestSubmit()}
												disabled={$undoSubmitting}
											>
												<Undo2 class="h-4 w-4" />
												Undo Import
											</DropdownMenu.Item>
										{/if}
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="text-muted-foreground">No versions found</div>
	{/if}
{/if}
