import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { editTenderSchema } from '$lib/schemas/tender';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import { getTenderById, updateTender } from '$lib/tender_utils';

export const load: PageServerLoad = async ({ locals, params }) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();
	const { tender_id_short } = params;

	if (!tender_id_short) {
		throw error(400, 'Tender ID is required');
	}

	const project_id = projectUUID(project_id_short);
	const tender_id = tenderUUID(tender_id_short);

	// Fetch tender details
	const tender = await getTenderById(supabase, tender_id);

	// Verify the tender belongs to the project
	if (tender.project_id !== project_id) {
		throw error(404, 'Tender not found');
	}

	// Fetch vendors for the project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: 'project',
		entity_id_param: project_id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors:', vendorsError });
		throw error(500, 'Failed to fetch vendors');
	}

	// Fetch available currencies
	const { data: currencies, error: currenciesError } = await supabase
		.from('currency')
		.select('currency_code, symbol, description')
		.order('currency_code');

	if (currenciesError) {
		locals.log.error({ msg: 'Error fetching currencies:', currenciesError });
		throw error(500, 'Failed to fetch currencies');
	}

	// Initialize form with tender data
	const form = await superValidate(
		{
			tender_name: tender.tender_name,
			description: tender.description,
			vendor_id: tender.vendor_id,
			submission_date: tender.submission_date,
			currency_code: tender.currency_code,
			status: tender.status,
			notes: tender.notes,
		},
		zod(editTenderSchema),
	);

	return {
		form,
		tender,
		vendors: vendors || [],
		currencies: currencies || [],
		tender_id_short,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const tender_id = tenderUUID(tender_id_short);

		const form = await superValidate(request, zod(editTenderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// Update the tender
			const tender = await updateTender(supabase, tender_id, form.data);

			throw redirect(
				302,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
					client_name,
				)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
					tender_id_short,
				)}`,
				{
					type: 'success',
					message: `Tender "${tender.tender_name}" has been updated successfully.`,
				},
				cookies,
			);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error updating tender:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update tender' },
			});
		}
	},
};
