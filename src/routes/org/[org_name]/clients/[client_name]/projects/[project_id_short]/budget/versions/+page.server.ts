import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate, message } from 'sveltekit-superforms/server';
import { z } from 'zod';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { projectUUID } from '$lib/schemas/project';

const activateSchema = z.object({
	versionId: z.string().min(1),
});

const undoImportSchema = z.object({
	importId: z.string().min(1),
	filename: z.string().optional(),
});

export const load: PageServerLoad = async ({ params, url, locals: { supabase }, depends }) => {
	depends('project:budget-versions');
	const { org_name, client_name, project_id_short } = params;
	const compareVersionId = url.searchParams.get('compare');
	const withVersionId = url.searchParams.get('with');

	// Convert project_id_short to UUID and get project info
	const project_id = projectUUID(project_id_short);

	// Load versions (used for both compare mode and listing mode)
	const { data: versions, error: versionsError } = await supabase.rpc('list_budget_versions', {
		p_project_id: project_id,
		p_limit: 100,
	});

	if (versionsError) {
		throw error(500, `Failed to load versions: ${versionsError.message}`);
	}

	// If both query params are present, compute diff and return compare view data
	if (compareVersionId && withVersionId) {
		const compareVersion = versions?.find((v) => v.budget_version_id === compareVersionId);
		const withVersion = versions?.find((v) => v.budget_version_id === withVersionId);

		if (!compareVersion || !withVersion) {
			throw error(404, 'One or both versions not found');
		}

		const { data: diffData, error: diffError } = await supabase.rpc('diff_budget_versions', {
			p_version_a: compareVersionId,
			p_version_b: withVersionId,
		});

		if (diffError) {
			throw error(500, `Failed to compute diff: ${diffError.message}`);
		}

		// Load related import records for all fetched versions (covers both compare cards)
		const versionIds = (versions || []).map((v) => v.budget_version_id);
		const { data: imports, error: importsError } = await supabase
			.from('budget_import')
			.select('*')
			.eq('project_id', project_id)
			.in('new_version_id', versionIds);

		if (importsError) {
			console.warn('Failed to load import records:', importsError.message);
		}

		const activateForm = await superValidate(zod(activateSchema));
		const undoForm = await superValidate(zod(undoImportSchema));

		return {
			compareVersion,
			withVersion,
			diffData,
			imports: imports || [],
			org_name,
			client_name,
			activateForm,
			undoForm,
		};
	}

	// Otherwise, return data for the version history listing (no diff)
	const versionIds = (versions || []).map((v) => v.budget_version_id);
	const { data: imports, error: importsError } = await supabase
		.from('budget_import')
		.select('*')
		.eq('project_id', project_id)
		.in('new_version_id', versionIds)
		.order('created_at', { ascending: false });

	if (importsError) {
		console.warn('Failed to load import records:', importsError.message);
	}

	const activateForm = await superValidate(zod(activateSchema));
	const undoForm = await superValidate(zod(undoImportSchema));

	// Show in chronological order (oldest first) with deterministic tiebreakers
	const kindOrder: Record<string, number> = { system: 0, stage: 1, manual: 2, import: 3 };
	const getTime = (v: { effective_at: string | null; created_at: string }) =>
		new Date(v.effective_at ?? v.created_at).getTime();
	const sortedVersions = [...(versions || [])].sort((a, b) => {
		const ta = getTime(a);
		const tb = getTime(b);
		if (ta !== tb) return ta - tb;
		// Prefer stage immediately before its contemporary manual/import versions
		const ka = kindOrder[a.kind as keyof typeof kindOrder] ?? 99;
		const kb = kindOrder[b.kind as keyof typeof kindOrder] ?? 99;
		if (ka !== kb) return ka - kb;
		// Final stable id tiebreaker
		return a.budget_version_id.localeCompare(b.budget_version_id);
	});

	return {
		versions: sortedVersions,
		imports: imports || [],
		org_name,
		client_name,
		activateForm,
		undoForm,
	};
};

export const actions: Actions = {
	activate: async ({ request, locals }) => {
		const form = await superValidate(request, zod(activateSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;
		const { error: rpcError } = await supabase.rpc('activate_budget_version', {
			p_version_id: form.data.versionId,
			p_reason: 'Activate from Version History',
		});

		if (rpcError) {
			return message(form, { type: 'error', text: rpcError.message }, { status: 500 });
		}

		return message(form, { type: 'success', text: 'Version activated' });
	},

	undoImport: async ({ request, locals }) => {
		const form = await superValidate(request, zod(undoImportSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;
		const { error: rpcError } = await supabase.rpc('undo_budget_import', {
			p_budget_import_id: form.data.importId,
			p_reason: `Undoing import${form.data.filename ? ` of ${form.data.filename}` : ''}`,
		});

		if (rpcError) {
			return message(form, { type: 'error', text: rpcError.message }, { status: 500 });
		}

		return message(form, { type: 'success', text: 'Import undone successfully' });
	},
};
