import { requireProject } from '$lib/server/auth';
import { requireUser } from '$lib/server/auth';
import type { PageServerLoad } from './$types';
import { projectUUID } from '$lib/schemas/project';

export const load = (async ({ locals, depends }) => {
	depends('project:budget');

	await requireUser();

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const { data: currentItems, error: itemsError } = await supabase.rpc(
		'get_active_budget_version_items',
		{ p_project_id: project_id },
	);

	if (itemsError) {
		locals.log.error({ msg: 'Error fetching active version items', itemsError });
	}

	const rawCurrentItems = (currentItems || []).map((bli) => ({ ...bli, label: 'current' }));

	return {
		// wbsItems provided by layout; no duplication here
		rawCurrentItems: rawCurrentItems || [],
	};
}) satisfies PageServerLoad;
