import { requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import { upsertBudgetLineItem } from '$lib/project_utils';
import type { PageServerLoad } from './$types';
import { budgetItemSchema, projectUUID } from '$lib/schemas/project';
import type { Tables } from '$lib/database.types';

export const load: PageServerLoad = async ({ locals, depends }) => {
	depends('project:budget');

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch budget items from the active budget version
	const { data: rawCurrentItems, error: budgetError } = await supabase.rpc(
		'get_active_budget_version_items',
		{
			p_project_id: project_id,
		},
	);

	if (budgetError) {
		locals.log.error({ msg: 'Error fetching active budget version items', budgetError });
		// Continue with empty budget items rather than failing completely
	}

	const typedRawCurrentItems = rawCurrentItems || [];

	// Create the form with the budget item schema
	const form = await superValidate(zod(budgetItemSchema));

	// is this construction stage?
	const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
		project_id_param: project_id,
	});
	// if yes, get risk register data
	let riskRegisterData: Tables<'risk_register'>[] = [];
	if (isConstructionStage) {
		const { data: riskData } = await supabase
			.from('risk_register')
			.select('*')
			.eq('project_id', project_id);
		riskRegisterData = riskData || [];
	}

	return {
		rawCurrentItems: typedRawCurrentItems,
		form,
		riskRegisterData,
	};
};

export const actions: Actions = {
	// Upsert a budget line item
	upsertBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		const form = await superValidate(request, zod(budgetItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Check if current stage is construction stage - don't allow updates during construction
		const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
			project_id_param: form.data.project_id,
		});

		if (isConstructionStage) {
			return message(
				form,
				{
					type: 'error',
					text: 'Budget modifications are not allowed during the construction stage',
				},
				{ status: 403 },
			);
		}

		// Update the budget line item
		try {
			const id = await upsertBudgetLineItem(supabase, form.data);
			locals.log.info({ msg: 'Budget line item upserted', id });
			return message(form, { type: 'success', text: 'Budget line item saved successfully' });
		} catch (error) {
			locals.log.error({ msg: 'Error upserting budget line item', error });
			return message(
				form,
				{ type: 'error', text: 'Error saving budget line item' },
				{ status: 500 },
			);
		}
	},

	// Delete a budget line item
	deleteBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		const form = await superValidate(request, zod(budgetItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { project_id: projectId, budget_line_item_id: budgetLineItemId } = form.data;

		if (!projectId || !budgetLineItemId) {
			return fail(400, { success: false, message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, {
				success: false,
				message: 'You do not have permission to edit this project',
			});
		}

		// Check if current stage is construction stage - don't allow deletions during construction
		const { data: isConstructionStage } = await supabase.rpc('is_construction_stage', {
			project_id_param: projectId,
		});

		if (isConstructionStage) {
			return fail(403, {
				success: false,
				message: 'Budget modifications are not allowed during the construction stage',
			});
		}

		// Delete the specific budget line item by its ID (version item id)
		try {
			const { error } = await supabase
				.from('budget_version_item')
				.delete()
				.eq('budget_version_item_id', budgetLineItemId);

			if (error) throw error;

			return {
				success: true,
				message: { type: 'success', text: 'Budget line item deleted successfully' },
			};
		} catch (error) {
			locals.log.error({ msg: 'Error deleting budget line item', error });
			return fail(500, { success: false, message: 'Error deleting budget line item' });
		}
	},
};
