<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { Calendar } from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import CalendarIcon from 'phosphor-svelte/lib/Calendar';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { editTenderSchema } from '$lib/schemas/tender';
	import {
		DateFormatter,
		getLocalTimeZone,
		parseDate,
		today,
		type DateValue,
	} from '@internationalized/date';
	import { cn } from '$lib/utils';

	const { data }: PageProps = $props();

	const form = superForm(data.form, {
		validators: zodClient(editTenderSchema),
	});

	const { form: formData, enhance } = form;

	const df = new DateFormatter('en-US', {
		dateStyle: 'long',
	});

	let calendarOpen = $state(false);
	let submissionDateValue = $derived(
		$formData.submission_date ? parseDate($formData.submission_date) : undefined,
	);
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));

	// Convert vendors to select options
	const vendorOptions = $derived(
		data.vendors.map((vendor) => ({
			value: vendor.vendor_id,
			label: vendor.name,
		})),
	);

	// Convert currencies to select options
	const currencyOptions = $derived(
		data.currencies.map((currency) => ({
			value: currency.currency_code,
			label: `${currency.currency_code} (${currency.symbol})`,
		})),
	);

	// Status options
	const statusOptions = [
		{ value: 'submitted', label: 'Submitted' },
		{ value: 'under_review', label: 'Under Review' },
		{ value: 'selected', label: 'Selected' },
		{ value: 'rejected', label: 'Rejected' },
	];
</script>

<svelte:head>
	<title>Edit {data.tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:px-6">
	<div>
		<h1 class="text-2xl font-bold tracking-tight">Edit Tender</h1>
		<p class="text-muted-foreground">
			Update tender details for {data.tender?.tender_name}
		</p>
	</div>

	<form method="POST" use:enhance class="space-y-6">
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
			<Form.Field {form} name="tender_name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Tender Name</Form.Label>
						<Input {...props} bind:value={$formData.tender_name} placeholder="Enter tender name" />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="vendor_id">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Vendor <span class="text-red-500">*</span></Form.Label>
						<Select.Root type="single" bind:value={$formData.vendor_id} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.vendor_id
									? vendorOptions.find((v) => v.value === $formData.vendor_id)?.label
									: 'Select a vendor'}
							</Select.Trigger>
							<Select.Content>
								{#each vendorOptions as vendor (vendor.value)}
									<Select.Item value={vendor.value} label={vendor.label}>
										{vendor.label}
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
						<input hidden bind:value={$formData.vendor_id} name={props.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="submission_date">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Submission Date</Form.Label>
						<Popover.Root bind:open={calendarOpen}>
							<Popover.Trigger>
								{#snippet child({ props: triggerProps })}
									<Button
										{...props}
										{...triggerProps}
										variant="outline"
										class={cn(
											'w-full justify-start text-left font-normal',
											!submissionDateValue && 'text-muted-foreground',
										)}
									>
										<CalendarIcon class="mr-2 h-4 w-4" />
										{submissionDateValue
											? df.format(submissionDateValue.toDate(getLocalTimeZone()))
											: 'Pick a date'}
									</Button>
								{/snippet}
							</Popover.Trigger>
							<Popover.Content class="w-auto p-0" align="start">
								<Calendar
									type="single"
									value={submissionDateValue as DateValue}
									bind:placeholder
									captionLayout="dropdown"
									onValueChange={(v) => {
										if (v) {
											$formData.submission_date = v.toString();
										} else {
											$formData.submission_date = '';
										}
										calendarOpen = false;
									}}
								/>
							</Popover.Content>
						</Popover.Root>
						<input hidden bind:value={$formData.submission_date} name={props.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="currency_code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Currency</Form.Label>
						<Select.Root type="single" bind:value={$formData.currency_code} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.currency_code
									? currencyOptions.find((c) => c.value === $formData.currency_code)?.label
									: 'Select currency'}
							</Select.Trigger>
							<Select.Content>
								{#each currencyOptions as currency (currency.value)}
									<Select.Item value={currency.value} label={currency.label}>
										{currency.label}
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
						<input hidden bind:value={$formData.currency_code} name={props.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="status">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Status</Form.Label>
						<Select.Root type="single" bind:value={$formData.status} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.status
									? statusOptions.find((s) => s.value === $formData.status)?.label
									: 'Select status'}
							</Select.Trigger>
							<Select.Content>
								{#each statusOptions as status (status.value)}
									<Select.Item value={status.value} label={status.label}>
										{status.label}
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
						<input hidden bind:value={$formData.status} name={props.name} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</div>

		<Form.Field {form} name="description">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Description</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.description}
						placeholder="Enter tender description"
						rows={3}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors />
		</Form.Field>

		<Form.Field {form} name="notes">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Notes</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.notes}
						placeholder="Enter any additional notes"
						rows={3}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors />
		</Form.Field>

		<div class="flex justify-end space-x-4">
			<Button
				type="button"
				variant="outline"
				href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
					data.client_name,
				)}/projects/{encodeURIComponent(data.project_id_short)}/tenders/{encodeURIComponent(
					data.tender_id_short,
				)}"
			>
				Cancel
			</Button>
			<Button type="submit">Update Tender</Button>
		</div>
	</form>
</div>
