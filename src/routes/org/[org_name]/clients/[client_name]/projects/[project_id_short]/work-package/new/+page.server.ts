import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { workPackageSchema } from '$lib/schemas/work_package';
import { purchaseOrderModalSchema } from '$lib/schemas/purchase_order';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import { createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { createPurchaseOrderModal } from '$lib/components/forms/purchase_order/purchase_order_form_actions';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: (await requireUser()).user.id,
		entity_type_param: 'project',
		entity_id_param: project_id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors:', vendorsError });
		throw error(500, 'Failed to fetch vendors');
	}

	const form = await superValidate(zod(workPackageSchema));
	const newPurchaseOrderForm = await superValidate(zod(purchaseOrderModalSchema));
	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newPurchaseOrderForm,
		newVendorForm,
		vendors: vendors || [],
	};
};

export const actions: Actions = {
	createVendorModal,
	createPurchaseOrderModal,
	createWorkPackage: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const form = await superValidate(request, zod(workPackageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const project_id = projectUUID(project_id_short);
		// Create the work package
		const { data: workPackage, error: workPackageError } = await supabase
			.from('work_package')
			.insert({
				...form.data,
				project_id: project_id,
			})
			.select('work_package_id, name')
			.single();

		if (workPackageError) {
			locals.log.error({ msg: 'Error creating work package:', workPackageError });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_id_short)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackage.name}" created successfully`,
			},
			cookies,
		);
	},
};
