import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import type {
	CostDetailBudgetItem,
	CostDetailWorkPackageData,
	CostDetailPurchaseOrderData,
} from '$lib/schemas/cost_detail';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch cost detail data using the RPC function
	const { data: costDetailData, error: costDetailError } = await supabase.rpc(
		'get_cost_detail_data',
		{
			project_id_param: project_id,
		},
	);

	if (costDetailError) {
		locals.log.error({ msg: 'Error fetching cost detail data:', costDetailError });
		throw error(500, 'Failed to fetch cost detail data');
	}

	// Transform cost detail data to match BudgetLineItemBase interface
	const transformedCostDetailData: CostDetailBudgetItem[] = (costDetailData || []).map((item) => ({
		wbs_library_item_id: item.wbs_library_item_id,
		quantity: item.quantity,
		unit_rate: item.unit_rate,
		factor: item.factor,
		wbs_code: item.wbs_code,
		wbs_description: item.wbs_description,
		wbs_level: item.wbs_level,
		parent_item_id: item.parent_item_id,
		budget_amount: item.budget_amount,
		work_packages: Array.isArray(item.work_packages)
			? (item.work_packages as unknown as CostDetailWorkPackageData[])
			: [],
		purchase_orders: Array.isArray(item.purchase_orders)
			? (item.purchase_orders as unknown as CostDetailPurchaseOrderData[])
			: [],
	}));

	return {
		costDetailData: transformedCostDetailData,
	};
};
