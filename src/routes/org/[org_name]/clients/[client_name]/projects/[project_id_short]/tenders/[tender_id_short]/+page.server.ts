import type { PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import { getTenderById } from '$lib/tender_utils';

export const load: PageServerLoad = async ({ locals, params }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();
	const { tender_id_short } = params;

	if (!tender_id_short) {
		throw error(400, 'Tender ID is required');
	}

	const project_id = projectUUID(project_id_short);
	const tender_id = tenderUUID(tender_id_short);

	// Fetch tender details with all related data
	const tender = await getTenderById(supabase, tender_id);

	// Verify the tender belongs to the project
	if (tender.project_id !== project_id) {
		throw error(404, 'Tender not found');
	}

	return {
		tender,
		tender_id_short,
	};
};
