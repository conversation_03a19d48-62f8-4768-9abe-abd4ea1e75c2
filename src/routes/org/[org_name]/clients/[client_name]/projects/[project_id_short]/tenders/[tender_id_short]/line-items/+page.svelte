<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import EditIcon from 'phosphor-svelte/lib/PencilSimple';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import { enhance } from '$app/forms';
	import ConfirmationDialog from '$lib/components/tender/ConfirmationDialog.svelte';
	import NormalizationInput from '$lib/components/tender/NormalizationInput.svelte';
	import { formatCurrency } from '$lib/utils';
	const { data }: PageProps = $props();

	const tender = $derived(data.tender);
	const lineItems = $derived(tender?.tender_revision?.[0]?.tender_line_item || []);

	let addDialogOpen = $state(false);
	let _editDialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let selectedLineItem = $state<(typeof lineItems)[0] | null>(null);

	// Form state for new/edit line item
	let formData = $state({
		line_number: '',
		description: '',
		quantity: '',
		unit: '',
		material_rate: '',
		labor_rate: '',
		productivity: '',
		unit_rate: '',
		subtotal: '',
		normalization_type: 'amount' as 'amount' | 'percentage',
		normalization_amount: '',
		normalization_percentage: '',
		notes: '',
	});

	function resetForm() {
		formData = {
			line_number: '',
			description: '',
			quantity: '',
			unit: '',
			material_rate: '',
			labor_rate: '',
			productivity: '',
			unit_rate: '',
			subtotal: '',
			normalization_type: 'amount' as 'amount' | 'percentage',
			normalization_amount: '',
			normalization_percentage: '',
			notes: '',
		};
	}

	function openAddDialog() {
		resetForm();
		// Set next line number
		const maxLineNumber = Math.max(...lineItems.map((item) => item.line_number), 0);
		formData.line_number = (maxLineNumber + 1).toString();
		addDialogOpen = true;
	}

	function openEditDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		formData = {
			line_number: lineItem.line_number.toString(),
			description: lineItem.description || '',
			quantity: lineItem.quantity?.toString() || '',
			unit: lineItem.unit || '',
			material_rate: lineItem.material_rate?.toString() || '',
			labor_rate: lineItem.labor_rate?.toString() || '',
			productivity: lineItem.productivity_factor?.toString() || '',
			unit_rate: lineItem.unit_rate?.toString() || '',
			subtotal: lineItem.subtotal?.toString() || '',
			normalization_type: (lineItem.normalization_type || 'amount') as 'amount' | 'percentage',
			normalization_amount: lineItem.normalization_amount?.toString() || '',
			normalization_percentage: lineItem.normalization_percentage?.toString() || '',
			notes: lineItem.notes || '',
		};
		_editDialogOpen = true;
	}

	function openDeleteDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		deleteDialogOpen = true;
	}

	// Calculate subtotal when quantity and unit_rate change
	function calculateSubtotal() {
		const quantity = parseFloat(formData.quantity) || 0;
		const unitRate = parseFloat(formData.unit_rate) || 0;
		if (quantity > 0 && unitRate > 0) {
			formData.subtotal = (quantity * unitRate).toString();
		}
	}

	// Calculate unit rate from material rate, labor rate, and productivity
	function calculateUnitRate() {
		const materialRate = parseFloat(formData.material_rate) || 0;
		const laborRate = parseFloat(formData.labor_rate) || 0;
		const productivity = parseFloat(formData.productivity) || 1;

		if (materialRate > 0 || laborRate > 0) {
			const calculatedRate = materialRate + laborRate / productivity;
			formData.unit_rate = calculatedRate.toString();
			calculateSubtotal();
		}
	}
</script>

<svelte:head>
	<title>Line Items - {tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold tracking-tight">Line Items</h1>
			<p class="text-muted-foreground">
				Manage line items for {tender?.tender_name}
			</p>
		</div>
		<div class="flex items-center space-x-2">
			<Button onclick={openAddDialog}>
				<PlusIcon class="mr-2 h-4 w-4" />
				Add Line Item
			</Button>
		</div>
	</div>

	<!-- Summary Card -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Summary</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-3 gap-4">
				<div>
					<label class="text-sm font-medium text-gray-500">Total Line Items</label>
					<p class="text-2xl font-bold">{lineItems.length}</p>
				</div>
				<div>
					<label class="text-sm font-medium text-gray-500">Total Amount</label>
					<p class="text-2xl font-bold">
						{formatCurrency(
							lineItems.reduce((sum, item) => sum + (item.subtotal || 0), 0),
							{
								symbol: tender?.vendor?.currency ?? '$',
								fallback: '-',
							},
						)}
					</p>
				</div>
				<div>
					<label class="text-sm font-medium text-gray-500">WBS Mappings</label>
					<p class="text-2xl font-bold">
						{lineItems.reduce(
							(sum: number, item) => sum + (item.tender_wbs_mapping?.length || 0),
							0,
						)}
					</p>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Line Items Table -->
	{#if lineItems.length > 0}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Line #</TableHead>
						<TableHead>Description</TableHead>
						<TableHead>Quantity</TableHead>
						<TableHead>Unit</TableHead>
						<TableHead>Unit Rate</TableHead>
						<TableHead>Subtotal</TableHead>
						<TableHead>Normalization</TableHead>
						<TableHead>WBS</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each lineItems as item (item.tender_line_item_id)}
						<TableRow>
							<TableCell>{item.line_number}</TableCell>
							<TableCell class="max-w-xs">
								<div class="truncate" title={item.description}>
									{item.description}
								</div>
							</TableCell>
							<TableCell>{item.quantity || '-'}</TableCell>
							<TableCell>{item.unit || '-'}</TableCell>
							<TableCell>
								{item.unit_rate
									? formatCurrency(item.unit_rate, {
											symbol: tender?.vendor?.currency ?? '$',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{item.subtotal
									? formatCurrency(item.subtotal, {
											symbol: tender?.vendor?.currency ?? '$',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{#if item.normalization_type === 'percentage'}
									{item.normalization_percentage}%
								{:else if item.normalization_amount}
									{formatCurrency(item.normalization_amount, {
										symbol: tender?.vendor?.currency ?? '$',
										fallback: '-',
									})}
								{:else}
									-
								{/if}
							</TableCell>
							<TableCell>
								{item.tender_wbs_mapping?.length || 0}
							</TableCell>
							<TableCell class="text-right">
								<div class="flex items-center justify-end space-x-2">
									<Button size="sm" variant="ghost" onclick={() => openEditDialog(item)}>
										<EditIcon class="h-4 w-4" />
									</Button>
									<Button size="sm" variant="ghost" onclick={() => openDeleteDialog(item)}>
										<TrashIcon class="h-4 w-4" />
									</Button>
								</div>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="py-12 text-center">
			<h3 class="mt-2 text-sm font-semibold text-gray-900">No line items</h3>
			<p class="mt-1 text-sm text-gray-500">Get started by adding your first line item.</p>
			<div class="mt-6">
				<Button onclick={openAddDialog}>
					<PlusIcon class="mr-2 h-4 w-4" />
					Add Line Item
				</Button>
			</div>
		</div>
	{/if}
</div>

<!-- Add Line Item Dialog -->
<Dialog.Root bind:open={addDialogOpen}>
	<Dialog.Content class="max-w-4xl">
		<Dialog.Header>
			<Dialog.Title>Add Line Item</Dialog.Title>
			<Dialog.Description>Add a new line item to this tender revision.</Dialog.Description>
		</Dialog.Header>
		<form method="POST" action="?/create" use:enhance>
			<div class="grid grid-cols-2 gap-4 py-4">
				<div>
					<label class="text-sm font-medium">Line Number</label>
					<Input bind:value={formData.line_number} name="line_number" type="number" required />
				</div>
				<div>
					<label class="text-sm font-medium">Unit</label>
					<Input bind:value={formData.unit} name="unit" placeholder="e.g., m², kg, hours" />
				</div>
				<div class="col-span-2">
					<label class="text-sm font-medium">Description</label>
					<Textarea bind:value={formData.description} name="description" required />
				</div>
				<div>
					<label class="text-sm font-medium">Quantity</label>
					<Input
						bind:value={formData.quantity}
						name="quantity"
						type="number"
						step="0.0001"
						onchange={calculateSubtotal}
					/>
				</div>
				<div>
					<label class="text-sm font-medium">Unit Rate</label>
					<Input
						bind:value={formData.unit_rate}
						name="unit_rate"
						type="number"
						step="0.01"
						onchange={calculateSubtotal}
					/>
				</div>
				<div>
					<label class="text-sm font-medium">Material Rate</label>
					<Input
						bind:value={formData.material_rate}
						name="material_rate"
						type="number"
						step="0.01"
						onchange={calculateUnitRate}
					/>
				</div>
				<div>
					<label class="text-sm font-medium">Labor Rate</label>
					<Input
						bind:value={formData.labor_rate}
						name="labor_rate"
						type="number"
						step="0.01"
						onchange={calculateUnitRate}
					/>
				</div>
				<div>
					<label class="text-sm font-medium">Productivity</label>
					<Input
						bind:value={formData.productivity}
						name="productivity"
						type="number"
						step="0.01"
						onchange={calculateUnitRate}
					/>
				</div>
				<div>
					<label class="text-sm font-medium">Subtotal</label>
					<Input bind:value={formData.subtotal} name="subtotal" type="number" step="0.01" />
				</div>
			</div>

			<!-- Normalization Input -->
			<div class="space-y-2">
				<label class="text-sm font-medium">Normalization</label>
				<NormalizationInput
					bind:type={formData.normalization_type}
					amount={formData.normalization_amount}
					percentage={formData.normalization_percentage}
					subtotal={parseFloat(formData.subtotal) || 0}
					onTypeChange={(type) => (formData.normalization_type = type)}
					onAmountChange={(amount) => (formData.normalization_amount = amount)}
					onPercentageChange={(percentage) => (formData.normalization_percentage = percentage)}
				/>
			</div>
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (addDialogOpen = false)}>
					Cancel
				</Button>
				<Button type="submit">Add Line Item</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<ConfirmationDialog
	open={deleteDialogOpen}
	title="Delete Line Item"
	description="Are you sure you want to delete this line item? This action cannot be undone."
	confirmText="Delete"
	variant="destructive"
	onConfirm={() => {
		if (selectedLineItem) {
			// Handle delete action here
			console.log('Deleting line item:', selectedLineItem.tender_line_item_id);
		}
		deleteDialogOpen = false;
		selectedLineItem = null;
	}}
	onCancel={() => {
		deleteDialogOpen = false;
		selectedLineItem = null;
	}}
/>
