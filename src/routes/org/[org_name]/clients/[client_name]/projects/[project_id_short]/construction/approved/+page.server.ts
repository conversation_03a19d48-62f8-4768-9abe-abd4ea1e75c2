import { requireProject } from '$lib/server/auth';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { riskFilterSchema } from '$lib/schemas/risk';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, url, depends }) => {
	depends('project:approved-changes');

	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get filter parameters from URL
	const dateFromFilter = url.searchParams.get('date_from') || undefined;
	const dateToFilter = url.searchParams.get('date_to') || undefined;
	const wbsItemFilter = url.searchParams.get('wbs_item') || undefined;

	// Build query for approved changes from risk register
	let approvedChangesQuery = supabase
		.from('risk_register')
		.select(
			`
				*,
				wbs_item:wbs_library_item(wbs_library_item_id, code, description),
				risk_owner:profile!risk_owner_user_id(full_name, email)
      `,
		)
		.eq('project_id', project_id)
		.eq('status', 'approved')
		.order('date_identified', { ascending: false });

	if (dateFromFilter) {
		approvedChangesQuery = approvedChangesQuery.gte('date_identified', dateFromFilter);
	}

	if (dateToFilter) {
		approvedChangesQuery = approvedChangesQuery.lte('date_identified', dateToFilter);
	}

	// Fetch approved changes and budget data in parallel (WBS items provided by layout)
	const [{ data: approvedChanges, error: approvedChangesError }, { data: budgetData }] =
		await Promise.all([
			approvedChangesQuery,
			supabase.rpc('get_active_budget_version_items', { p_project_id: project_id }),
		]);

	if (approvedChangesError) {
		locals.log.error({ msg: 'Error fetching approved changes', approvedChangesError });
	}

	// WBS filtering is handled client-side using layout-provided wbsItems

	// Create a map of WBS item to total budget amount
	const budgetMap = new Map<string, number>();
	if (budgetData) {
		budgetData.forEach((item) => {
			const currentTotal = budgetMap.get(item.wbs_library_item_id) || 0;
			budgetMap.set(item.wbs_library_item_id, currentTotal + item.quantity * item.unit_rate);
		});
	}

	// Create the filter form
	const filterSchema = riskFilterSchema.pick({
		date_from: true,
		date_to: true,
		wbs_library_item_id: true,
	});

	const filterForm = await superValidate(
		{
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_library_item_id: wbsItemFilter,
		},
		zod(filterSchema),
	);

	return {
		title: `Approved Changes`,
		approvedChanges: approvedChanges || [],
		// wbsItems provided by layout
		budgetMap: Object.fromEntries(budgetMap),
		filterForm,
		filters: {
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_item: wbsItemFilter,
		},
	};
};

export const actions: Actions = {
	// Apply filters
	applyFilters: async ({ request }) => {
		const filterSchema = riskFilterSchema.pick({
			date_from: true,
			date_to: true,
			wbs_library_item_id: true,
		});
		const form = await superValidate(request, zod(filterSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Return the form data to be used for URL parameters
		return { filterForm: form };
	},
};
