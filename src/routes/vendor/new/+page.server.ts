import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	vendorSchema,
	processVendorCreationHierarchy,
	type VendorCreationHierarchyItem,
} from '$lib/schemas/vendor';
import { requireUser } from '$lib/server/auth';
import { createVendor } from '$lib/components/forms/vendor/vendor_form_actions';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;

	// Get vendor creation hierarchy using the optimized RPC function
	const { data: hierarchyData, error: hierarchyError } = await supabase.rpc(
		'get_vendor_creation_hierarchy',
	);

	if (hierarchyError) {
		locals.log.error({ msg: 'Error fetching vendor creation hierarchy:', hierarchyError });
		throw new Error('Failed to fetch vendor creation hierarchy');
	}

	// Process the hierarchy data into UI-friendly format
	const { organizations, clients, projects } = processVendorCreationHierarchy(
		(hierarchyData as VendorCreationHierarchyItem[]) || [],
	);

	const { data: currencies, error: currenciesError } = await supabase
		.from('currency')
		.select('currency_code, description, symbol, symbol_position')
		.order('currency_code');

	if (currenciesError) {
		locals.log.error({ msg: 'Error fetching currencies:', currenciesError });
		throw new Error('Failed to fetch currency options');
	}

	const form = await superValidate(zod(vendorSchema));

	return {
		title: 'New Vendor',
		form,
		organizations,
		clients,
		projects,
		currencies: currencies || [],
	};
};

export const actions: Actions = {
	createVendor,
};
