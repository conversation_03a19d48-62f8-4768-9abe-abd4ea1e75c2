<script lang="ts">
	import type { PageProps } from './$types';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { toast } from 'svelte-sonner';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';
	import {
		DEFAULT_CURRENCY_CODE,
		editVendorSchema,
		vendorTypes,
		paymentTermsOptions,
		type CurrencyCode,
	} from '$lib/schemas/vendor';
	import type { Tables } from '$lib/database.types';

	// Type definitions for the joined data structures
	type OrganizationData = {
		org_id: string;
		name: string;
	};

	type ClientWithOrganization = {
		client_id: string;
		name: string;
		organization?: OrganizationData;
	};

	type ProjectWithClient = {
		project_id: string;
		name: string;
		client?: {
			client_id: string;
			name: string;
			organization?: OrganizationData;
		};
	};

	const { data }: PageProps = $props();

	const vendor = $derived(data.vendor);
	const organizations = $derived(data.organizations);
	const clients = $derived(data.clients);
	const projects = $derived(data.projects);
	const currencies = $derived(
		data.currencies as Pick<
			Tables<'currency'>,
			'currency_code' | 'description' | 'symbol' | 'symbol_position'
		>[],
	);

	const form = superForm(data.form, {
		validators: zodClient(editVendorSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance, delayed, submitting } = form;

	// Reactive variables for hierarchical selection
	let selectedEntityType = $derived<'organization' | 'client' | 'project'>(
		vendor.org_id ? 'organization' : vendor.client_id ? 'client' : 'project',
	);
	let selectedOrganization = $derived<string>(vendor.org_id || '');
	let selectedClient = $derived<string>(vendor.client_id || '');
	let selectedProject = $derived<string>(vendor.project_id || '');

	// Local variables for select components that need string values
	let vendorTypeValue = $derived<(typeof vendorTypes)[number] | ''>(
		(vendor.vendor_type as (typeof vendorTypes)[number]) || '',
	);
	let currencyValue = $derived<CurrencyCode>(vendor.currency || DEFAULT_CURRENCY_CODE);
	let paymentTermsValue = $derived<(typeof paymentTermsOptions)[number] | ''>(
		(vendor.payment_terms as (typeof paymentTermsOptions)[number]) || '',
	);

	// Local variables for JSON fields displayed as text
	let certificationInfoText = $derived<string>(
		vendor.certification_info ? JSON.stringify(vendor.certification_info, null, 2) : '',
	);
	let insuranceInfoText = $derived<string>(
		vendor.insurance_info ? JSON.stringify(vendor.insurance_info, null, 2) : '',
	);

	// Update form data when selections change
	$effect(() => {
		// Clear all entity IDs first
		$formData.org_id = null;
		$formData.client_id = null;
		$formData.project_id = null;

		// Set the appropriate entity ID based on selection
		if (selectedEntityType === 'organization' && selectedOrganization) {
			$formData.org_id = selectedOrganization;
		} else if (selectedEntityType === 'client' && selectedClient) {
			$formData.client_id = selectedClient;
		} else if (selectedEntityType === 'project' && selectedProject) {
			$formData.project_id = selectedProject;
		}
	});

	// Sync local select values with form data
	$effect(() => {
		$formData.vendor_type = vendorTypeValue || null;
	});

	$effect(() => {
		$formData.currency = currencyValue;
	});

	$effect(() => {
		$formData.payment_terms = paymentTermsValue || null;
	});

	// Sync JSON text fields with form data
	$effect(() => {
		try {
			$formData.certification_info = certificationInfoText
				? JSON.parse(certificationInfoText)
				: null;
		} catch {
			// Keep the original value if JSON is invalid
		}
	});

	$effect(() => {
		try {
			$formData.insurance_info = insuranceInfoText ? JSON.parse(insuranceInfoText) : null;
		} catch {
			// Keep the original value if JSON is invalid
		}
	});

	// Filter clients based on selected organization
	function filteredClients() {
		if (!selectedOrganization) return clients;
		return clients.filter((c) => {
			const clientData = c.client as ClientWithOrganization;
			return clientData?.organization?.org_id === selectedOrganization;
		});
	}

	// Filter projects based on selected client
	function filteredProjects() {
		if (!selectedClient) return projects;
		return projects.filter((p) => {
			const projectData = p.project as ProjectWithClient;
			return projectData?.client?.client_id === selectedClient;
		});
	}
</script>

<div class="container mx-auto max-w-4xl py-8">
	<div class="mb-6">
		<h1 class="text-2xl font-semibold">Edit Vendor</h1>
		<p class="text-muted-foreground">Update vendor information and settings</p>
	</div>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Basic Information</h2>
					<input type="hidden" name="vendor_id" bind:value={$formData.vendor_id} />

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="name">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Vendor Name <span class="text-red-500">*</span></Form.Label>
									<Input {...props} bind:value={$formData.name} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="vendor_type">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Vendor Type</Form.Label>
									<Select.Root type="single" bind:value={vendorTypeValue} name={props.name}>
										<Select.Trigger {...props}>
											{vendorTypeValue || 'Select vendor type'}
										</Select.Trigger>
										<Select.Content>
											{#each vendorTypes as type (type)}
												<Select.Item value={type}>{type}</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea {...props} bind:value={$formData.description} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Access Level Selection -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Access Level</h2>
					<p class="text-muted-foreground text-sm">
						Choose the access level for this vendor. Organization-level vendors are accessible
						across all clients and projects.
					</p>

					<!-- Entity Type Selection -->
					<div class="space-y-2">
						<span class="text-sm font-medium">Vendor Access Level</span>
						<div class="flex gap-4">
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="organization"
									class="text-primary"
								/>
								<span>Organization Level</span>
							</label>
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="client"
									class="text-primary"
								/>
								<span>Client Level</span>
							</label>
							<label class="flex items-center space-x-2">
								<input
									type="radio"
									bind:group={selectedEntityType}
									value="project"
									class="text-primary"
								/>
								<span>Project Level</span>
							</label>
						</div>
					</div>

					<!-- Organization Selection -->
					{#if selectedEntityType === 'organization'}
						<Form.Field {form} name="org_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Organization <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedOrganization} name={props.name}>
										<Select.Trigger {...props}>
											{selectedOrganization
												? organizations.find((o) => o.organization?.org_id === selectedOrganization)
														?.organization?.name
												: 'Select organization'}
										</Select.Trigger>
										<Select.Content>
											{#each organizations as org (org.organization?.org_id)}
												{#if org.organization?.org_id}
													<Select.Item value={org.organization.org_id}>
														{org.organization.name}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}

					<!-- Client Selection -->
					{#if selectedEntityType === 'client'}
						<!-- Organization selection for filtering -->
						<div>
							<label for="org-filter" class="text-sm font-medium">Filter by Organization</label>
							<Select.Root
								type="single"
								bind:value={selectedOrganization}
								onValueChange={() => {
									selectedClient = ''; // Reset client selection
								}}
							>
								<input type="hidden" id="org-filter" />
								<Select.Trigger>
									{selectedOrganization
										? organizations.find((o) => o.organization?.org_id === selectedOrganization)
												?.organization?.name
										: 'All organizations'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All organizations</Select.Item>
									{#each organizations as org (org.organization?.org_id)}
										{#if org.organization?.org_id}
											<Select.Item value={org.organization.org_id}>
												{org.organization.name}
											</Select.Item>
										{/if}
									{/each}
								</Select.Content>
							</Select.Root>
						</div>

						<Form.Field {form} name="client_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Client <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedClient} name={props.name}>
										<Select.Trigger {...props}>
											{selectedClient
												? filteredClients().find((c) => c.client?.client_id === selectedClient)
														?.client?.name
												: 'Select client'}
										</Select.Trigger>
										<Select.Content>
											{#each filteredClients() as client (client.client?.client_id)}
												{#if client.client?.client_id}
													<Select.Item value={client.client.client_id}>
														{client.client.name}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}

					<!-- Project Selection -->
					{#if selectedEntityType === 'project'}
						<!-- Client selection for filtering -->
						<div>
							<label for="client-filter" class="text-sm font-medium">Filter by Client</label>
							<Select.Root
								type="single"
								bind:value={selectedClient}
								onValueChange={() => {
									selectedProject = ''; // Reset project selection
								}}
							>
								<input type="hidden" id="client-filter" />
								<Select.Trigger>
									{selectedClient
										? clients.find((c) => c.client?.client_id === selectedClient)?.client?.name
										: 'All clients'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All clients</Select.Item>
									{#each clients as client (client.client?.client_id)}
										{#if client.client?.client_id}
											<Select.Item value={client.client.client_id}>
												{client.client.name}
											</Select.Item>
										{/if}
									{/each}
								</Select.Content>
							</Select.Root>
						</div>

						<Form.Field {form} name="project_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Project <span class="text-red-500">*</span></Form.Label>
									<Select.Root type="single" bind:value={selectedProject} name={props.name}>
										<Select.Trigger {...props}>
											{selectedProject
												? filteredProjects().find((p) => p.project?.project_id === selectedProject)
														?.project?.name
												: 'Select project'}
										</Select.Trigger>
										<Select.Content>
											{#each filteredProjects() as project (project.project?.project_id)}
												{#if project.project?.project_id}
													<Select.Item value={project.project.project_id}>
														{project.project.name}
													</Select.Item>
												{/if}
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					{/if}
				</div>

				<!-- Contact Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Contact Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="contact_name">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Name</Form.Label>
									<Input {...props} bind:value={$formData.contact_name} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="contact_email">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Email</Form.Label>
									<Input {...props} type="email" bind:value={$formData.contact_email} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="contact_phone">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Contact Phone</Form.Label>
									<Input {...props} bind:value={$formData.contact_phone} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="website">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Website</Form.Label>
									<Input {...props} type="url" bind:value={$formData.website} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="contact_address">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Contact Address</Form.Label>
								<Textarea {...props} bind:value={$formData.contact_address} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Financial Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Financial Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
						<Form.Field {form} name="currency">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Currency</Form.Label>
									<Select.Root type="single" bind:value={currencyValue} name={props.name}>
										<Select.Trigger {...props}>
											{currencyValue}
										</Select.Trigger>
										<Select.Content>
											{#each currencies as currency (currency.currency_code)}
												<Select.Item value={currency.currency_code}>
													{currency.currency_code}
													{#if currency.description}
														— {currency.description}
													{/if}
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="payment_terms">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Payment Terms</Form.Label>
									<Select.Root type="single" bind:value={paymentTermsValue} name={props.name}>
										<Select.Trigger {...props}>
											{paymentTermsValue || 'Select payment terms'}
										</Select.Trigger>
										<Select.Content>
											{#each paymentTermsOptions as terms (terms)}
												<Select.Item value={terms}>{terms}</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="payment_terms_days">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Payment Terms (Days)</Form.Label>
									<Input {...props} type="number" bind:value={$formData.payment_terms_days} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="credit_limit">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Credit Limit</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.credit_limit} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="tax_id">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Tax ID</Form.Label>
									<Input {...props} bind:value={$formData.tax_id} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				<!-- Additional Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Additional Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="certification_info">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Certification Information</Form.Label>
									<Textarea {...props} bind:value={certificationInfoText} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="insurance_info">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Insurance Information</Form.Label>
									<Textarea {...props} bind:value={insuranceInfoText} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="is_active">
						<Form.Control>
							{#snippet children({ props })}
								<div class="flex items-center space-x-2">
									<Checkbox {...props} bind:checked={$formData.is_active} />
									<Form.Label>Active Vendor</Form.Label>
								</div>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Form Actions -->
				<div class="flex justify-end space-x-2">
					<Button type="button" variant="outline" onclick={() => history.back()}>Cancel</Button>
					<Button type="submit" disabled={$submitting}>
						{#if $delayed}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
						Update Vendor
					</Button>
				</div>
			</div>
		</form>
	</div>
</div>
