<script lang="ts">
	import ControlBudgetNode from '$lib/components/ControlBudgetNode.svelte';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { formatCurrency, formatPercentage } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';
	import { type ControlBudgetNode as ControlBudgetNodeType } from '$lib/budget_utils';

	let {
		node,
		indent,
		expanded,
		toggle,
		projectId,
		hideZeros,
		showRateCalculation,
	}: {
		node: ControlBudgetNodeType;
		indent: number;
		expanded: SvelteSet<string>;
		toggle: (nodeId: string) => void;
		projectId: string;
		hideZeros: boolean;
		showRateCalculation: boolean;
	} = $props();

	// true if this node is in the expanded set
	const isOpen = $derived(node.id && !expanded.has(node.id));

	// Called when the caret is clicked:
	function onToggle() {
		if (node.id) toggle(node.id);
	}
</script>

{#if !hideZeros || node.value || node.data.subtotal > 0}
	{@const controlBudget = node.data.subtotal}
	{@const forecastFinalCost =
		controlBudget +
		(node.data.riskData?.acceptedChanges ?? 0) +
		(node.data.riskData?.anticipatedChanges ?? 0)}
	{@const delta = forecastFinalCost - controlBudget}
	<!-- Show total cost for nodes with children or no budget data -->
	{#if (node.value !== node.data.subtotal && (node.children?.length || 0) > 0) || !node.data.budgetData}
		<tr
			class={[
				'group hover:bg-muted/20 border-b',
				(node.children?.length || 0) > 0 && 'font-medium',
			]}
		>
			<!-- WBS Code -->
			<td class="py-3 pr-2" style="padding-left: {0.5 + indent * 1}rem">
				<div class="flex items-center">
					{#if node.children?.length}
						<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
							{#if isOpen}
								<CaretDownIcon class="size-4" />
							{:else}
								<CaretRightIcon class="size-4" />
							{/if}
						</button>
						<span class="ml-1">{node.data.code}</span>
					{:else}
						<span class="ml-6">{node.data.code}</span>
					{/if}
				</div>
			</td>
			<!-- Description -->
			<td class="px-px py-1" style="padding-left: {0.5 + indent * 1}rem">
				{node.data.description}
			</td>
			{#if showRateCalculation}
				<!-- Quantity -->
				<td class="py-1 pr-1 pl-4 text-right"></td>
				<!-- Unit -->
				<td class="py-1 pr-1 pl-4"></td>
				<!-- Material Rate -->
				<td class="py-1 pr-1 pl-4 text-right"></td>
				<!-- Labor Rate -->
				<td class="py-1 pr-1 pl-4 text-right"></td>
				<!-- Productivity -->
				<td class="py-1 pr-1 pl-4 text-right"></td>
				<!-- Unit Rate -->
				<td class="text-muted-foreground py-1 pr-1 pl-4 text-right font-normal">
					{#if node.value && node.data.budgetData?.factor && node.data.budgetData?.factor !== 1}
						{formatCurrency(node.value / node.data.budgetData?.factor)}
					{/if}
				</td>
				<!-- Factor -->
				<td class="p-1 text-right">
					{node.data.budgetData?.factor && node.data.budgetData.factor !== 1
						? node.data.budgetData.factor
						: ''}
				</td>
			{/if}
			<!-- Capital Project Appropriation -->
			<td class="p-1 text-right">
				{formatCurrency(node.data.subtotal)}
			</td>
			<!-- WP Transfers -->
			<td class="p-1 text-right"></td>
			<!-- Client Change Orders -->
			<td class="p-1 text-right"></td>
			<!-- Control Budget -->
			<td class="p-1 text-right">
				{formatCurrency(node.data.subtotal)}
			</td>
			<!-- Accepted Tender -->
			<td class="p-1 text-right"></td>
			<!-- Works to Complete -->
			<td class="p-1 text-right"></td>
			<!-- Budget Unallocated -->
			<td class="p-1 text-right">
				<!-- subtotal for now, accepted tender - works to complete when they are ready -->
				{formatCurrency(node.data.subtotal)}
			</td>
			<!-- Accepted Changes -->
			<td class="p-1 text-right">
				{#if node.data.riskData?.acceptedChanges && node.data.riskData.acceptedChanges > 0}
					<!-- link to associated approved changes page -->
					<a href="construction/approved?wbs_item={node.data.wbs_library_item_id}">
						{formatCurrency(node.data.riskData.acceptedChanges)}
					</a>
				{/if}
			</td>
			<!-- Anticipated Changes -->
			<td class="p-1 text-right">
				{#if node.data.riskData?.anticipatedChanges && node.data.riskData.anticipatedChanges > 0}
					<!-- link to associated pending changes page -->
					<a href="construction/pending?wbs_item={node.data.wbs_library_item_id}">
						{formatCurrency(node.data.riskData.anticipatedChanges)}
					</a>
				{/if}
			</td>
			<!-- Forecast Final Cost -->
			<td class="p-1 text-right">
				{formatCurrency(forecastFinalCost)}
			</td>
			<!-- Delta to Control Budget -->
			<td class="p-1 text-right">
				{#if delta}
					<div class={['flex flex-col text-xs', delta > 0 ? 'text-red-500' : 'text-green-500']}>
						<div>
							{formatCurrency(delta)}
						</div>
						<div>
							{delta > 0 ? '+' : ''}{formatPercentage(delta / controlBudget)}
						</div>
					</div>
				{/if}
			</td>
			<!-- Advanced Warnings -->
			<td class="p-1 text-right">
				{#if node.data.riskData?.advancedWarnings && node.data.riskData.advancedWarnings > 0}
					<!-- link to associated risks page -->
					<a href="construction/risks?wbs_item={node.data.wbs_library_item_id}">
						{formatCurrency(node.data.riskData.advancedWarnings)}
					</a>
				{/if}
			</td>
		</tr>
	{/if}

	<!-- Display budget data if it exists -->
	{#if node.data.budgetData}
		{@const budgetData = node.data.budgetData}
		{#if budgetData.quantity || budgetData.unit || budgetData.unit_rate}
			<!-- Display row -->
			<tr class="group hover:bg-muted/20 relative border-b">
				<!-- WBS Code -->
				<td class="py-3 pr-2" style="padding-left: {0.5 + (indent - 1) * 1}rem">
					<div class="flex items-center">
						{#if node.children?.length && !hideZeros}
							<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
								{#if isOpen}
									<CaretDownIcon class="size-4" />
								{:else}
									<CaretRightIcon class="size-4" />
								{/if}
							</button>
							<span class="ml-2">{node.data.code}</span>
						{:else}
							<span class="ml-6">{node.data.code}</span>
						{/if}
					</div>
				</td>
				<!-- Description -->
				<td class="px-px py-1" style="padding-left: {0.5 + (indent - 1) * 1}rem">
					{node.data.description}
				</td>
				{#if showRateCalculation}
					<!-- Quantity -->
					<td class="p-1 text-right">
						{budgetData.quantity}
					</td>
					<!-- Unit -->
					<td class="px-px py-1">
						{budgetData.unit ?? ''}
					</td>
					<!-- Material Rate -->
					<td class="p-1 text-right">
						{#if !budgetData.unit_rate_manual_override}
							{formatCurrency(budgetData.material_rate)}
						{/if}
					</td>
					<!-- Labor Rate -->
					<td class="p-1 text-right">
						{#if !budgetData.unit_rate_manual_override}
							{formatCurrency(budgetData.labor_rate ?? 0)}
						{/if}
					</td>
					<!-- Productivity -->
					<td class="p-1 text-right">
						{#if !budgetData.unit_rate_manual_override}
							{formatCurrency(budgetData.productivity_per_hour ?? 0)}
						{/if}
					</td>
					<!-- Unit Rate -->
					<td class="p-1 text-right">
						{formatCurrency(budgetData.unit_rate)}
					</td>
					<!-- Factor -->
					<td class="p-1 text-right">
						{budgetData.factor ?? ''}
					</td>
				{/if}
				<!-- Capital Project Appropriation -->
				<td class="p-1 text-right">
					{formatCurrency(controlBudget)}
				</td>
				<!-- WP Transfers -->
				<td class="p-1 text-right"></td>
				<!-- Client Change Orders -->
				<td class="p-1 text-right"></td>
				<!-- Control Budget -->
				<td class="p-1 text-right">
					{formatCurrency(controlBudget)}
				</td>
				<!-- Accepted Tender -->
				<td class="p-1 text-right"></td>
				<!-- Works to Complete -->
				<td class="p-1 text-right"></td>
				<!-- Budget Unallocated -->
				<td class="p-1 text-right">
					<!-- controlBudget for now, accepted tender - works to complete when they are ready -->
					{formatCurrency(controlBudget)}
				</td>
				<!-- Accepted Changes -->
				<td class="p-1 text-right">
					{#if node.data.riskData?.acceptedChanges && node.data.riskData.acceptedChanges > 0}
						<!-- link to associated approved changes page -->
						<a href="construction/approved?wbs_item={node.data.wbs_library_item_id}">
							{formatCurrency(node.data.riskData.acceptedChanges)}
						</a>
					{/if}
				</td>
				<!-- Anticipated Changes -->
				<td class="p-1 text-right">
					{#if node.data.riskData?.anticipatedChanges && node.data.riskData.anticipatedChanges > 0}
						<!-- link to associated pending changes page -->
						<a href="construction/pending?wbs_item={node.data.wbs_library_item_id}">
							{formatCurrency(node.data.riskData.anticipatedChanges)}
						</a>
					{/if}
				</td>
				<!-- Forecast Final Cost -->
				<td class="p-1 text-right">
					{formatCurrency(
						node.data.subtotal +
							(node.data.riskData?.acceptedChanges ?? 0) +
							(node.data.riskData?.anticipatedChanges ?? 0),
					)}
				</td>
				<!-- Delta to Control Budget -->
				<td class="p-1 text-right">
					{#if delta}
						<div class={['flex flex-col text-xs', delta > 0 ? 'text-red-500' : 'text-green-500']}>
							<div>
								{formatCurrency(delta)}
							</div>
							<div>
								{delta > 0 ? '+' : ''}{formatPercentage(delta / controlBudget)}
							</div>
						</div>
					{/if}
				</td>
				<!-- Advanced Warnings -->
				<td class="p-1 text-right">
					{#if node.data.riskData?.advancedWarnings && node.data.riskData.advancedWarnings > 0}
						<!-- link to associated risks page -->
						<a href="construction/risks?wbs_item={node.data.wbs_library_item_id}">
							{formatCurrency(node.data.riskData.advancedWarnings)}
						</a>
					{/if}
				</td>
			</tr>
		{/if}
	{/if}

	<!-- Only render children when this node is open -->
	{#if isOpen && node.children}
		{#each node.children as child (child.id)}
			<ControlBudgetNode
				node={child}
				indent={indent + 1}
				{expanded}
				{toggle}
				{projectId}
				{hideZeros}
				{showRateCalculation}
			/>
		{/each}
	{/if}
{:else if node.id === '__unallocated_risks__'}
	<!-- Display row -->
	<tr class="group hover:bg-muted/20 relative border-b">
		<!-- WBS Code -->
		<td class="py-3 pr-2" style="padding-left: {0.5 + (indent - 1) * 1}rem">
			<div class="flex items-center">
				<span class="ml-6">–</span>
			</div>
		</td>
		<!-- Description -->
		<td class="px-px py-1" style="padding-left: {0.5 + (indent - 1) * 1}rem">
			{node.data.description}
		</td>
		{#if showRateCalculation}
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
			<td class="p-1 text-right"></td>
		{/if}

		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<td class="p-1 text-right"></td>
		<!-- Accepted Changes -->
		<td class="p-1 text-right">
			{#if node.data.riskData?.acceptedChanges && node.data.riskData.acceptedChanges > 0}
				<!-- link to associated approved changes page -->
				<a href="construction/approved?wbs_item={node.data.wbs_library_item_id}">
					{formatCurrency(node.data.riskData.acceptedChanges)}
				</a>
			{/if}
		</td>
		<!-- Anticipated Changes -->
		<td class="p-1 text-right">
			{#if node.data.riskData?.anticipatedChanges && node.data.riskData.anticipatedChanges > 0}
				<!-- link to associated pending changes page -->
				<a href="construction/pending?wbs_item={node.data.wbs_library_item_id}">
					{formatCurrency(node.data.riskData.anticipatedChanges)}
				</a>
			{/if}
		</td>
		<!-- Forecast Final Cost -->
		<td class="p-1 text-right">
			{formatCurrency(
				(node.data.riskData?.acceptedChanges ?? 0) + (node.data.riskData?.anticipatedChanges ?? 0),
			)}
		</td>
		<!-- Delta to Control Budget -->
		<td class="p-1 text-right"> </td>
		<!-- Advanced Warnings -->
		<td class="p-1 text-right">
			{#if node.data.riskData?.advancedWarnings && node.data.riskData.advancedWarnings > 0}
				<!-- link to associated risks page -->
				<a href="construction/risks?wbs_item={node.data.wbs_library_item_id}">
					{formatCurrency(node.data.riskData.advancedWarnings)}
				</a>
			{/if}
		</td>
	</tr>
{/if}
