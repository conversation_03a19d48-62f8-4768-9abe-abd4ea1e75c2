<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import TenderStatusBadge from './TenderStatusBadge.svelte';
	import { formatCurrency } from '$lib/utils';

	interface Props {
		tender: {
			id: string;
			tender_name: string;
			vendor_name: string;
			status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
			submission_date: string | null;
			total_amount: number | null;
			currency_symbol: string;
			line_item_count: number;
		};
		onView?: () => void;
		onEdit?: () => void;
		onDelete?: () => void;
	}

	const { tender, onView, onEdit, onDelete }: Props = $props();

	function formatDate(dateString: string | null) {
		if (!dateString) return 'Not submitted';
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}
</script>

<Card.Root class="transition-shadow hover:shadow-md">
	<Card.Header>
		<div class="flex items-start justify-between">
			<div>
				<Card.Title class="text-lg">{tender.tender_name}</Card.Title>
				<Card.Description>{tender.vendor_name}</Card.Description>
			</div>
			<TenderStatusBadge status={tender.status} />
		</div>
	</Card.Header>
	<Card.Content>
		<div class="space-y-2">
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Submission Date:</span>
				<span>{formatDate(tender.submission_date)}</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Total Amount:</span>
				<span class="font-medium">
					{formatCurrency(tender.total_amount, {
						symbol: tender.currency_symbol,
						symbolPosition: 'before',
						fallback: '-',
					})}
				</span>
			</div>
			<div class="flex justify-between text-sm">
				<span class="text-gray-500">Line Items:</span>
				<span>{tender.line_item_count}</span>
			</div>
		</div>
	</Card.Content>
	<Card.Footer class="flex gap-2">
		{#if onView}
			<Button variant="outline" size="sm" onclick={onView}>View</Button>
		{/if}
		{#if onEdit}
			<Button variant="outline" size="sm" onclick={onEdit}>Edit</Button>
		{/if}
		{#if onDelete}
			<Button variant="destructive" size="sm" onclick={onDelete}>Delete</Button>
		{/if}
	</Card.Footer>
</Card.Root>
