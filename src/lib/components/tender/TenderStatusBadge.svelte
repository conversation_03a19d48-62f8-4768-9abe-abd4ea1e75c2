<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';

	interface Props {
		status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
	}

	const { status }: Props = $props();

	const statusConfig = {
		draft: { variant: 'secondary', label: 'Draft' },
		submitted: { variant: 'default', label: 'Submitted' },
		under_review: { variant: 'outline', label: 'Under Review' },
		approved: { variant: 'default', label: 'Approved' },
		rejected: { variant: 'destructive', label: 'Rejected' },
	} as const;

	const config = $derived(statusConfig[status]);
</script>

<Badge variant={config.variant}>{config.label}</Badge>
