import { fail } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { calculateNormalizationAmount, updateNormalizationAmount } from '$lib/tender_utils';

/**
 * Form action to update normalization amount
 */
export async function updateNormalizationAction(event: RequestEvent) {
	try {
		const formData = await event.request.formData();
		const tender_line_item_id = formData.get('tender_line_item_id') as string;
		const normalization_type = formData.get('normalization_type') as 'amount' | 'percentage';
		const normalization_amount = formData.get('normalization_amount');
		const normalization_percentage = formData.get('normalization_percentage');

		if (!tender_line_item_id || !normalization_type) {
			return fail(400, { message: 'Line item ID and normalization type are required' });
		}

		let normalizationValue: number;
		let calculatedAmount: number | null = null;

		if (normalization_type === 'amount') {
			if (!normalization_amount) {
				return fail(400, { message: 'Normalization amount is required' });
			}
			normalizationValue = parseFloat(normalization_amount as string);
		} else {
			if (!normalization_percentage) {
				return fail(400, { message: 'Normalization percentage is required' });
			}

			// Store the original percentage value
			normalizationValue = parseFloat(normalization_percentage as string);

			// Calculate amount from percentage for display/reference
			const calculationResult = await calculateNormalizationAmount(
				event.locals.supabase,
				tender_line_item_id,
				normalizationValue,
			);

			if (!calculationResult || calculationResult.length === 0) {
				return fail(500, { message: 'Failed to calculate normalization amount' });
			}

			calculatedAmount = Number(calculationResult[0].calculated_amount);
		}

		const lineItem = await updateNormalizationAmount(
			event.locals.supabase,
			tender_line_item_id,
			normalization_type,
			normalizationValue,
			calculatedAmount,
		);

		return {
			lineItem,
			calculatedAmount: calculatedAmount ?? normalizationValue,
		};
	} catch (error) {
		console.error('Error updating normalization:', error);
		return fail(500, { message: 'Failed to update normalization' });
	}
}

/**
 * Form action to calculate normalization amount from percentage
 */
export async function calculateNormalizationAction(event: RequestEvent) {
	try {
		const formData = await event.request.formData();
		const lineItemId = formData.get('line_item_id') as string;
		const percentage = parseFloat(formData.get('percentage') as string);

		if (!lineItemId || isNaN(percentage)) {
			return fail(400, { message: 'Line item ID and percentage are required' });
		}

		const result = await calculateNormalizationAmount(
			event.locals.supabase,
			lineItemId,
			percentage,
		);

		if (!result || result.length === 0) {
			return fail(500, { message: 'Failed to calculate normalization amount' });
		}

		return {
			calculatedAmount: result[0].calculated_amount,
			totalBudgetAmount: result[0].total_budget_amount,
			mappingCount: result[0].mapping_count,
		};
	} catch (error) {
		console.error('Error calculating normalization:', error);
		return fail(500, { message: 'Failed to calculate normalization amount' });
	}
}
