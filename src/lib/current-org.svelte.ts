import { getContext, setContext } from 'svelte';
import type { Tables, Database, Enums } from './database.types';
import { dev } from '$app/environment';
import type { SupabaseClient } from '@supabase/supabase-js';

// Define a type for organization with role
type OrgWithRole = Tables<'organization'> & { role: Enums<'membership_role'> };

export class CurrentOrg {
	orgId = $state<string | null>(null);
	availableOrgs = $state<OrgWithRole[]>([]);
	currentOrg = $state<OrgWithRole | null>(null);

	constructor(orgId: string | null) {
		this.orgId = orgId;
	}

	setOrgId(orgId: string | null) {
		this.orgId = orgId;
		// Update currentOrg when orgId changes
		if (orgId && this.availableOrgs.length > 0) {
			this.currentOrg = this.availableOrgs.find((o) => o.org_id === orgId) ?? null;
		} else {
			this.currentOrg = null;
		}
	}

	getOrgId() {
		return this.orgId;
	}

	getAvailableOrgs() {
		return this.availableOrgs;
	}

	getCurrentOrg() {
		return this.currentOrg;
	}

	async loadOrgs(supabase: SupabaseClient<Database>, userId: string | undefined) {
		if (!userId) {
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		// Fetch organizations (with role) via RPC
		const { data, error } = await supabase.rpc('get_orgs_for_user');

		if (error) {
			console.error('Error fetching organizations:', error);
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		if (!data || data.length === 0) {
			console.log('No organizations found');
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		// Data already includes role from the function
		const orgs: OrgWithRole[] = data as OrgWithRole[];

		this.availableOrgs = orgs;

		const orgIds = orgs.map((org) => org.org_id);

		// determine active org selection
		if (this.orgId && orgIds.includes(this.orgId)) {
			this.currentOrg = orgs.find((o) => o.org_id === this.orgId) ?? null;
		} else if (orgs.length === 1) {
			const first = orgs[0];
			this.setOrgId(first.org_id);
			this.currentOrg = first;
		} else {
			this.setOrgId(null);
			this.currentOrg = null;
		}
	}
}

const ORG_ID_KEY = Symbol('ORG_ID');

export function setCurrentOrgId(orgId: string | null) {
	return setContext(ORG_ID_KEY, new CurrentOrg(orgId));
}

export function getCurrentOrgId() {
	return getContext<ReturnType<typeof setCurrentOrgId>>(ORG_ID_KEY);
}

export const ORG_COOKIE_NAME = 'activeOrgId';
export const ORG_COOKIE_OPTIONS = {
	path: '/',
	httpOnly: false,
	secure: !dev,
	sameSite: 'lax' as const,
	maxAge: 60 * 60 * 24 * 30,
};
