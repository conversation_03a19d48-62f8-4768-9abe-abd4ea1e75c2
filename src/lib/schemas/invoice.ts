import { z } from 'zod';

// Base invoice schema for creation
export const invoiceSchema = z.object({
	purchase_order_id: z.uuid('Please select a purchase order'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z
		.string()
		.regex(/^\d{4}-\d{2}$/, 'Period must be in YYYY-MM format (e.g., 2024-01)')
		.optional()
		.nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
});

// Edit invoice schema - includes invoice_id for updates
export const editInvoiceSchema = z.object({
	invoice_id: z.uuid(),
	purchase_order_id: z.uuid('Please select a purchase order'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z
		.string()
		.regex(/^\d{4}-\d{2}$/, 'Period must be in YYYY-MM format (e.g., 2024-01)')
		.optional()
		.nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
});

// Modal version for inline creation (without project_id as it's provided by context)
export const invoiceModalSchema = z.object({
	purchase_order_id: z.uuid('Please select a purchase order'),
	description: z.string().optional().nullable(),
	invoice_date: z.string().min(1, 'Invoice date is required'),
	account: z.string().min(1, 'Account is required'),
	amount: z.number().min(0.01, 'Amount must be greater than 0'),
	period: z
		.string()
		.regex(/^\d{4}-\d{2}$/, 'Period must be in YYYY-MM format (e.g., 2024-01)')
		.optional()
		.nullable(),
	post_date: z.string().min(1, 'Post date is required'),
	notes: z.string().optional().nullable(),
});

// Type exports
export type InvoiceSchema = z.infer<typeof invoiceSchema>;
export type EditInvoiceSchema = z.infer<typeof editInvoiceSchema>;
export type InvoiceModalSchema = z.infer<typeof invoiceModalSchema>;

// Type for invoice data from database (includes timestamps and IDs)
export type InvoiceData = {
	invoice_id: string;
	purchase_order_id: string;
	description: string | null;
	invoice_date: string;
	account: string;
	amount: number;
	period: string | null;
	post_date: string;
	notes: string | null;
	created_by_user_id: string;
	created_at: string;
	updated_at: string;
};

// Type for invoice list items (with joined vendor and purchase order information)
// This matches the return type from get_accessible_invoices RPC function
export type InvoiceListItem = {
	invoice_id: string;
	purchase_order_id: string;
	po_number: string;
	description: string | null;
	invoice_date: string;
	vendor_name: string;
	account: string;
	amount: number;
	period: string | null;
	post_date: string;
	notes: string | null;
	created_at: string;
};

// Helper function to create period from year and month
export function createAccountingPeriod(year: number, month: number): string {
	const monthStr = month.toString().padStart(2, '0');
	return `${year}-${monthStr}`;
}

// Helper function to format period for display (e.g., "2024-01" -> "January 2024")
export function formatAccountingPeriod(period: string): string {
	if (!period || !period.match(/^\d{4}-\d{2}$/)) {
		return period; // Return as-is if not in expected format
	}

	const [year, month] = period.split('-');
	const monthNames = [
		'January',
		'February',
		'March',
		'April',
		'May',
		'June',
		'July',
		'August',
		'September',
		'October',
		'November',
		'December',
	];

	const monthIndex = parseInt(month, 10) - 1;
	return `${monthNames[monthIndex]} ${year}`;
}

// Helper function to get current period in YYYY-MM format
export function getCurrentAccountingPeriod(): string {
	const now = new Date();
	const year = now.getFullYear();
	const month = (now.getMonth() + 1).toString().padStart(2, '0');
	return `${year}-${month}`;
}

// Helper function to parse period string into year and month
export function parseAccountingPeriod(period: string): { year: number; month: number } | null {
	if (!period || !period.match(/^\d{4}-\d{2}$/)) {
		return null;
	}

	const [yearStr, monthStr] = period.split('-');
	return {
		year: parseInt(yearStr, 10),
		month: parseInt(monthStr, 10),
	};
}

// Helper function to get periods for a specific year
export function getPeriodsForYear(year: number): string[] {
	const periods: string[] = [];
	for (let month = 1; month <= 12; month++) {
		const monthStr = month.toString().padStart(2, '0');
		periods.push(`${year}-${monthStr}`);
	}
	return periods;
}

// Helper function to get unique years from a list of periods
export function getYearsFromPeriods(periods: (string | null)[]): number[] {
	const years = new Set<number>();

	periods.forEach((period) => {
		if (period) {
			const parsed = parseAccountingPeriod(period);
			if (parsed) {
				years.add(parsed.year);
			}
		}
	});

	return Array.from(years).sort((a, b) => b - a); // Most recent first
}

// Date formatting utility
export function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleDateString('en-GB', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	});
}
