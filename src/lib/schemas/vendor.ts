import type { Tables } from '$lib/database.types';
import { z } from 'zod';

// Vendor type options
export const vendorTypes = [
	'Contractor',
	'Subcontractor',
	'Supplier',
	'Consultant',
	'Service Provider',
	'Material Supplier',
	'Equipment Rental',
	'Professional Services',
	'Other',
] as const;

export type CurrencyCode = Tables<'currency'>['currency_code'];
export const DEFAULT_CURRENCY_CODE: CurrencyCode = 'SEK';

export const currencyCodeSchema = z
	.string()
	.regex(/^[A-Z]{3}$/u, 'Currency code must be a 3-letter uppercase ISO 4217 code');

// Payment terms options
export const paymentTermsOptions = [
	'Net 30',
	'Net 60',
	'Net 90',
	'Due on Receipt',
	'COD',
	'Prepaid',
	'2/10 Net 30',
	'Custom',
] as const;

// Base vendor schema for creation
export const vendorSchema = z
	.object({
		name: z.string().min(1, 'Vendor name is required'),
		description: z.string().optional().nullable(),

		// Hierarchical access control - exactly one must be provided
		org_id: z.uuid().optional().nullable(),
		client_id: z.uuid().optional().nullable(),
		project_id: z.uuid().optional().nullable(),

		// Contact information
		contact_name: z.string().optional().nullable(),
		contact_email: z.email('Please enter a valid email address').optional().nullable(),
		contact_phone: z.string().optional().nullable(),
		contact_address: z.string().optional().nullable(),
		website: z.url('Please enter a valid URL').optional().nullable(),

		// Vendor-specific fields
		vendor_type: z.enum(vendorTypes).optional().nullable(),
		tax_id: z.string().optional().nullable(),
		payment_terms: z.enum(paymentTermsOptions).optional().nullable(),
		payment_terms_days: z.number().int().min(0).max(365).optional().nullable(),
		credit_limit: z.number().min(0).optional().nullable(),
		currency: currencyCodeSchema.default(DEFAULT_CURRENCY_CODE),
		is_active: z.boolean().default(true),

		// JSONB fields - will be handled as key-value pairs in UI
		certification_info: z.record(z.string(), z.string()).optional().nullable(),
		insurance_info: z.record(z.string(), z.string()).optional().nullable(),
		additional_data: z.record(z.string(), z.string()).optional().nullable(),
	})
	.refine(
		(data) => {
			// Exactly one of org_id, client_id, or project_id must be provided
			const hasOrgId = data.org_id != null;
			const hasClientId = data.client_id != null;
			const hasProjectId = data.project_id != null;

			return (
				(hasOrgId && !hasClientId && !hasProjectId) ||
				(!hasOrgId && hasClientId && !hasProjectId) ||
				(!hasOrgId && !hasClientId && hasProjectId)
			);
		},
		{
			message: 'Exactly one of organization, client, or project must be selected',
			path: ['org_id'], // Show error on org_id field
		},
	);

// Edit vendor schema - includes vendor_id for updates
export const editVendorSchema = z
	.object({
		vendor_id: z.uuid(),
		name: z.string().min(1, 'Vendor name is required'),
		description: z.string().optional().nullable(),

		// Hierarchical access control - exactly one must be provided
		org_id: z.uuid().optional().nullable(),
		client_id: z.uuid().optional().nullable(),
		project_id: z.uuid().optional().nullable(),

		// Contact information
		contact_name: z.string().optional().nullable(),
		contact_email: z.email('Please enter a valid email address').optional().nullable(),
		contact_phone: z.string().optional().nullable(),
		contact_address: z.string().optional().nullable(),
		website: z.url('Please enter a valid URL').optional().nullable(),

		// Vendor-specific fields
		vendor_type: z.enum(vendorTypes).optional().nullable(),
		tax_id: z.string().optional().nullable(),
		payment_terms: z.enum(paymentTermsOptions).optional().nullable(),
		payment_terms_days: z.number().int().min(0).max(365).optional().nullable(),
		credit_limit: z.number().min(0).optional().nullable(),
		currency: currencyCodeSchema.default(DEFAULT_CURRENCY_CODE),
		is_active: z.boolean().default(true),

		// JSONB fields - will be handled as key-value pairs in UI
		certification_info: z.record(z.string(), z.string()).optional().nullable(),
		insurance_info: z.record(z.string(), z.string()).optional().nullable(),
		additional_data: z.record(z.string(), z.string()).optional().nullable(),
	})
	.refine(
		(data) => {
			// Exactly one of org_id, client_id, or project_id must be provided
			const hasOrgId = data.org_id != null;
			const hasClientId = data.client_id != null;
			const hasProjectId = data.project_id != null;

			return (
				(hasOrgId && !hasClientId && !hasProjectId) ||
				(!hasOrgId && hasClientId && !hasProjectId) ||
				(!hasOrgId && !hasClientId && hasProjectId)
			);
		},
		{
			message: 'Exactly one of organization, client, or project must be selected',
			path: ['org_id'], // Show error on org_id field
		},
	);

// Type definitions
export type VendorSchema = z.infer<typeof vendorSchema>;
export type EditVendorSchema = z.infer<typeof editVendorSchema>;

// Type for vendor data from database (includes timestamps and IDs)
export type VendorData = {
	vendor_id: string;
	name: string;
	description: string | null;
	org_id: string | null;
	client_id: string | null;
	project_id: string | null;
	contact_name: string | null;
	contact_email: string | null;
	contact_phone: string | null;
	contact_address: string | null;
	website: string | null;
	vendor_type: string | null;
	tax_id: string | null;
	payment_terms: string | null;
	payment_terms_days: number | null;
	credit_limit: number | null;
	currency: CurrencyCode;
	is_active: boolean;
	certification_info: Record<string, string> | null;
	insurance_info: Record<string, string> | null;
	additional_data: Record<string, string> | null;
	created_by_user_id: string;
	created_at: string;
	updated_at: string;
};

// Type for vendor list from RPC function
export type VendorListItem = {
	vendor_id: string;
	name: string;
	description: string | null;
	vendor_type: string | null;
	contact_name: string | null;
	contact_email: string | null;
	contact_phone: string | null;
	is_active: boolean;
	access_level:
		| 'organization'
		| 'client'
		| 'project'
		| Omit<string, 'organization' | 'client' | 'project'>;
};

// Helper type for hierarchical selection
export type HierarchicalSelection = {
	type: 'organization' | 'client' | 'project';
	org_id?: string;
	client_id?: string;
	project_id?: string;
};

// Type for vendor creation hierarchy data from RPC function
export type VendorCreationHierarchyItem = {
	entity_type: string;
	entity_id: string;
	entity_name: string;
	parent_entity_type: string | null;
	parent_entity_id: string | null;
	parent_entity_name: string | null;
	grandparent_entity_type: string | null;
	grandparent_entity_id: string | null;
	grandparent_entity_name: string | null;
	user_role: string;
};

// Processed hierarchy data for UI consumption
export type ProcessedHierarchyData = {
	organizations: Array<{
		organization: { org_id: string; name: string };
		role: string;
	}>;
	clients: Array<{
		client: {
			client_id: string;
			name: string;
			organization?: { org_id: string; name: string };
		};
		role: string;
	}>;
	projects: Array<{
		project: {
			project_id: string;
			name: string;
			client?: {
				client_id: string;
				name: string;
				organization?: { org_id: string; name: string };
			};
		};
		role: string;
	}>;
};

// Key-value pair type for JSONB fields
export type KeyValuePair = {
	id: string;
	key: string;
	value: string;
};

// Utility function to process hierarchy data from RPC into UI-friendly format
export function processVendorCreationHierarchy(
	hierarchyData: VendorCreationHierarchyItem[],
): ProcessedHierarchyData {
	const organizations: ProcessedHierarchyData['organizations'] = [];
	const clients: ProcessedHierarchyData['clients'] = [];
	const projects: ProcessedHierarchyData['projects'] = [];

	for (const item of hierarchyData) {
		switch (item.entity_type) {
			case 'organization':
				organizations.push({
					organization: {
						org_id: item.entity_id,
						name: item.entity_name,
					},
					role: item.user_role,
				});
				break;

			case 'client':
				clients.push({
					client: {
						client_id: item.entity_id,
						name: item.entity_name,
						organization: item.parent_entity_id
							? {
									org_id: item.parent_entity_id,
									name: item.parent_entity_name!,
								}
							: undefined,
					},
					role: item.user_role,
				});
				break;

			case 'project':
				projects.push({
					project: {
						project_id: item.entity_id,
						name: item.entity_name,
						client: item.parent_entity_id
							? {
									client_id: item.parent_entity_id,
									name: item.parent_entity_name!,
									organization: item.grandparent_entity_id
										? {
												org_id: item.grandparent_entity_id,
												name: item.grandparent_entity_name!,
											}
										: undefined,
								}
							: undefined,
					},
					role: item.user_role,
				});
				break;
		}
	}

	return { organizations, clients, projects };
}
