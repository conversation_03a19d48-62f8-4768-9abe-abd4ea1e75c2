Create a comprehensive technical implementation plan for a "tender analysis" workflow system. This plan must be detailed enough for a junior developer to implement without additional guidance, including specific database schema designs, API endpoints, UI components, and step-by-step implementation instructions. Save the plan as a markdown file with actionable checklist items that can be tracked and completed individually.

**Context & Starting Point:**

- The system builds upon existing WBS (Work Breakdown Structure) and budget hierarchy from pre-construction phase (stored in `budget_version` table and related data)
- Follow the established patterns from the existing project controls application (SvelteKit, TypeScript, Supabase, Shadcn-Svelte components, and sveltekit-superforms)
- Integrate with existing vendor management system and audit logging infrastructure

**Core Requirements:**

**1. Tender Data Management:**

- Enable manual input of tender offers from contractors (vendors from existing `vendor` table)
- Support tender line items with these optional fields: quantity, unit, material_rate, labor_rate, productivity, unit_rate, subtotal, percentage_of_scope, normalization, notes
- Implement tender revision system with version tracking
- Handle "Provisional Sum" and "Prime Cost" entries by encouraging the user to note them in the "Unit" column
- Support tender line items that map to either individual WBS leaf nodes or entire WBS hierarchy categories

**2. WBS Mapping & Scope Coverage:**

- Each tender line item must map to one or more WBS items from the existing budget hierarchy
- For each WBS mapping, we need to know the percentage of the budgeted scope that is being tendered. To do so, require user input of either:
  - Specific quantity covered by the tender line item, OR
  - Percentage of the WBS item's total scope covered
- For category-level tender items, provide checkbox interface to specify inclusion/exclusion of each child WBS item

**3. Budget Transfer Mechanism:**

- Implement functionality to transfer budget amounts between WBS items when a tender line item covers scope from multiple WBS codes
- Track these transfers for audit and comparison purposes

**4. Tender Scoring System:**

- Support multiple scoring criteria (completeness, experience, price, methodology, team, etc.) with 0-10 scale
- Enable optional weighted average calculation based on user-defined criterion weights
- Store scoring history and allow score revisions

**5. Tender Comparison View:**

- Display only the most recent version of each tender (handle multiple alternatives from same vendor)
- Organize data by WBS items for direct comparison
- Highlight significant price variances from original budget (both over and under)
- Show percentage coverage for each WBS item
- Validate that line items with partial coverage (not 100%) include normalization amounts
- Handle complex UI scenarios where budget transfers affect some but not all tenders
- Provide filtering through toggle-able WBS codes/sections with aggregated totals and normalization
- Use search params for those filters so that they can be shared or saved
- Provide show/hide toggle for each tender

**6. Tender Selection & Work Package Creation:**

- Implement tender selection workflow (marking tenders as chosen for offline contract signing)
- Force the user to convert selected tenders into work packages containing one or more WBS codes
- Support work package to purchase order many-to-many relationship via join table
- Enable multiple purchase orders per work package for phased payments

**7. Data Architecture Requirements:**

- Design complete database schema including all new tables, relationships, and constraints
- Implement audit logging for all new tables following existing patterns
- Ensure proper foreign key relationships with existing budget, vendor, and project data
- Handle data integrity for tender revisions and WBS mappings

**Technical Implementation Specifications:**

- Use existing SvelteKit routing patterns (/org/[org_name]/clients/[client_name]/projects/[project_id_short]/tenders)
- Follow established form patterns with Superforms, Zod validation, and Shadcn-Svelte components
- Minimize database roundtrips in load functions and form actions through the use of join queries or rpc functions
- Implement proper RLS (Row Level Security) policies following existing access control patterns
- Use TypeScript throughout with proper type definitions
- Include comprehensive error handling and user feedback
- Design responsive UI components that work on desktop and tablet devices

**Deliverable Format:**
Structure the plan with:

1. Database schema design (tables, relationships, indexes, RLS policies)
2. API endpoint specifications (load functions, form actions, RPC functions)
3. UI component breakdown with specific Svelte component requirements
4. Step-by-step implementation checklist
5. Testing strategy including unit tests and integration tests
6. Migration strategy for existing data
7. User acceptance criteria for each feature

Each checklist item should be specific enough to be completed in 1-2 hours by a junior developer and include clear acceptance criteria.
