import { test, expect } from '@playwright/test';

test.describe('Tender Management E2E', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to the project page
		await page.goto(
			'/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/overview',
		);

		// Wait for page to load
		await expect(page.locator('h1')).toContainText('Marcuss Gata 31');
	});

	test('should navigate to tenders page', async ({ page }) => {
		// Click on Tenders tab
		await page.click('text=Tenders');

		// Verify we're on the tenders page
		await expect(page).toHaveURL(/.*\/tenders$/);
		await expect(page.locator('h1')).toContainText('Tenders');
	});

	test('should create a new tender', async ({ page }) => {
		// Navigate to tenders page
		await page.click('text=Tenders');

		// Click New Tender button
		await page.click('text=New Tender');

		// Verify we're on the new tender page
		await expect(page).toHaveURL(/.*\/tenders\/new$/);

		// Fill out the tender form
		await page.fill('input[name="tender_name"]', 'Test E2E Tender');
		await page.fill(
			'textarea[name="description"]',
			'This is a test tender created via E2E testing',
		);

		// Select a vendor (assuming there's at least one vendor available)
		await page.click('[data-testid="vendor-select"]');
		await page.click('text=Test Vendor'); // Adjust based on available vendors

		// Fill optional fields
		await page.fill('textarea[name="notes"]', 'E2E test notes');

		// Submit the form
		await page.click('button[type="submit"]');

		// Should redirect to line items page
		await expect(page).toHaveURL(/.*\/tenders\/.*\/line-items$/);
		await expect(page.locator('h1')).toContainText('Line Items');
	});

	test('should add line items to a tender', async ({ page }) => {
		// Navigate to tenders page and create a tender first
		await page.click('text=Tenders');
		await page.click('text=New Tender');

		// Create a basic tender
		await page.fill('input[name="tender_name"]', 'Test Tender for Line Items');
		await page.fill('textarea[name="description"]', 'Test description');
		await page.click('[data-testid="vendor-select"]');
		await page.click('text=Test Vendor');
		await page.click('button[type="submit"]');

		// Now we should be on the line items page
		await expect(page).toHaveURL(/.*\/line-items$/);

		// Click Add Line Item button
		await page.click('text=Add Line Item');

		// Fill out the line item form
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Test line item description');
		await page.fill('input[name="quantity"]', '10');
		await page.fill('input[name="unit"]', 'm²');
		await page.fill('input[name="unit_rate"]', '100');

		// Submit the line item
		await page.click('button[type="submit"]');

		// Verify the line item appears in the table
		await expect(page.locator('table')).toContainText('Test line item description');
		await expect(page.locator('table')).toContainText('10');
		await expect(page.locator('table')).toContainText('m²');
		await expect(page.locator('table')).toContainText('100');
	});

	test('should calculate subtotals correctly', async ({ page }) => {
		// Navigate to line items page (assuming we have a tender)
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		// If there are existing tenders, click on one
		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');
		} else {
			// Create a new tender and add line items
			await page.click('text=New Tender');
			await page.fill('input[name="tender_name"]', 'Calculation Test Tender');
			await page.fill('textarea[name="description"]', 'Test calculation');
			await page.click('[data-testid="vendor-select"]');
			await page.click('text=Test Vendor');
			await page.click('button[type="submit"]');
		}

		// Add a line item with known values
		await page.click('text=Add Line Item');
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Calculation test item');
		await page.fill('input[name="quantity"]', '5');
		await page.fill('input[name="unit"]', 'units');
		await page.fill('input[name="unit_rate"]', '200');

		// The subtotal should be automatically calculated (5 * 200 = 1000)
		const subtotalField = page.locator('input[name="subtotal"]');
		await expect(subtotalField).toHaveValue('1000');

		await page.click('button[type="submit"]');

		// Verify the calculated subtotal appears in the table
		await expect(page.locator('table')).toContainText('1000');
	});

	test('should handle normalization inputs', async ({ page }) => {
		// Navigate to a tender with line items
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		// Create or navigate to a tender with line items
		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');
		}

		// Add a line item with normalization
		await page.click('text=Add Line Item');
		await page.fill('input[name="line_number"]', '1');
		await page.fill('textarea[name="description"]', 'Normalization test item');
		await page.fill('input[name="quantity"]', '10');
		await page.fill('input[name="unit"]', 'm²');
		await page.fill('input[name="unit_rate"]', '100');

		// Test percentage normalization
		await page.selectOption('select[name="normalization_type"]', 'percentage');
		await page.fill('input[name="normalization_percentage"]', '10');

		// Submit and verify
		await page.click('button[type="submit"]');

		// The normalized amount should be calculated (1000 * 10% = 100)
		await expect(page.locator('table')).toContainText('100'); // normalization amount
	});

	test('should edit existing line items', async ({ page }) => {
		// Navigate to a tender with existing line items
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		// Find a tender and navigate to its line items
		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');

			// Click edit on the first line item
			const editButton = page
				.locator('table tbody tr')
				.first()
				.locator('button[aria-label="Edit"]');
			if (await editButton.isVisible()) {
				await editButton.click();

				// Modify the description
				await page.fill('textarea[name="description"]', 'Updated line item description');

				// Submit the changes
				await page.click('button[type="submit"]');

				// Verify the changes appear
				await expect(page.locator('table')).toContainText('Updated line item description');
			}
		}
	});

	test('should delete line items with confirmation', async ({ page }) => {
		// Navigate to a tender with line items
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();
			await page.click('text=Line Items');

			// Count initial line items
			const initialCount = await page.locator('table tbody tr').count();

			// Click delete on the first line item
			const deleteButton = page
				.locator('table tbody tr')
				.first()
				.locator('button[aria-label="Delete"]');
			if (await deleteButton.isVisible()) {
				await deleteButton.click();

				// Confirm deletion in the dialog
				await page.click('text=Confirm');

				// Verify the line item was removed
				const newCount = await page.locator('table tbody tr').count();
				expect(newCount).toBe(initialCount - 1);
			}
		}
	});

	test('should update tender status', async ({ page }) => {
		// Navigate to tenders page
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		// Find a tender in draft status
		const draftTender = page.locator('table tbody tr').filter({ hasText: 'Draft' }).first();
		if (await draftTender.isVisible()) {
			await draftTender.locator('text=Edit').click();

			// Change status to submitted
			await page.selectOption('select[name="status"]', 'submitted');

			// Submit the form
			await page.click('button[type="submit"]');

			// Verify the status changed
			await expect(page.locator('table')).toContainText('Submitted');
		}
	});

	test('should display tender summary correctly', async ({ page }) => {
		// Navigate to a specific tender
		await page.goto('/org/Aurora/clients/Maroon%20Dysprosium/projects/Marcuss%20Gata%2031/tenders');

		const firstTender = page.locator('table tbody tr').first();
		if (await firstTender.isVisible()) {
			await firstTender.locator('text=View').click();

			// Verify tender details are displayed
			await expect(page.locator('[data-testid="tender-summary"]')).toBeVisible();
			await expect(page.locator('text=Vendor')).toBeVisible();
			await expect(page.locator('text=Status')).toBeVisible();
			await expect(page.locator('text=Total Amount')).toBeVisible();
			await expect(page.locator('text=Line Items')).toBeVisible();
		}
	});
});
