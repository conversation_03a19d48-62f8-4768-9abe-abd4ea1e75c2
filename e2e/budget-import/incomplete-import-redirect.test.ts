/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { expect, test } from '@playwright/test';
import { TestDataSetup } from '../utils/mcp-helpers';

test.describe('Redirect to import when WBS is empty', () => {
	let testOrgName: string;
	let testClientName: string;
	let testDataSetup: TestDataSetup;

	test.beforeAll(async ({ browser }) => {
		const timestamp = Date.now();
		testOrgName = `Redirect Org ${timestamp}`;
		testClientName = `Redirect Client ${timestamp}`;

		const page = await browser.newPage();
		testDataSetup = new TestDataSetup(page);

		await testDataSetup.signIn();
		await testDataSetup.createOrganization(testOrgName);
		await testDataSetup.createClient(testClientName, 'Client for redirect tests');

		await page.close();
	});

	test.beforeEach(async ({ page }) => {
		testDataSetup = new TestDataSetup(page);
		await testDataSetup.signIn();
		await page.goto(
			`/org/${encodeURIComponent(testOrgName)}/clients/${encodeURIComponent(testClientName)}`,
		);
	});

	test('blocks non-import routes until budget import completes', async ({ page }) => {
		// Create a new project (default ICMS WBS), then switch to empty Custom WBS
		const projectName = `Redirect Test Project ${Date.now()}`;
		const projectIdShort = await testDataSetup.createProject(projectName, {
			description: 'Project to verify redirect gating on empty WBS',
			stageType: 'icms',
			wbsLibraryName: 'Custom WBS',
		});

		// Build base project URL from known identifiers to avoid brittle regex
		const baseUrl = `/org/${encodeURIComponent(testOrgName)}/clients/${encodeURIComponent(
			testClientName,
		)}/projects/${encodeURIComponent(projectIdShort)}`;

		// At this point, WBS library is Custom WBS (empty). Non-import routes should be gated.

		// Helper to assert redirect back to import with flash message
		const expectRedirectToImport = async () => {
			await expect(page).toHaveURL(new RegExp(`${baseUrl}/budget/import$`));
		};

		// Try several non-import routes and expect redirect back to import
		const attemptPaths = [
			'/overview',
			'/budget',
			'/budget/edit',
			'/work-package',
			'/work-package/new',
		];

		for (const path of attemptPaths) {
			await page.goto(baseUrl + path);
			await expectRedirectToImport();
		}

		// The import route itself must remain reachable (no loop)
		await page.goto(`${baseUrl}/budget/import`);
		await expect(page).toHaveURL(`${baseUrl}/budget/import`);
		await expect(page.getByRole('heading', { name: /Import Budget/i })).toBeVisible();
	});
});
