import { expect, test } from '@playwright/test';
import { TestDataSetup } from '../utils/mcp-helpers';

test.describe('Budget Version Manager - Version History dialog', () => {
	let testOrgName: string;
	let testClientName: string;
	let testDataSetup: TestDataSetup;

	test.beforeAll(async ({ browser }) => {
		// Generate unique test identifiers
		const timestamp = Date.now();
		testOrgName = `Test Org ${timestamp}`;
		testClientName = `Test Client ${timestamp}`;

		// Create a fresh test environment
		const page = await browser.newPage();
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Create organization and client
		await testDataSetup.createOrganization(testOrgName);
		await testDataSetup.createClient(
			testClientName,
			'Test client for budget version history tests',
		);

		await page.close();
	});

	test('signs in, creates project, and opens version history', async ({ page }) => {
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Navigate to the test client
		await page.goto(
			`/org/${encodeURIComponent(testOrgName)}/clients/${encodeURIComponent(testClientName)}`,
		);

		// Create a new project for this test
		const projectName = `Version History Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for version history dialog',
			stageType: 'icms',
		});

		// Landed on project overview; navigate to budget version history
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		await page.locator('#project-menu').click();
		await page.getByRole('link', { name: 'Budget History' }).click();

		await expect(page.getByText('Version')).toBeVisible();
		await expect(page.getByText('Type')).toBeVisible();
		await expect(page.getByText('Items')).toBeVisible();
		await expect(page.getByText('Total Cost')).toBeVisible();
		await expect(page.getByText('Actions')).toBeVisible();

		// Check if there are any table rows (versions) - should have demo data
		const rows = page.locator('table tbody tr');
		const rowCount = await rows.count();

		// The test passes if we can open the dialog and see version data
		expect(rowCount).toBeGreaterThan(0);
	});
});
