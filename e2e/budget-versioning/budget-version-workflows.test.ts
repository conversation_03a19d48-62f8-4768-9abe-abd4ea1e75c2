/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { expect, test } from '@playwright/test';
import { TestDataSetup } from '../utils/mcp-helpers';
import { formatCurrency } from '../../src/lib/utils';

test.describe('Budget Versioning End-to-End Workflows', () => {
	let testOrgName: string;
	let testClientName: string;
	let testDataSetup: TestDataSetup;

	test.beforeAll(async ({ browser }) => {
		// Generate unique test identifiers
		const timestamp = Date.now();
		testOrgName = `Test Org ${timestamp}`;
		testClientName = `Test Client ${timestamp}`;

		// Create a fresh test environment
		const page = await browser.newPage();
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Create organization and client
		await testDataSetup.createOrganization(testOrgName);
		await testDataSetup.createClient(testClientName, 'Test client for budget versioning tests');

		await page.close();
	});

	test.beforeEach(async ({ page }) => {
		testDataSetup = new TestDataSetup(page);

		// Sign in with test user
		await testDataSetup.signIn();

		// Navigate to the test client
		await page.goto(
			`/org/${encodeURIComponent(testOrgName)}/clients/${encodeURIComponent(testClientName)}`,
		);
	});

	test('complete budget import workflow with versioning', async ({ page }) => {
		// Create a new project for this test
		const projectName = `Budget Import Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for budget import workflow',
			importFromCostx: false, // We'll test manual import
			stageType: 'icms',
		});

		// Navigate to budget import page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const importUrl = page.url().replace('/overview', '/budget/import');
		await page.goto(importUrl);
		// Ensure client-side hydration completes before interacting with inputs
		try {
			await page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// Ignore timeout errors, we'll check readiness another way
		}

		// Test file upload (create a mock Excel file)
		const XLSX = await import('xlsx');
		const workbook = XLSX.utils.book_new();
		const worksheetData = [
			['Code', 'Description', 'Quantity', 'Unit', 'Unit Rate'],
			['1.1.1', 'Test Item 1', 10, 'each', 100],
			['1.1.2', 'Test Item 2', 5, 'm2', 200],
		];
		const worksheetTotalBudget = worksheetData.slice(1).reduce((acc, row) => {
			const quantity = Number(row[2]) || 0;
			const unitRate = Number(row[4]) || 0;
			return acc + quantity * unitRate;
		}, 0);

		const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, 'Budget');
		const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

		// Target the specific file input for spreadsheets (avoid other file inputs on page)
		const fileInput = page
			.locator(
				'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			)
			.first();
		await expect(fileInput).toBeAttached();
		await expect(fileInput).toBeEnabled();

		console.debug('Excel buffer size:', excelBuffer.length);
		console.debug('Setting input files...');

		// Directly set files on the underlying input without opening OS dialog

		await fileInput.setInputFiles({
			name: 'test-budget.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			buffer: excelBuffer,
		});

		console.debug('Files set, waiting for processing...');

		// Wait for file processing and step transition
		await expect(page.getByText('Step 2: Preview Data')).toBeVisible({ timeout: 15000 });

		// Check browser console for errors (if needed for debugging)

		// Check for either success or error message
		const successMessage = page.getByText('File uploaded successfully');
		const errorMessage = page.getByText(/Failed to parse Excel file/);
		const anyToast = page.locator('[data-sonner-toast]');

		console.debug('Checking for toast messages...');
		const toastCount = await anyToast.count();
		console.debug('Toast count:', toastCount);

		if (toastCount > 0) {
			for (let i = 0; i < toastCount; i++) {
				const toastText = await anyToast.nth(i).textContent();
				console.debug(`Toast ${i}:`, toastText);
			}
		}

		try {
			await expect(successMessage).toBeVisible({ timeout: 10000 });
		} catch (e) {
			// If success message not found, check for error message
			const isErrorVisible = await errorMessage.isVisible();
			if (isErrorVisible) {
				const errorText = await errorMessage.textContent();
				throw new Error(`File upload failed: ${errorText}`);
			}
			throw e; // Re-throw original error if no error message found
		}

		// Proceed through import wizard steps

		// Step 2: Preview Data
		await expect(page.getByText('Step 2: Preview Data')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 3: Map Columns
		await expect(page.getByText('Step 3: Map Columns')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 4: Classify Rows
		await expect(page.getByText('Step 4: Classify Rows')).toBeVisible();
		await page.click('button:has-text("Next")');

		// Step 5: Import (Review and import)
		await expect(page.getByText('Step 5: Review Import')).toBeVisible();

		// Verify preview panel is shown
		await expect(page.getByText('Preview vs Active Budget')).toBeVisible();

		// Check for preview summary chips
		await expect(page.getByText(/Items to Import/)).toBeVisible();
		await expect(page.getByText(/Total Budget/)).toBeVisible();
		await expect(page.getByText(/WBS Levels/)).toBeVisible();

		// Verify diff tabs are present
		await expect(page.getByRole('tab', { name: /Added/ })).toBeVisible();
		await expect(page.getByRole('tab', { name: /Removed/ })).toBeVisible();
		await expect(page.getByRole('tab', { name: /Changed/ })).toBeVisible();

		// Complete the import
		await page.getByRole('button', { name: 'Import' }).click();

		// Verify import success
		await expect(page.getByText(/Budget imported and activated!/)).toBeVisible({ timeout: 30000 });

		// Navigate to budget page to verify version was created
		const budgetUrl = page.url().replace('/budget/import', '/budget');
		await page.goto(budgetUrl);

		// await network idle
		await page.waitForLoadState('networkidle');

		// Verify total budget is displayed
		await expect(page.getByText(formatCurrency(worksheetTotalBudget)).first()).toBeVisible();

		// navigate to budget version history
		await page.locator('#project-menu').click();
		await page.getByRole('link', { name: 'Budget History' }).click();

		// Verify import version is listed
		await expect(page.getByText('test-budget.xlsx').first()).toBeVisible();

		// Test version comparison
		const compareButton = page.getByRole('button', { name: 'Compare to Active' }).first();
		if (await compareButton.isVisible()) {
			await compareButton.click();
			// Verify diff is displayed (implementation depends on UI)
			await page.waitForTimeout(2000); // Allow time for diff to load
		}

		await expect(page.getByText(formatCurrency(worksheetTotalBudget)).first()).toBeVisible();
	});

	test('stage completion creates budget version', async ({ page }) => {
		// Create a new project for this test
		const projectName = `Stage Completion Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for stage completion workflow',
			stageType: 'icms',
		});

		// Navigate to first stage gateway
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const gatewayUrl = page.url().replace('/overview', '/stage-1/gateway');
		await page.goto(gatewayUrl);

		// Ensure the checklist section is visible, then set all statuses to Complete and save
		await page.getByRole('button', { name: /Gateway Checklist/ }).click();
		await page.waitForLoadState('networkidle');

		const statusCombos = page.locator('form[action*="?/updateChecklist"] [role="combobox"]');
		const comboCount = await statusCombos.count();
		for (let i = 0; i < comboCount; i++) {
			await statusCombos.nth(i).click();
			await page.getByRole('option', { name: 'Complete' }).click();
		}

		if (comboCount > 0) {
			await page.getByRole('button', { name: 'Save Checklist Progress' }).click();
			await expect(page.getByText('Checklist updated successfully')).toBeVisible();
		}

		// Check stage readiness
		// await page.getByRole('button', { name: 'Check Completion Readiness' }).click();
		const checkCompletenessButton = page.getByRole('button', {
			name: 'Check Completion Readiness',
		});
		await expect(checkCompletenessButton).toBeVisible();
		await expect(checkCompletenessButton).toBeEnabled();
		await checkCompletenessButton.click();

		await expect(page.getByText('Stage is ready for completion')).toBeVisible();
		const completeStageButton = page.getByRole('button', { name: 'Mark Stage as Complete' });
		await expect(completeStageButton).toBeVisible();
		await expect(completeStageButton).toBeEnabled();

		// Complete the stage
		const notesTextarea = page.locator('textarea[placeholder*="notes"]');
		if (await notesTextarea.isVisible()) {
			await notesTextarea.fill('E2E test stage completion');
		}

		await completeStageButton.click();

		// Verify redirect to project page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+$/, { timeout: 30000 });
		await expect(page.getByText(/Stage completed successfully/)).toBeVisible();

		// Navigate to budget page and check version history
		await page.getByText('Current Budget').click();
		await page.waitForLoadState('networkidle');

		// navigate to budget version history
		await page.locator('#project-menu').click();
		await page.getByRole('link', { name: 'Budget History' }).click();
		await expect(page.getByText(/Stage Completion/)).toBeVisible();
		await expect(page.getByRole('cell', { name: /^stage$/i })).toBeVisible();
	});

	test('version activation workflow', async ({ page }) => {
		// Create a new project for this test
		const projectName = `Version Activation Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for version activation workflow',
			stageType: 'icms',
		});

		// Ensure there's at least one non-active version to activate by creating an import version
		// Navigate to budget import page
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const importUrl = page.url().replace('/overview', '/budget/import');
		await page.goto(importUrl);
		try {
			await page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// ignore networkidle timeouts, continue with readiness checks
		}

		// Prepare a tiny Excel workbook to import and create a new version
		const XLSX = await import('xlsx');
		const workbook = XLSX.utils.book_new();
		const worksheetData = [
			['Code', 'Description', 'Quantity', 'Unit', 'Unit Rate'],
			['1.9.9', 'Activate Flow Item', 1, 'each', 123],
		];
		const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, 'Budget');
		const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

		// Upload the file
		const fileInput = page
			.locator(
				'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			)
			.first();
		await expect(fileInput).toBeAttached();
		await expect(fileInput).toBeEnabled();
		await fileInput.setInputFiles({
			name: 'activate-flow.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			buffer: excelBuffer,
		});

		await expect(page.getByText('Step 2: Preview Data')).toBeVisible({ timeout: 15000 });
		await page.getByRole('button', { name: 'Next' }).click();
		await page.getByRole('button', { name: 'Next' }).click();
		await page.getByRole('button', { name: 'Next' }).click();
		await page.getByRole('button', { name: 'Import' }).click();
		await expect(page.getByText(/Budget imported and activated!/)).toBeVisible({ timeout: 30000 });

		// Navigate to budget version history
		const versionsUrl = page.url().replace('/budget/import', '/budget');
		await page.goto(versionsUrl);
		await page.waitForLoadState('networkidle');

		// navigate to budget version history
		await page.locator('#project-menu').click();
		await page.getByRole('link', { name: 'Budget History' }).click();
		// Wait for table to render and stabilize
		await page.waitForLoadState('networkidle');
		const versionRows = page.locator('table tbody tr');
		await expect(versionRows.first()).toBeVisible();

		// Activate a specific non-active version: the "Initial Budget" system version
		const targetRow = page
			.locator('table tbody tr', {
				has: page.getByText(/^Initial Budget$/),
			})
			.first();
		await expect(targetRow).toBeVisible();
		await targetRow.scrollIntoViewIfNeeded();
		const menuTrigger = targetRow.locator('[data-slot="dropdown-menu-trigger"]').first();
		await expect(menuTrigger).toBeVisible();
		await menuTrigger.click();

		// Select Activate Version from the menu
		const activateOption = page.getByRole('menuitem', { name: /Activate Version/ });
		await expect(activateOption).toBeVisible();
		await activateOption.click();

		// Verify activation success
		await expect(page.getByText(/Version activated/)).toBeVisible({ timeout: 10000 });

		// Verify page reload (budget should reflect new active version)
		await page.waitForLoadState('networkidle');
	});

	test('import duplicate detection', async ({ page }) => {
		// Create a new project for this test
		const projectName = `Import Duplicate Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for import duplicate detection',
			stageType: 'icms',
		});

		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const importUrl = page.url().replace('/overview', '/budget/import');
		await page.goto(importUrl);
		try {
			await page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// ignore
		}
		// Ensure file input is present (may be hidden but attached)
		await page.waitForSelector(
			'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			{ state: 'attached' },
		);

		// Upload the same file twice to test duplicate detection
		const XLSX = await import('xlsx');
		const workbook = XLSX.utils.book_new();
		const worksheetData = [
			['Code', 'Description', 'Quantity', 'Unit', 'Unit Rate'],
			['1.2.1', 'Duplicate Test Item', 8, 'each', 150],
		];
		const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
		XLSX.utils.book_append_sheet(workbook, worksheet, 'Budget');
		const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

		// First import
		const fileInput = page
			.locator(
				'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			)
			.first();
		await expect(fileInput).toBeEnabled();
		await fileInput.setInputFiles({
			name: 'duplicate-test.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			buffer: excelBuffer,
		});

		// Complete first import
		await expect(page.getByText('File uploaded successfully')).toBeVisible({ timeout: 10000 });
		// Preview Data
		await page.getByRole('button', { name: 'Next' }).click();
		// Map Columns
		await page.getByRole('button', { name: 'Next' }).click();
		// Classify Rows
		await page.getByRole('button', { name: 'Next' }).click();
		// Import
		await page.getByRole('button', { name: 'Import' }).click();

		await expect(page.getByText(/Budget imported and activated!/)).toBeVisible({ timeout: 30000 });

		// Start second import with same file
		await page.goto(importUrl);
		try {
			await page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// ignore
		}
		await expect(page.getByRole('heading', { name: 'Import Budget' })).toBeVisible();
		await page.waitForSelector(
			'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			{ state: 'attached' },
		);
		const fileInput2 = page
			.locator(
				'input[type="file"][accept*="openxmlformats-officedocument.spreadsheetml.sheet"], input[type="file"][accept*=".xlsx"]',
			)
			.first();
		await expect(fileInput2).toBeEnabled();
		await fileInput2.setInputFiles({
			name: 'duplicate-test.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			buffer: excelBuffer,
		});

		// Wait for file processing
		await expect(page.getByText('File uploaded successfully')).toBeVisible({ timeout: 10000 });

		// Navigate to review step
		// Preview Data
		await page.getByRole('button', { name: 'Next' }).click();
		// Map Columns
		await page.getByRole('button', { name: 'Next' }).click();
		// Classify Rows
		await page.getByRole('button', { name: 'Next' }).click();
		// Import
		await page.getByRole('button', { name: 'Import' }).click();

		// Verify duplicate detection banner is shown
		await expect(page.getByText(/budget import reused/i)).toBeVisible();
		await expect(page.getByText(/activated existing version/i)).toBeVisible();
	});

	test('version comparison shows detailed diff', async ({ page }) => {
		// Create a new project for this test
		const projectName = `Version Comparison Test Project ${Date.now()}`;
		await testDataSetup.createProject(projectName, {
			description: 'Test project for version comparison workflow',
			stageType: 'icms',
		});

		// Navigate to budget and open version history
		await expect(page).toHaveURL(/\/org\/.+\/projects\/.+\/overview/);
		const budgetUrl = page.url().replace('/overview', '/budget');
		await page.goto(budgetUrl);
		await page.waitForLoadState('networkidle');

		// navigate to budget version history
		await page.locator('#project-menu').click();
		await page.getByRole('link', { name: 'Budget History' }).click();

		// Find and compare versions
		const compareButton = page.getByRole('button', { name: 'Compare to Active' }).first();
		if (await compareButton.isVisible()) {
			await compareButton.click();

			// Wait for comparison to load
			await page.waitForTimeout(3000);

			// Verify diff interface is shown (exact implementation depends on UI)
			// This is a placeholder - actual assertions would depend on the diff UI implementation
			await expect(page.locator('body')).toBeVisible();
		}
	});
});
