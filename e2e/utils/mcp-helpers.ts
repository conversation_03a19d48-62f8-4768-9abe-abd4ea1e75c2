/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { Page, Locator, expect, <PERSON><PERSON> } from '@playwright/test';

/**
 * MCP Helper utilities for Playwright automation
 * These utilities are designed to work well with AI-driven browser automation
 */

/**
 * Test data setup utilities for creating organizations, clients, and projects
 */
export class TestDataSetup {
	constructor(private page: Page) {}

	/**
	 * Create a new organization for testing
	 */
	async createOrganization(orgName: string): Promise<void> {
		await this.page.goto('/org/new');
		await this.page.fill('input[name="name"]', orgName);
		await this.page.click('button[type="submit"]');

		// Wait for redirect to clients page
		await expect(this.page).toHaveURL(/\/org\/.*\/clients/, { timeout: 10000 });
	}

	/**
	 * Create a new client within an organization
	 */
	async createClient(clientName: string, description?: string): Promise<void> {
		// Navigate to new client page
		await this.page.goto(this.page.url().replace(/\/clients.*/, '/clients/new'));

		await this.page.fill('input[name="name"]', clientName);
		if (description) {
			await this.page.fill('textarea[name="description"]', description);
		}
		await this.page.click('button[type="submit"]');

		// Wait for redirect to client page
		await expect(this.page).toHaveURL(/\/org\/.*\/clients\/.*/, { timeout: 10000 });
	}

	/**
	 * Create a new project using the project creation form
	 */
	async createProject(
		projectName: string,
		options?: {
			description?: string;
			importFromCostx?: boolean;
			stageType?: 'icms' | 'riba' | 'custom';
			customStages?: Array<{ name: string; description?: string; isConstruction?: boolean }>;
			wbsLibraryName?: string; // optional override for WBS library selection when not importing
		},
	): Promise<string> {
		// Navigate to new project page from current client page
		const currentUrl = this.page.url();
		const newProjectUrl = currentUrl + '/projects/new';
		await this.page.goto(newProjectUrl);
		try {
			await this.page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			await this.page.waitForLoadState('domcontentloaded');
		}
		await expect(this.page.getByRole('button', { name: 'Create Project' })).toBeVisible();

		// Fill project name
		await this.page.fill('input[name="name"]', projectName);

		// Fill description if provided
		if (options?.description) {
			await this.page.fill('textarea[name="description"]', options.description);
		}

		// Handle CostX import option
		if (options?.importFromCostx) {
			await this.page.check('input[name="import_from_costx"]');
		} else {
			// Try to select the default WBS library (ICMS v3). If the trigger is not reliably found,
			// skip selection and rely on the server default.
			// Open the WBS library select and choose "ICMS v3"
			// Locate the select trigger adjacent to the "WBS Library" label
			const trigger = this.page
				.locator('xpath=//label[normalize-space()="WBS Library"]/following::button[1]')
				.first();
			await expect(trigger).toBeVisible();
			await trigger.click();

			// Select by visible text to avoid role differences
			const libraryToChoose = options?.wbsLibraryName ?? 'ICMS v3';
			const option = this.page.getByText(libraryToChoose, { exact: true }).first();
			await expect(option).toBeVisible();
			await option.click();
		}

		// Handle stage selection
		const stageType = options?.stageType || 'icms';
		if (stageType === 'icms') {
			// ICMS is selected by default, so no need to change anything
			// ICMS stages are selected by default, including Construction stage
		} else if (stageType === 'riba') {
			await this.page.getByRole('radio', { name: 'Standard RIBA Stages' }).check();
			// RIBA stages are selected by default, including Manufacturing and Construction stage
		} else if (stageType === 'custom' && options?.customStages) {
			await this.page.getByRole('radio', { name: 'Custom Stages' }).check();

			// Clear default custom stage
			await this.page.fill('input[name="custom_stages[0].name"]', '');

			// Add custom stages
			for (let i = 0; i < options.customStages.length; i++) {
				const stage = options.customStages[i];

				if (i > 0) {
					// Add new stage
					await this.page.click('button:has-text("Add Stage")');
				}

				await this.page.fill(`input[name="custom_stages[${i}].name"]`, stage.name);
				if (stage.description) {
					await this.page.fill(
						`textarea[name="custom_stages[${i}].description"]`,
						stage.description,
					);
				}
			}

			// Select construction stage
			const constructionIndex = options.customStages.findIndex((s) => s.isConstruction);
			if (constructionIndex >= 0) {
				await this.page.check(`input[value="${constructionIndex}"]`);
			}
		}

		// Submit the form
		await this.page.getByRole('button', { name: 'Create Project' }).click();

		// Wait for redirect to concrete project page (not /projects/new)
		await this.page.waitForURL(/\/org\/.*\/projects\/(?!new($|\?)).+/, { timeout: 20000 });

		// Extract project ID from URL
		const url = this.page.url();
		const projectIdMatch = url.match(/\/projects\/([^/]+)/);
		if (!projectIdMatch) {
			throw new Error('Could not extract project ID from URL');
		}

		return projectIdMatch[1];
	}

	/**
	 * Sign in with test user
	 */
	async signIn(
		email: string = '<EMAIL>',
		password: string = 'testtest',
	): Promise<void> {
		await this.page.goto('/auth/signin');
		await this.page.fill('input[name="email"]', email);
		await this.page.fill('input[name="password"]', password);
		await this.page.click('button:has-text("Sign In")');
		await this.page.waitForURL(/\/|\/org\/.*\/clients|\/org\/new/, { timeout: 10000 });
	}
}

export class MCPHelpers {
	constructor(private page: Page) {}

	/**
	 * Wait for page to be fully loaded and interactive
	 */
	async waitForPageReady(): Promise<void> {
		try {
			// Try networkidle first, but with a shorter timeout
			await this.page.waitForLoadState('networkidle', { timeout: 10000 });
		} catch {
			// If networkidle fails (e.g., due to hot reloading), fall back to domcontentloaded
			await this.page.waitForLoadState('domcontentloaded');
		}
		await this.page.waitForFunction(() => document.readyState === 'complete');
	}

	/**
	 * Take a screenshot with timestamp for debugging
	 */
	async takeTimestampedScreenshot(name: string): Promise<void> {
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		await this.page.screenshot({
			path: `test-results-mcp/screenshots/${name}-${timestamp}.png`,
			fullPage: true,
		});
	}

	/**
	 * Get detailed page information for MCP context
	 */
	async getPageContext(): Promise<{
		url: string;
		title: string;
		viewport: { width: number; height: number };
		userAgent: string;
		cookies: Array<Cookie>;
		localStorage: Record<string, string>;
	}> {
		const url = this.page.url();
		const title = await this.page.title();
		const viewport = this.page.viewportSize() || { width: 0, height: 0 };
		const userAgent = await this.page.evaluate(() => navigator.userAgent);
		const cookies = await this.page.context().cookies();

		const localStorage = await this.page.evaluate(() => {
			const storage: Record<string, string> = {};
			for (let i = 0; i < window.localStorage.length; i++) {
				const key = window.localStorage.key(i);
				if (key) {
					storage[key] = window.localStorage.getItem(key) || '';
				}
			}
			return storage;
		});

		return { url, title, viewport, userAgent, cookies, localStorage };
	}

	/**
	 * Smart element finder that tries multiple strategies
	 */
	async findElement(selector: string): Promise<Locator | null> {
		// Try exact selector first - if multiple elements, return the first one
		let element = this.page.locator(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as text content
		element = this.page.getByText(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as placeholder
		element = this.page.getByPlaceholder(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as label
		element = this.page.getByLabel(selector);
		if ((await element.count()) > 0) {
			return element.first();
		}

		// Try as role
		try {
			element = this.page.getByRole('button', { name: selector });
			if ((await element.count()) > 0) {
				return element.first();
			}
		} catch {
			// Ignore role errors
		}

		return null;
	}

	/**
	 * Smart click that waits for element and handles different scenarios
	 */
	async smartClick(selector: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.waitFor({ state: 'visible' });
			await element.click();
			return true;
		} catch (error) {
			console.warn(`Failed to click element: ${selector}`, error);
			return false;
		}
	}

	/**
	 * Smart type that handles different input types
	 */
	async smartType(selector: string, text: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.waitFor({ state: 'visible' });
			await element.clear();
			await element.fill(text);
			return true;
		} catch (error) {
			console.warn(`Failed to type in element: ${selector}`, error);
			return false;
		}
	}

	/**
	 * Get all interactive elements on the page
	 */
	async getInteractiveElements(): Promise<
		Array<{
			tagName: string;
			text: string;
			selector: string;
			type?: string;
			href?: string;
		}>
	> {
		return await this.page.evaluate(() => {
			const elements = document.querySelectorAll(
				'button, input, select, textarea, a[href], [onclick], [role="button"]',
			);

			return Array.from(elements)
				.map((el, index) => {
					const rect = el.getBoundingClientRect();
					const isVisible =
						rect.width > 0 &&
						rect.height > 0 &&
						window.getComputedStyle(el).visibility !== 'hidden';

					if (!isVisible) return null;

					return {
						tagName: el.tagName.toLowerCase(),
						text: el.textContent?.trim() || '',
						selector: `${el.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
						type: (el as HTMLInputElement).type || undefined,
						href: (el as HTMLAnchorElement).href || undefined,
					};
				})
				.filter((item): item is NonNullable<typeof item> => item !== null);
		});
	}

	/**
	 * Wait for navigation with better error handling
	 */
	async waitForNavigation(timeout: number = 30000): Promise<void> {
		try {
			await this.page.waitForLoadState('networkidle', { timeout });
		} catch (error) {
			console.warn('Navigation timeout, continuing...', error);
		}
	}

	/**
	 * Scroll element into view and ensure it's visible
	 */
	async scrollToElement(selector: string): Promise<boolean> {
		const element = await this.findElement(selector);
		if (!element) {
			return false;
		}

		try {
			await element.scrollIntoViewIfNeeded();
			await element.waitFor({ state: 'visible' });
			return true;
		} catch (error) {
			console.warn(`Failed to scroll to element: ${selector}`, error);
			return false;
		}
	}
}
