import { expect, test } from '@playwright/test';

test.describe('Auth Edge Cases E2E Tests', () => {
	// Generate unique test identifiers
	const timestamp = Date.now();
	const testEmail = `test-${timestamp}@example.com`;
	const password = 'TestPassword123';

	test('should handle concurrent login attempts gracefully', async ({ browser }) => {
		// Create multiple browser contexts for concurrent operations
		const contexts = await Promise.all([
			browser.newContext(),
			browser.newContext(),
			browser.newContext(),
		]);

		try {
			// Create pages from each context
			const pages = await Promise.all(contexts.map((context) => context.newPage()));

			// First, create a test user
			await pages[0].goto('/auth/signup');
			await pages[0].fill('input[name="email"]', testEmail);
			await pages[0].fill('input[name="password"]', password);
			await pages[0].click('button[type="submit"]');

			// Wait for signup to complete (either redirected or success message)
			await Promise.race([
				pages[0].waitForURL(/\//, { timeout: 5000 }),
				pages[0].waitForSelector('text=Welcome to ', { timeout: 5000 }),
				pages[0].waitForURL(/\/org\/new/, { timeout: 5000 }),
			]);

			// If redirected to org creation, wait for that to complete
			if (pages[0].url().includes('/org/new')) {
				await pages[0].fill('input[name="name"]', `Test Org ${timestamp}`);
				await pages[0].click('button[type="submit"]');
				await pages[0].waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });
			}

			// Sign out if possible
			try {
				await pages[0].locator('#profile-button').click();
				await pages[0].getByText('Sign out').click();

				await pages[0].waitForURL(/\/auth\//, { timeout: 5000 });
			} catch (_e) {
				// Ignore if signout not possible or doesn't redirect
			}

			// Now attempt to login concurrently from all pages
			await Promise.all(
				pages.map(async (page) => {
					await page.goto('/auth/signin');
					await page.fill('input[name="email"]', testEmail);
					await page.fill('input[name="password"]', password);
					await page.click('button[type="submit"]');
				}),
			);

			// Verify that all login attempts eventually succeed
			// Note: In some systems there might be rate limiting or session conflicts
			// but the application should handle these gracefully without crashing
			for (const page of pages) {
				try {
					// Wait for success (redirect to home or organization page)
					await page.waitForURL(/\/|\/org\/.*\/clients|\/org\/new/, { timeout: 10000 });

					// Verify user appears to be logged in (e.g. by checking for profile elements)
					const userMenu = page.locator('#profile-button');
					await expect(userMenu).toBeVisible({ timeout: 5000 });
				} catch (e) {
					console.debug('Login failed for page:', page.url(), { e });
					// If one login failed, that's acceptable as long as the application didn't crash
					// The important thing is the application should remain responsive
					await expect(page.getByText(/error|try again/i)).toBeVisible();
				}
			}
		} finally {
			// Clean up contexts
			await Promise.all(contexts.map((context) => context.close()));
		}
	});

	test('should handle invalid auth tokens gracefully', async ({ page }) => {
		// This test simulates an invalid auth token situation

		// First, login normally
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for successful login
		await page.waitForURL(/\/org\/new|\/org\/.*\/clients/, { timeout: 5000 });

		// Now simulate an invalid token by clearing cookies but keeping localStorage
		// (this creates a situation where the frontend thinks it's logged in but the token is invalid)
		await page.context().clearCookies();

		// Now navigate to a protected route
		await page.goto('/org/new');

		// The app should gracefully handle this by either:
		// 1. Redirecting to signup page
		// 2. Showing an auth error message
		// 3. Attempting to refresh the token and then falling back to login

		// Check if redirected to signup
		const isRedirectedToSignUp = page.url().includes('/auth/signin');

		// Or check if an auth error message is shown
		const hasAuthError = await page.getByRole('heading', { name: 'Sign Up' }).isVisible();

		// Either condition indicates graceful handling
		expect(isRedirectedToSignUp || hasAuthError).toBeTruthy();
	});

	test('should prevent access with malformed credentials', async ({ page }) => {
		// Test with various malformed inputs
		const malformedInputs = [
			{ email: "' OR 1=1 --", password: 'password' }, // SQL injection attempt
			{ email: '<script>alert(1)</script>@example.com', password: 'password' }, // XSS attempt
			{ email: '<EMAIL>', password: "' OR 1=1 --" }, // SQL injection in password
			{ email: "user@' UNION SELECT 1,2,3 --", password: 'password' }, // Union injection attempt
			{ email: '\<EMAIL>', password: 'password' }, // Null byte injection
		];

		for (const input of malformedInputs) {
			await page.goto('/auth/signin');

			// Fill with malformed input
			await page.fill('input[name="email"]', input.email);
			await page.fill('input[name="password"]', input.password);
			await page.click('button[type="submit"]');

			// The application should either:
			// 1. Show a validation error
			// 2. Show a login failure message
			// 3. Not redirect to authenticated pages

			// Check for validation error or login failure message
			const hasError = await Promise.race([
				page
					.getByText(/invalid|failed|incorrect|error/i)
					.isVisible({ timeout: 3000 })
					.catch(() => false),
				page
					.waitForURL(/\/auth\//, { timeout: 3000 })
					.then(() => true)
					.catch(() => false),
			]);

			// We should not be redirected to authenticated pages
			const isAuthenticated = page.url().includes('/org') || page.url().includes('/wbs-libraries');

			expect(hasError).toBeTruthy();
			expect(isAuthenticated).toBeFalsy();
		}
	});

	test.skip('should handle network disruptions during auth', async ({ page }) => {
		await page.goto('/auth/signin');

		// Fill in credentials
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', password);

		// Setup offline mode before submitting
		await page.context().setOffline(true);

		// Submit the form
		await page.click('button[type="submit"]');

		// Application should show a network error
		await expect(page.getByText(/network|connection|offline|internet/i)).toBeVisible({
			timeout: 5000,
		});

		// Return to online mode
		await page.context().setOffline(false);

		// Try again - this time it should work
		await page.click('button[type="submit"]');

		// Wait for successful login
		await Promise.race([
			page.waitForURL(/^(\/|\/auth\/invite\/.*|\/org\/new)$/, { timeout: 5000 }),
			page.waitForSelector('text=/welcome|success/', { timeout: 5000 }),
		]);
	});
});
