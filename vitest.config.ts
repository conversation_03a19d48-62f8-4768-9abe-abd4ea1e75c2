import { defineConfig } from 'vitest/config';
import { storybookTest } from '@storybook/addon-vitest/vitest-plugin';
import { sveltekit } from '@sveltejs/kit/vite';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const dirname =
	typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
	plugins: [
		sveltekit(),
		storybookTest({
			// The location of your Storybook config, main.js|ts
			configDir: path.join(dirname, '.storybook'),
			// This should match your package.json script to run Storybook
			// The --ci flag will skip prompts and not open a browser
			storybookScript: 'pnpm storybook --ci',
		}),
	],
	test: {
		projects: [
			{
				extends: './vite.config.test.ts', // Use test-specific config without Sentry
				test: {
					name: 'client',
					environment: 'browser',
					browser: {
						enabled: true,
						provider: 'playwright',
						instances: [{ browser: 'chromium' }],
					},
					include: ['src/**/*.svelte.{test,spec}.{js,ts}'],
					exclude: ['src/lib/server/**', 'src/**/*.ssr.{test,spec}.{js,ts}'],
					setupFiles: ['./src/vitest-setup-client.ts'],
				},
			},
			{
				extends: './vite.config.test.ts', // Use test-specific config without Sentry
				test: {
					name: 'ssr',
					environment: 'node',
					include: ['src/**/*.ssr.{test,spec}.{js,ts}'],
					setupFiles: ['./src/vitest-setup-server.ts'],
				},
			},
			{
				extends: './vite.config.test.ts', // Use test-specific config without Sentry
				test: {
					name: 'server',
					environment: 'node',
					include: ['src/**/*.{test,spec}.{js,ts}'],
					exclude: ['src/**/*.svelte.{test,spec}.{js,ts}', 'src/**/*.ssr.{test,spec}.{js,ts}'],
					setupFiles: ['./src/vitest-setup-server.ts'],
				},
			},
			{
				extends: './vite.config.test.ts', // Use test-specific config without Sentry
				test: {
					name: 'storybook',
					browser: {
						enabled: true,
						provider: 'playwright',
						headless: true,
						instances: [{ browser: 'chromium' }],
					},
					setupFiles: ['./.storybook/vitest.setup.ts'],
					exclude: [
						'e2e/**/*.test.ts',
						'e2e/**/*.spec.ts',
						'**/integration/**/*.spec.ts',
						'**/performance/**/*.spec.ts',
						'**/*server.spec.ts',
						'**/*server.test.ts',
						'**/node_modules/**',
						'**/dist/**',
						'**/.{idea,git,cache,output,temp}/**',
						'**/vite.config*.ts',
						'**/vitest.config*.ts',
					],
				},
			},
		],
	},
});
