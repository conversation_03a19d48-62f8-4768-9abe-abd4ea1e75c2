# Budget Versioning Upgrade Plan & Implementation

> **Implementation-Ready Document**: This document combines strategic planning with detailed implementation specifications. Use this as the single source of truth for the budget versioning upgrade.

## Executive Summary

This upgrade introduces immutable budget versions with an active pointer per project, enabling "replace-on-import" with full auditability and fast undo. Because there is no production data to preserve, we will use an aggressive rollout without dual-write, feature flags, or compatibility views.

### Key Benefits

- **Immutable versions**: Every budget state is preserved as a version
- **Replace-on-import**: Create new version while keeping pre-import state for undo
- **Minimal disruption**: Direct integration without views or dual-write
- **Custom WBS support**: Full integration with existing custom WBS functionality
- **Performance**: Optimized for 5k-20k budget items per project

### Decisions Made

- ✅ **Aggressive rollout**: No dual-write and no feature flags
- ✅ **No compatibility views**: Do not replace table with a view after soak
- ✅ **Snapshot linking now**: Add `budget_snapshot.budget_version_id` now and backfill; enforcement level TBD after backfill
- ✅ **Version lineage**: Include `prev_version_id` for audit trail and undo
- ✅ **Security**: All RPCs are `SECURITY INVOKER` and must error when `auth.uid()` is NULL unless explicitly justified
- ✅ **Active pointer semantics**: `project.active_budget_version_id` points to the in-progress version for pre-construction; becomes immutable in construction (changes via `risk_register` and `approved_change` only)
- ✅ **RLS first**: Prefer preserving RLS over performance; materialized views considered only if security is preserved
- ✅ **Import idempotency**: `budget_import.source_hash` uniqueness is per-project
- ✅ **Version items schema**: `budget_version_item` mirrors all columns from `budget_line_item_current`; no new denormalized totals
- ✅ **Backfill**: Create backfilled versions with `kind='stage'` and set active pointers for all projects (low priority but keeps demo data usable)

## Implementation Checklist

### Phase 1: Schema & Database Setup

Focus on the schemas and do not create or edit migration files. Generate a migration after schema changes should be rolled out by running `supabase db diff -f <description>`. New schema files must be included in `supabase/config.toml` in the exact processing order to be included in a migration.

- [x] **Create budget versioning tables**
  - [x] Create `budget_version_kind` enum: `('stage', 'import', 'manual', 'system')`
  - [x] Create `budget_version` table with constraints and indexes
  - [x] Create `budget_version_item` table mirroring `budget_line_item_current`
  - [x] Create `budget_import` table with per-project idempotency
- [x] **Update existing tables**
  - [x] Add `active_budget_version_id` to `project` table (nullable FK)
  - [x] Initialize `active_budget_version_id` for all projects (via backfill)
  - [x] Add `budget_version_id` to `budget_snapshot` table now and plan backfill
- [x] **Create performance indexes** (see indexing guidance)
- [x] **Add comprehensive RLS policies** mirroring current rules for active stage versions
- [x] **Run backfill migration** to create bootstrap versions (`kind='stage'`) from existing data (low priority)
  - Implemented as manual migration: `supabase/migrations/20250829145500_budget_version_backfill.sql`
- [x] **Update supabase/config.toml** with new table ordering

### Phase 2: RPC Functions (No Dual-Write)

- [x] **Core version management RPCs**
  - [x] Implement `create_budget_version()` (clone active by default; empty for imports)
  - [x] Implement `activate_budget_version()` with per-project concurrency lock
  - [x] Implement `list_budget_versions()` (newest first; include cached aggregates)
  - [x] Implement `diff_budget_versions()` (returns `BudgetVersionDiff` JSON)
- [x] **Enhanced import system**
  - [x] Implement `apply_budget_import()` starting from a new empty version; if `(project_id, source_hash)` hit exists, reuse that version
  - [x] Keep existing project-scoped custom WBS creation; on undo, remove created custom WBS items (undo removal to be completed when undo RPC is added)
- [x] **Set permissions and security** for all new RPCs (error if `auth.uid()` is NULL)

Note: RLS policies now explicitly allow inserts/updates to `budget_version` and `budget_version_item` for import/manual kinds when the user can modify the project. Accordingly, `create_budget_version()` and `apply_budget_import()` are implemented as `SECURITY INVOKER` and enforce permission checks (`auth.uid()`, `can_modify_project`, `can_access_project`).

### Phase 3: Read Path Migration

- [x] **Read path migration completed** - Application now reads from `budget_version`/`budget_version_item` via `get_active_budget_version_items()` RPC function with RLS-preserving policies

### Phase 4: Frontend Integration & Import UI

- [x] **Backend service updates** for new import system
- [x] Implement small "undo import" RPC to revert `project.active_budget_version_id` to the import's `pre_version_id`; optionally remove custom WBS items created by that import
- [x] **TypeScript interface definitions** for budget version diff data structures
- [x] **Frontend type safety** - replaced all `any` types with proper interfaces for `BudgetVersionDiff`, `BudgetVersionDiffSummary`, `BudgetVersionDiffItem`, and `TelemetryData`
- [x] **Icon updates** - replaced deprecated Lucide icons (`AlertTriangle` → `TriangleAlert`, `CheckCircle2` → `CircleCheck`)
- [x] **Code cleanup** - removed unused variables and improved type casting safety
- [x] **Enhanced import UX** with preview and diff display
- [x] **Analytics** for import operations
- [x] **Import preview functionality** - implemented `diff_active_vs_items()` and `find_existing_import()` RPCs
- [x] **Duplicate detection** - UI shows banner for existing imports with reuse option
- [x] **Pre-flight diff display** - shows added/removed/changed items with cost delta before import

#### Enhanced Import UX: Preview + Diff

Goal: before committing an import, show users exactly how the imported items will change the active budget. This includes duplicate detection, a summarized delta, and an inspectable diff of added/removed/changed line items.

Key behaviors

- Duplicate detection: compute a deterministic `source_hash` client‑side and check for an existing `(project_id, source_hash)` import. If found, display a non‑blocking banner with link to the existing version and allow “Reuse existing version” or “Continue anyway”.
- Pre‑flight diff: compute a diff between the current active version and the import payload prior to applying. Render a summary (counts + cost delta) and an inspectable list grouped by Added/Removed/Changed.
- Large data handling: virtualize long lists, collapse sections by default, and show Top N with “Load more” to keep the UI responsive on 10k–20k items.
- Finalization: on Confirm, call `apply_budget_import` to persist the version and activate it. After success, surface a link to “Compare new version to previous” using the existing Version History dialog.

UI placement (Import Wizard)

- Step 5 “Review Import” gains a right‑side panel (or stacked section on mobile) titled “Preview vs Active” containing:
  - Duplicate banner (when applicable), showing filename and created‑at of the prior import and a “Reuse” action.
  - Summary chips: Items Added, Items Removed, Items Changed, and Net Cost Delta (green/red depending on sign).
  - Diff viewer: tabs for Added, Removed, Changed. Each list row shows `code`, `description`, and key numeric fields. For Changed, show compact per‑field deltas (e.g., `quantity: 10 → 12`, `unit_rate: 15.25 → 16.00`).
  - Controls: search by code/description, “Show only increases/decreases” filters, and an optional “Label this version” input.
  - Footer: primary “Import and Activate” and secondary “Import (keep inactive)” if we later support non‑activating imports.

Backend support

- New RPC: `diff_active_vs_items(p_project_id uuid, p_items jsonb)` → `jsonb`
  - Purpose: return a `BudgetVersionDiff` without persisting anything. Joins the active version’s items to the provided payload by WBS code for comparison.
  - Matching strategy: join active `budget_version_item` to `wbs_library_item` to obtain `code`, then compare by `code` to the incoming `p_items[*].code`. Treat unknown codes as “Added”; codes not present in the import as “Removed”.
  - Output shape: identical to `diff_budget_versions` (see JSON shape below), with `wbs_library_item_id` omitted for items that do not yet exist; include an extra array `new_wbs_codes: string[]` listing codes that would be created (including any missing parents) during a real import.
  - Permissions: `SECURITY INVOKER`. Require `auth.uid()` and `can_access_project(p_project_id)`.

- Idempotency pre‑check: the UI already computes `source_hash` prior to import. Add lightweight RPC `find_existing_import(p_project_id uuid, p_source_hash text)` returning `{ exists: boolean, budget_import_id?: uuid, version_id?: uuid, created_at?: timestamptz, filename?: text }` to power the duplicate banner without running a diff.

Performance & UX safeguards

- Limit returned diff arrays to 1,000 items per category by default. Include counts for total matches and implement “Load more” via cursor params if needed (`offset/limit`), or show a note: “Showing first 1,000. Narrow results with search.”
- Render totals and cost delta first; lazy‑load detail lists after summary paints. Debounce diff calls while users edit mappings/classifications to avoid thrash.
- Fall back strategy: if the preview call errors, show a soft warning “Preview unavailable; you can still import” and keep the Import button enabled.

Telemetry

- `budget_import_preview_shown`: project, counts (added/removed/changed), cost delta, duration_ms.
- `budget_import_duplicate_detected`: project, existing_import_id/version_id, filename.
- `budget_import_preview_failed`: project, error_code/message, duration_ms.

Validation tie‑ins

- Reuse existing validation from Import Summary. Block the Import button only on validation errors; preview warnings never block.
- Highlight potentially risky changes in the Changed tab (e.g., negative quantity/rate deltas) with a caution icon.

Post‑import affordance

- After success, show a toast with counts and cost delta. Include “View Diff” action that opens Version History pre‑filtered to the latest version and auto‑runs Compare vs previous.

### Phase 5: Stage Integration

- [x] **Stage completion integration** to tag versions - implemented in `complete_project_stage()` RPC with automatic budget version creation
- [x] **Gateway workflow validation** ensuring no UI changes - gateway workflow continues to work seamlessly with budget versioning
- [x] **Version lineage for stages** through gate progression - stage versions are linked via `prev_version_id` and `stage_id` fields

### Phase 6: Version Management UI

- [x] **Version list interface** with history and quick actions - implemented in `BudgetVersionManager.svelte`
- [x] **Version comparison interface** with detailed diffs - implemented with `diff_budget_versions()` RPC
- [x] **Version management controls** for activation and labeling - activation and undo functionality complete
- [x] **Import workflow improvements** with preview capabilities - enhanced import UX with preview and diff display

### Phase 7: Testing & Validation

- [x] **Unit tests** for all RPC functions and edge cases - implemented in `src/tests/unit/budget-versioning/`
- [x] **Integration tests** for cross-system functionality - implemented in `src/tests/integration/budget-versioning/`
- [x] **End-to-end tests** for complete user workflows - implemented in `e2e/budget-versioning/`
- [x] **Performance tests** with large datasets - implemented in `src/tests/performance/budget-versioning-performance.spec.ts`

### Phase 8: Production Rollout

- [x] **Pre-deployment preparation** with concise runbooks and monitoring - CI/CD pipelines configured, staging/production workflows in place
- [x] **Immediate cutover** (no dual-write, no feature flags) - system is ready for production deployment
- [ ] **Monitoring and optimization** after rollout - ready for post-deployment monitoring
- [ ] **Cleanup and finalization** - ready for final cleanup tasks

### Operational Considerations

- [x] **Concurrency and safety** with advisory locks - implemented in `activate_budget_version()` and `undo_budget_import()` RPCs
- [x] **Observability** with structured logging and metrics - Pino logging, PostHog analytics, and Sentry error tracking in place
- [x] **Data management** and retention policies - audit tables implemented with triggers for budget_version and budget_import
- [x] **Performance monitoring** and optimization - performance tests implemented, query optimization in place

## Technical Implementation Details

### Key Database Tables

#### budget_version

```sql
CREATE TYPE budget_version_kind AS ENUM ('stage', 'import', 'manual', 'system');

CREATE TABLE budget_version (
	budget_version_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	project_id uuid NOT NULL REFERENCES project (project_id),
	label text,
	kind budget_version_kind NOT NULL,
	stage_id uuid REFERENCES project_stage (project_stage_id),
	prev_version_id uuid REFERENCES budget_version (budget_version_id),
	created_by_user_id uuid NOT NULL REFERENCES profile (user_id),
	created_at timestamptz DEFAULT now (),
	updated_at timestamptz DEFAULT now (),
	CONSTRAINT stage_consistency CHECK (
		(
			kind = 'stage'
			AND stage_id IS NOT NULL
		)
		OR (kind != 'stage')
	),
	CONSTRAINT no_self_reference CHECK (budget_version_id != prev_version_id)
);
```

#### budget_version_item

```sql
CREATE TABLE budget_version_item (
	budget_version_item_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	budget_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id) ON DELETE CASCADE,
	wbs_library_item_id uuid NOT NULL REFERENCES wbs_library_item (wbs_library_item_id),
	-- Mirror all columns from budget_line_item_current 1:1 (no extra denormalized totals)
	-- e.g., quantity, unit, material_rate, labor_rate, productivity_per_hour,
	-- unit_rate_manual_override, unit_rate, factor, remarks, cost_certainty, design_certainty, etc.
	created_at timestamptz DEFAULT now (),
	UNIQUE (budget_version_id, wbs_library_item_id)
);
```

#### budget_import

```sql
CREATE TABLE budget_import (
	budget_import_id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
	project_id uuid NOT NULL REFERENCES project (project_id),
	source_filename text NOT NULL,
	source_hash text NOT NULL,
	pre_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id),
	new_version_id uuid NOT NULL REFERENCES budget_version (budget_version_id),
	is_undone boolean DEFAULT false,
	undone_at timestamptz,
	undone_by_user_id uuid REFERENCES profile (user_id),
	created_by_user_id uuid NOT NULL REFERENCES profile (user_id),
	created_at timestamptz DEFAULT now (),
	UNIQUE (project_id, source_hash) -- idempotency scoped per project
);
```

### Key RPC Functions

```sql
-- Create new budget version
CREATE FUNCTION create_budget_version (
	p_project_id uuid,
	p_label text DEFAULT NULL,
	p_kind budget_version_kind DEFAULT 'manual',
	p_stage_id uuid DEFAULT NULL,
	p_prev_version_id uuid DEFAULT NULL
) RETURNS uuid LANGUAGE plpgsql SECURITY INVOKER;

-- Apply budget import with full error handling
CREATE FUNCTION apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_source_hash text DEFAULT NULL,
	p_items jsonb,
	p_notes text DEFAULT NULL
) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER;

-- Activate a budget version
CREATE FUNCTION activate_budget_version (p_version_id uuid, p_reason text DEFAULT NULL) RETURNS boolean LANGUAGE plpgsql SECURITY INVOKER;

-- List versions with pagination
CREATE FUNCTION list_budget_versions (
	p_project_id uuid,
	p_limit integer DEFAULT 50,
	p_cursor timestamptz DEFAULT NULL
) RETURNS TABLE (
	budget_version_id uuid,
	label text,
	kind budget_version_kind,
	is_active boolean,
	item_count bigint,
	total_cost numeric,
	created_at timestamptz
	-- ... additional fields
) LANGUAGE plpgsql SECURITY INVOKER;

-- Compare two versions
CREATE FUNCTION diff_budget_versions (p_version_a uuid, p_version_b uuid) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER;

-- Detailed diff with added/removed/changed items
-- Preview an import payload against the active version without persisting
CREATE FUNCTION diff_active_vs_items (p_project_id uuid, p_items jsonb) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER;

-- Lightweight duplicate check for idempotent imports
CREATE FUNCTION find_existing_import (p_project_id uuid, p_source_hash text) RETURNS TABLE (
	exists boolean,
	budget_import_id uuid,
	version_id uuid,
	created_at timestamptz,
	filename text
) LANGUAGE sql SECURITY INVOKER;
```

### RLS Policies

- Mirror existing `budget_line_item_current` rules: viewers can read; editors can modify.
- Apply these rules only to the current active budget where `budget_version.kind = 'stage'` and `budget_version.budget_version_id = project.active_budget_version_id`.
- For non-active versions or versions with other kinds, allow read-only access.
- All RPCs must use `SECURITY INVOKER` and should `RAISE EXCEPTION` when `auth.uid()` is NULL; fall back to system user only where explicitly required and consistent with existing patterns.

## Migration Strategy

### Phase 1: Schema Migration

1. Deploy new tables with indexes and constraints
2. Add `project.active_budget_version_id` and initialize for all projects
3. Add `budget_snapshot.budget_version_id` and plan backfill
4. Run backfill (low priority) to create bootstrap versions with `kind='stage'` for demo data

### Phase 2: Write Path Cutover

1. Update write paths to operate only on `budget_version`/`budget_version_item`
2. Keep reads as-is for now (no view replacement). If/when needed, update application reads directly to new tables.
3. Monitor performance and RLS behavior

### Phase 3: Read Migration (Optional / TBD)

1. If required, update application code to read from `budget_version`/`budget_version_item` for the active version only
2. Validate performance and RLS

### Phase 4: Cleanup

1. Remove any dead code and legacy references
2. Finalize indexes and constraints post-backfill

## Success Metrics

- **Migration Success**: 100% of projects migrated with data integrity
- **Performance**: Import <30s for 10k items, <60s for 20k items
- **Reliability**: <0.1% error rate for imports and version operations
- **User Adoption**: >80% using new system within 30 days

## Risk Mitigation

- **Data Loss**: Complete validation and rollback procedures
- **Performance**: Load testing and query optimization
- **User Disruption**: Aggressive cutover with limited demo data; clear rollback plan
- **Concurrency**: Advisory locks and testing
- **Rollback**: Multiple rollback strategies at each phase

---

_This document serves as the complete implementation guide. All developers should reference this single document for the budget versioning upgrade project._

---

## Additional Specifications

### Active Budget Pointer Semantics

- `project.active_budget_version_id` must always reference the in-progress version for pre-construction stages.
- When a project enters the construction stage, the active budget becomes immutable; all modifications must occur via `risk_register` and `approved_change` flows.

### Budget Snapshot Linking

- Add `budget_snapshot.budget_version_id` now; backfill existing snapshots.
- Backfill method TBD; recommendation: correlate snapshots by timestamp to the nearest earlier active version per project, or maintain explicit linkage during snapshot creation going forward.

### RPC Semantics

- `create_budget_version()`
  - Clone from the active version by default.
  - When `kind='import'`, create an empty version.
  - Attribution: `created_by_user_id = auth.uid()` with fallback to system user per existing patterns.
- `apply_budget_import()`
  - Start from a new empty budget version.
  - If `(project_id, source_hash)` already exists, reuse that version instead of creating a new one.
  - Maintain existing per-project custom WBS creation; on undo, remove created custom WBS items.
  - On undo, attribute `undone_by_user_id = auth.uid()` with system fallback as needed.
- `activate_budget_version()`
  - Use a per-project concurrency lock (e.g., `pg_advisory_xact_lock` keyed by project) to serialize activations.
- `list_budget_versions()`
  - Sort newest first (`created_at DESC`).
  - Include total items and sum from a cached aggregation (see Aggregations & Indexes).
- `diff_budget_versions()`
  - Returns JSON with keys:
    - `added`: items present only in B; each has `wbs_library_item_id` and fields `quantity, unit, material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override, unit_rate, factor, remarks, cost_certainty, design_certainty`.
    - `removed`: items present only in A; same fields as `added`.
    - `changed`: items present in both with any differing field; each entry has `wbs_library_item_id` and `diff` object: `{ from: { ...fields }, to: { ...fields } }`.
    - `summary`: `{ version_a, version_b, total_cost_a, total_cost_b, total_cost_delta }`.

### RLS Details

- Editors can modify only where `budget_version.kind='stage'` and the version is active for the project.
- Viewers can read all versions; edits blocked on non-active versions and non-`stage` kinds.
- All RPCs are `SECURITY INVOKER` and should `RAISE EXCEPTION` when `auth.uid()` is NULL.

### Aggregations & Indexes

- Prefer preserving RLS even if performance costs increase.
- Consider a materialized view only if RLS can be preserved; otherwise, maintain a per-version summary table updated by triggers/worker under `SECURITY INVOKER`.
- Recommended indexes:
  - `budget_version (project_id, created_at DESC)`
  - `budget_version (kind, project_id)`
  - `budget_version (prev_version_id)`
  - `budget_version (stage_id)`
  - `budget_version_item (budget_version_id)`
  - `budget_version_item (wbs_library_item_id)`
  - Unique: `(budget_version_id, wbs_library_item_id)`
  - `budget_import (project_id, source_hash)` unique
  - `project (active_budget_version_id)`
  - `budget_snapshot (budget_version_id)`

### Concurrency Locking

- Use a per-project advisory lock in `activate_budget_version()` to prevent races, e.g.:

```sql
PERFORM pg_advisory_xact_lock(
  hashtextextended('activate_budget_version', 0),
  hashtextextended(p_project_id::text, 0)
);
```

### Version Labeling

- Keep labeling simple and human-friendly for now; can evolve later.

### Version History UX

- Show all human-readable fields; avoid exposing UUIDs.
- Provide links to diff against the active version.
- Undo UX not required at this time.

### Audit Logging

- Add audit tables for new budget version tables to record who made changes (no need to duplicate full budget line items in audit records).
- Suggested approach:
  - `budget_version_audit(budget_version_id, action, changed_by_user_id, changed_at, notes jsonb)`
  - `budget_import_audit(budget_import_id, action, changed_by_user_id, changed_at, notes jsonb)`
  - For `budget_version_item` changes, capture minimal events (item id, action, who/when), not full row copies.

### Import Payload

- No changes required to the `import_budget_data` JSON shape.

### Backfill Defaults

- Backfilled versions should use `kind='stage'`.
- Set the active pointer for all projects; there are no archived projects (production contains only demo projects).
