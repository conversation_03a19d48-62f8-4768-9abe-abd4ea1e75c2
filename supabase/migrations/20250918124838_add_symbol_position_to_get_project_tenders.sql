drop function if exists "public"."get_project_tenders" (
	project_id_param uuid,
	status_filter tender_status,
	vendor_filter uuid,
	limit_param integer,
	offset_param integer
);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_project_tenders (
	project_id_param uuid,
	status_filter tender_status DEFAULT NULL::tender_status,
	vendor_filter uuid DEFAULT NULL::uuid,
	limit_param integer DEFAULT 50,
	offset_param integer DEFAULT 0
) RETURNS TABLE (
	tender_id uuid,
	vendor_name text,
	tender_name text,
	description text,
	submission_date date,
	currency_code text,
	currency_symbol text,
	symbol_position text,
	status tender_status,
	notes text,
	current_revision_id uuid,
	revision_number integer,
	line_item_count bigint,
	total_amount numeric,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        t.tender_id,
        v.name as vendor_name,
        t.tender_name,
        t.description,
        t.submission_date,
        t.currency_code,
        c.symbol as currency_symbol,
        c.symbol_position,
        t.status,
        t.notes,
        tr.tender_revision_id as current_revision_id,
        tr.revision_number,
        COALESCE(line_counts.line_item_count, 0) as line_item_count,
        COALESCE(line_totals.total_amount, 0) as total_amount,
        t.created_at,
        t.updated_at
    FROM public.tender t
    JOIN public.vendor v ON t.vendor_id = v.vendor_id
    JOIN public.currency c ON t.currency_code = c.currency_code
    LEFT JOIN public.tender_revision tr ON t.tender_id = tr.tender_id AND tr.is_current = true
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            COUNT(tli.tender_line_item_id) as line_item_count
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_counts ON t.tender_id = line_counts.tender_id
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            SUM(tli.subtotal) as total_amount
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_totals ON t.tender_id = line_totals.tender_id
    WHERE t.project_id = project_id_param
    AND (status_filter IS NULL OR t.status = status_filter)
    AND (vendor_filter IS NULL OR t.vendor_id = vendor_filter)
    ORDER BY t.submission_date DESC, t.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$function$;
