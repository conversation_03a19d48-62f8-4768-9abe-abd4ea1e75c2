-- Idempotent backfill to ensure all active budget versions have items
-- Copies from public.budget_line_item_current into public.budget_version_item
-- for each project's active version where an item is missing.
INSERT INTO
	public.budget_version_item (
		budget_version_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty,
		created_at,
		updated_at
	)
SELECT
	p.active_budget_version_id AS budget_version_id,
	bli.wbs_library_item_id,
	bli.quantity,
	bli.unit,
	bli.material_rate,
	bli.labor_rate,
	bli.productivity_per_hour,
	bli.unit_rate_manual_override,
	bli.unit_rate,
	bli.factor,
	bli.remarks,
	bli.cost_certainty,
	bli.design_certainty,
	bli.created_at,
	bli.updated_at
FROM
	public.project p
	JOIN public.budget_line_item_current bli ON bli.project_id = p.project_id
	LEFT JOIN public.budget_version_item bvi ON bvi.budget_version_id = p.active_budget_version_id
	AND bvi.wbs_library_item_id = bli.wbs_library_item_id
WHERE
	p.active_budget_version_id IS NOT NULL
	AND bvi.budget_version_item_id IS NULL;

-- Also ensure any existing snapshots without a link are associated with the active version
UPDATE public.budget_snapshot bs
SET
	budget_version_id = p.active_budget_version_id
FROM
	public.project_stage ps
	JOIN public.project p ON p.project_id = ps.project_id
WHERE
	bs.project_stage_id = ps.project_stage_id
	AND bs.budget_version_id IS NULL
	AND p.active_budget_version_id IS NOT NULL;
