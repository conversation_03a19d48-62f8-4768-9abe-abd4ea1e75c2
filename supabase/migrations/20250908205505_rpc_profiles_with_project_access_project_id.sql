drop function if exists "public"."profiles_with_project_access" (_project_name text, _client_name text);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.profiles_with_project_access (_project_id uuid) RETURNS TABLE (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone,
	access_via text,
	role text
) LANGUAGE sql STABLE SECURITY DEFINER
SET
	search_path TO '' AS $function$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'organization'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization' and m.entity_id = c.org_id
	join public.project pr on pr.client_id = c.client_id
where pr.project_id = _project_id
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'client'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client' and m.entity_id = c.client_id
	join public.project pr on pr.client_id = c.client_id
where pr.project_id = _project_id
union all
/* ── via PROJECT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'project'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.project pr on m.entity_type = 'project' and m.entity_id = pr.project_id
where pr.project_id = _project_id
order by created_at desc;
$function$;
