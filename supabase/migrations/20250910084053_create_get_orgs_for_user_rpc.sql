set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_orgs_for_user () RETURNS TABLE (
	org_id uuid,
	name text,
	description text,
	logo_url text,
	created_by_user_id uuid,
	created_at timestamp with time zone,
	updated_at timestamp with time zone,
	role membership_role
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF v_user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	RETURN QUERY
	SELECT o.org_id,
	       o.name,
	       o.description,
	       o.logo_url,
	       o.created_by_user_id,
	       o.created_at,
	       o.updated_at,
	       m.role
	FROM public.membership m
	JOIN public.organization o ON o.org_id = m.entity_id
	WHERE m.user_id = v_user_id
	  AND m.entity_type = 'organization'::public.entity_type
	ORDER BY o.updated_at DESC;
END;
$function$;
