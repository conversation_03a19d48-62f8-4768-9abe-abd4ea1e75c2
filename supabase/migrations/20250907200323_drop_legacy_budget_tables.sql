drop trigger if exists "audit_budget_line_item_trigger" on "public"."budget_line_item_current";

drop trigger if exists "update_updated_at" on "public"."budget_line_item_current";

drop policy "System can insert budget line item audit records" on "public"."budget_line_item_audit";

drop policy "Users can view budget line item audit for accessible projects" on "public"."budget_line_item_audit";

drop policy "Project editors can delete budget line item" on "public"."budget_line_item_current";

drop policy "Project editors can insert budget line item" on "public"."budget_line_item_current";

drop policy "Project editors can update budget line item" on "public"."budget_line_item_current";

drop policy "Project viewers can view budget line item" on "public"."budget_line_item_current";

revoke delete on table "public"."budget_line_item_audit"
from
	"anon";

revoke insert on table "public"."budget_line_item_audit"
from
	"anon";

revoke references on table "public"."budget_line_item_audit"
from
	"anon";

revoke
select
	on table "public"."budget_line_item_audit"
from
	"anon";

revoke trigger on table "public"."budget_line_item_audit"
from
	"anon";

revoke
truncate on table "public"."budget_line_item_audit"
from
	"anon";

revoke
update on table "public"."budget_line_item_audit"
from
	"anon";

revoke delete on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke insert on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke references on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke
select
	on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke trigger on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke
truncate on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke
update on table "public"."budget_line_item_audit"
from
	"authenticated";

revoke delete on table "public"."budget_line_item_audit"
from
	"service_role";

revoke insert on table "public"."budget_line_item_audit"
from
	"service_role";

revoke references on table "public"."budget_line_item_audit"
from
	"service_role";

revoke
select
	on table "public"."budget_line_item_audit"
from
	"service_role";

revoke trigger on table "public"."budget_line_item_audit"
from
	"service_role";

revoke
truncate on table "public"."budget_line_item_audit"
from
	"service_role";

revoke
update on table "public"."budget_line_item_audit"
from
	"service_role";

revoke delete on table "public"."budget_line_item_current"
from
	"anon";

revoke insert on table "public"."budget_line_item_current"
from
	"anon";

revoke references on table "public"."budget_line_item_current"
from
	"anon";

revoke
select
	on table "public"."budget_line_item_current"
from
	"anon";

revoke trigger on table "public"."budget_line_item_current"
from
	"anon";

revoke
truncate on table "public"."budget_line_item_current"
from
	"anon";

revoke
update on table "public"."budget_line_item_current"
from
	"anon";

revoke delete on table "public"."budget_line_item_current"
from
	"authenticated";

revoke insert on table "public"."budget_line_item_current"
from
	"authenticated";

revoke references on table "public"."budget_line_item_current"
from
	"authenticated";

revoke
select
	on table "public"."budget_line_item_current"
from
	"authenticated";

revoke trigger on table "public"."budget_line_item_current"
from
	"authenticated";

revoke
truncate on table "public"."budget_line_item_current"
from
	"authenticated";

revoke
update on table "public"."budget_line_item_current"
from
	"authenticated";

revoke delete on table "public"."budget_line_item_current"
from
	"service_role";

revoke insert on table "public"."budget_line_item_current"
from
	"service_role";

revoke references on table "public"."budget_line_item_current"
from
	"service_role";

revoke
select
	on table "public"."budget_line_item_current"
from
	"service_role";

revoke trigger on table "public"."budget_line_item_current"
from
	"service_role";

revoke
truncate on table "public"."budget_line_item_current"
from
	"service_role";

revoke
update on table "public"."budget_line_item_current"
from
	"service_role";

alter table "public"."budget_line_item_audit"
drop constraint "budget_line_item_audit_changed_by_fkey";

alter table "public"."budget_line_item_audit"
drop constraint "budget_line_item_audit_operation_type_check";

alter table "public"."budget_line_item_current"
drop constraint "budget_line_item_current_project_id_fkey";

alter table "public"."budget_line_item_current"
drop constraint "budget_line_item_current_project_id_wbs_library_item_id_key";

alter table "public"."budget_line_item_current"
drop constraint "budget_line_item_current_wbs_library_item_id_fkey";

drop function if exists "public"."audit_budget_line_item_changes" ();

alter table "public"."budget_line_item_audit"
drop constraint "budget_line_item_audit_pkey";

alter table "public"."budget_line_item_current"
drop constraint "budget_line_item_current_pkey";

drop index if exists "public"."budget_line_item_audit_budget_line_item_id_idx";

drop index if exists "public"."budget_line_item_audit_changed_at_idx";

drop index if exists "public"."budget_line_item_audit_changed_by_idx";

drop index if exists "public"."budget_line_item_audit_operation_type_idx";

drop index if exists "public"."budget_line_item_audit_pkey";

drop index if exists "public"."budget_line_item_audit_project_id_idx";

drop index if exists "public"."budget_line_item_current_pkey";

drop index if exists "public"."budget_line_item_current_project_id_idx";

drop index if exists "public"."budget_line_item_current_project_id_wbs_library_item_id_key";

drop index if exists "public"."budget_line_item_current_wbs_library_item_id_idx";

drop table "public"."budget_line_item_audit";

drop table "public"."budget_line_item_current";
