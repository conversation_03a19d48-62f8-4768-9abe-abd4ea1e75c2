create type "public"."normalization_type" as enum('amount', 'percentage');

create type "public"."tender_status" as enum(
	'submitted',
	'under_review',
	'selected',
	'rejected'
);

create table "public"."budget_transfer" (
	"budget_transfer_id" uuid not null default gen_random_uuid(),
	"project_id" uuid not null,
	"tender_line_item_id" uuid,
	"from_wbs_library_item_id" uuid not null,
	"to_wbs_library_item_id" uuid not null,
	"transfer_amount" numeric(20, 4) not null,
	"reason" text not null,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."budget_transfer" enable row level security;

create table "public"."budget_transfer_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"budget_transfer_id" uuid not null,
	"project_id" uuid,
	"tender_line_item_id" uuid,
	"from_wbs_library_item_id" uuid,
	"to_wbs_library_item_id" uuid,
	"transfer_amount" numeric(20, 4),
	"reason" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."budget_transfer_audit" enable row level security;

create table "public"."currency" (
	"currency_code" text not null,
	"symbol" text not null,
	"symbol_position" text not null default 'before'::text,
	"description" text not null,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."currency" enable row level security;

create table "public"."tender" (
	"tender_id" uuid not null default gen_random_uuid(),
	"project_id" uuid not null,
	"vendor_id" uuid not null,
	"tender_name" text not null,
	"description" text,
	"submission_date" date not null,
	"currency_code" text not null,
	"status" tender_status not null default 'submitted'::tender_status,
	"notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender" enable row level security;

create table "public"."tender_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_id" uuid not null,
	"project_id" uuid,
	"vendor_id" uuid,
	"tender_name" text,
	"description" text,
	"submission_date" date,
	"currency_code" text,
	"status" tender_status,
	"notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_audit" enable row level security;

create table "public"."tender_line_item" (
	"tender_line_item_id" uuid not null default gen_random_uuid(),
	"tender_revision_id" uuid not null,
	"line_number" integer not null,
	"description" text not null,
	"quantity" numeric(20, 4),
	"unit" text,
	"unit_rate" numeric(20, 4),
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_factor" numeric(10, 4),
	"subtotal" numeric(20, 4) not null,
	"normalization_type" normalization_type,
	"normalization_amount" numeric(20, 4),
	"normalization_percentage" numeric(5, 2),
	"notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_line_item" enable row level security;

create table "public"."tender_line_item_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_line_item_id" uuid not null,
	"tender_revision_id" uuid,
	"line_number" integer,
	"description" text,
	"quantity" numeric(20, 4),
	"unit" text,
	"unit_rate" numeric(20, 4),
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_factor" numeric(10, 4),
	"subtotal" numeric(20, 4),
	"normalization_type" normalization_type,
	"normalization_amount" numeric(20, 4),
	"normalization_percentage" numeric(5, 2),
	"notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_line_item_audit" enable row level security;

create table "public"."tender_revision" (
	"tender_revision_id" uuid not null default gen_random_uuid(),
	"tender_id" uuid not null,
	"revision_number" integer not null,
	"is_current" boolean not null default false,
	"revision_notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_revision" enable row level security;

create table "public"."tender_revision_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_revision_id" uuid not null,
	"tender_id" uuid,
	"revision_number" integer,
	"is_current" boolean,
	"revision_notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_revision_audit" enable row level security;

create table "public"."tender_score" (
	"tender_score_id" uuid not null default gen_random_uuid(),
	"tender_id" uuid not null,
	"tender_scoring_criteria_id" uuid not null,
	"score" numeric(5, 2) not null,
	"comments" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_score" enable row level security;

create table "public"."tender_score_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_score_id" uuid not null,
	"tender_id" uuid,
	"tender_scoring_criteria_id" uuid,
	"score" numeric(5, 2),
	"comments" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_score_audit" enable row level security;

create table "public"."tender_scoring_criteria" (
	"tender_scoring_criteria_id" uuid not null default gen_random_uuid(),
	"project_id" uuid not null,
	"criteria_name" text not null,
	"description" text,
	"weight" numeric(5, 2) not null,
	"max_score" numeric(5, 2) not null default 10,
	"is_active" boolean not null default true,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_scoring_criteria" enable row level security;

create table "public"."tender_scoring_criteria_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_scoring_criteria_id" uuid not null,
	"project_id" uuid,
	"criteria_name" text,
	"description" text,
	"weight" numeric(5, 2),
	"max_score" numeric(5, 2),
	"is_active" boolean,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_scoring_criteria_audit" enable row level security;

create table "public"."tender_wbs_mapping" (
	"tender_wbs_mapping_id" uuid not null default gen_random_uuid(),
	"tender_line_item_id" uuid not null,
	"wbs_library_item_id" uuid not null,
	"coverage_percentage" numeric(5, 2),
	"coverage_quantity" numeric(20, 4),
	"notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_wbs_mapping" enable row level security;

create table "public"."tender_wbs_mapping_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_wbs_mapping_id" uuid not null,
	"tender_line_item_id" uuid,
	"wbs_library_item_id" uuid,
	"coverage_percentage" numeric(5, 2),
	"coverage_quantity" numeric(20, 4),
	"notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_wbs_mapping_audit" enable row level security;

create table "public"."tender_work_package" (
	"tender_work_package_id" uuid not null default gen_random_uuid(),
	"tender_id" uuid not null,
	"work_package_name" text not null,
	"work_package_description" text,
	"conversion_date" date not null,
	"conversion_notes" text,
	"created_by_user_id" uuid not null default auth.uid (),
	"created_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"updated_at" timestamp with time zone not null default timezone ('utc'::text, now())
);

alter table "public"."tender_work_package" enable row level security;

create table "public"."tender_work_package_audit" (
	"audit_id" uuid not null default gen_random_uuid(),
	"operation_type" text not null,
	"changed_by" uuid not null,
	"changed_at" timestamp with time zone not null default timezone ('utc'::text, now()),
	"old_values" jsonb,
	"new_values" jsonb,
	"tender_work_package_id" uuid not null,
	"tender_id" uuid,
	"work_package_name" text,
	"work_package_description" text,
	"conversion_date" date,
	"conversion_notes" text,
	"created_by_user_id" uuid,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

alter table "public"."tender_work_package_audit" enable row level security;

CREATE INDEX budget_transfer_audit_budget_transfer_id_idx ON public.budget_transfer_audit USING btree (budget_transfer_id);

CREATE INDEX budget_transfer_audit_changed_at_idx ON public.budget_transfer_audit USING btree (changed_at);

CREATE INDEX budget_transfer_audit_changed_by_idx ON public.budget_transfer_audit USING btree (changed_by);

CREATE INDEX budget_transfer_audit_operation_type_idx ON public.budget_transfer_audit USING btree (operation_type);

CREATE UNIQUE INDEX budget_transfer_audit_pkey ON public.budget_transfer_audit USING btree (audit_id);

CREATE INDEX budget_transfer_audit_project_id_idx ON public.budget_transfer_audit USING btree (project_id);

CREATE INDEX budget_transfer_audit_tender_line_item_id_idx ON public.budget_transfer_audit USING btree (tender_line_item_id);

CREATE INDEX budget_transfer_created_at_idx ON public.budget_transfer USING btree (created_at);

CREATE INDEX budget_transfer_created_by_user_id_idx ON public.budget_transfer USING btree (created_by_user_id);

CREATE INDEX budget_transfer_from_wbs_library_item_id_idx ON public.budget_transfer USING btree (from_wbs_library_item_id);

CREATE UNIQUE INDEX budget_transfer_pkey ON public.budget_transfer USING btree (budget_transfer_id);

CREATE INDEX budget_transfer_project_id_idx ON public.budget_transfer USING btree (project_id);

CREATE INDEX budget_transfer_tender_line_item_id_idx ON public.budget_transfer USING btree (tender_line_item_id);

CREATE INDEX budget_transfer_to_wbs_library_item_id_idx ON public.budget_transfer USING btree (to_wbs_library_item_id);

CREATE INDEX currency_created_by_user_id_idx ON public.currency USING btree (created_by_user_id);

CREATE INDEX currency_description_idx ON public.currency USING btree (description);

CREATE UNIQUE INDEX currency_pkey ON public.currency USING btree (currency_code);

CREATE INDEX tender_audit_changed_at_idx ON public.tender_audit USING btree (changed_at);

CREATE INDEX tender_audit_changed_by_idx ON public.tender_audit USING btree (changed_by);

CREATE INDEX tender_audit_operation_type_idx ON public.tender_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_audit_pkey ON public.tender_audit USING btree (audit_id);

CREATE INDEX tender_audit_tender_id_idx ON public.tender_audit USING btree (tender_id);

CREATE INDEX tender_created_by_user_id_idx ON public.tender USING btree (created_by_user_id);

CREATE INDEX tender_currency_code_idx ON public.tender USING btree (currency_code);

CREATE UNIQUE INDEX tender_current_rev_uniq ON public.tender_revision USING btree (tender_id)
WHERE
	(is_current = true);

CREATE INDEX tender_line_item_audit_changed_at_idx ON public.tender_line_item_audit USING btree (changed_at);

CREATE INDEX tender_line_item_audit_changed_by_idx ON public.tender_line_item_audit USING btree (changed_by);

CREATE INDEX tender_line_item_audit_operation_type_idx ON public.tender_line_item_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_line_item_audit_pkey ON public.tender_line_item_audit USING btree (audit_id);

CREATE INDEX tender_line_item_audit_tender_line_item_id_idx ON public.tender_line_item_audit USING btree (tender_line_item_id);

CREATE INDEX tender_line_item_audit_tender_revision_id_idx ON public.tender_line_item_audit USING btree (tender_revision_id);

CREATE INDEX tender_line_item_created_by_user_id_idx ON public.tender_line_item USING btree (created_by_user_id);

CREATE INDEX tender_line_item_line_number_idx ON public.tender_line_item USING btree (line_number);

CREATE UNIQUE INDEX tender_line_item_line_number_unique ON public.tender_line_item USING btree (tender_revision_id, line_number);

CREATE INDEX tender_line_item_normalization_type_idx ON public.tender_line_item USING btree (normalization_type);

CREATE UNIQUE INDEX tender_line_item_pkey ON public.tender_line_item USING btree (tender_line_item_id);

CREATE INDEX tender_line_item_subtotal_idx ON public.tender_line_item USING btree (subtotal);

CREATE INDEX tender_line_item_tender_revision_id_idx ON public.tender_line_item USING btree (tender_revision_id);

CREATE UNIQUE INDEX tender_name_project_unique ON public.tender USING btree (tender_name, project_id);

CREATE UNIQUE INDEX tender_pkey ON public.tender USING btree (tender_id);

CREATE INDEX tender_project_id_idx ON public.tender USING btree (project_id);

CREATE INDEX tender_revision_audit_changed_at_idx ON public.tender_revision_audit USING btree (changed_at);

CREATE INDEX tender_revision_audit_changed_by_idx ON public.tender_revision_audit USING btree (changed_by);

CREATE INDEX tender_revision_audit_operation_type_idx ON public.tender_revision_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_revision_audit_pkey ON public.tender_revision_audit USING btree (audit_id);

CREATE INDEX tender_revision_audit_tender_id_idx ON public.tender_revision_audit USING btree (tender_id);

CREATE INDEX tender_revision_audit_tender_revision_id_idx ON public.tender_revision_audit USING btree (tender_revision_id);

CREATE INDEX tender_revision_created_by_user_id_idx ON public.tender_revision USING btree (created_by_user_id);

CREATE INDEX tender_revision_is_current_idx ON public.tender_revision USING btree (is_current);

CREATE UNIQUE INDEX tender_revision_number_unique ON public.tender_revision USING btree (tender_id, revision_number);

CREATE UNIQUE INDEX tender_revision_pkey ON public.tender_revision USING btree (tender_revision_id);

CREATE INDEX tender_revision_tender_id_idx ON public.tender_revision USING btree (tender_id);

CREATE INDEX tender_score_audit_changed_at_idx ON public.tender_score_audit USING btree (changed_at);

CREATE INDEX tender_score_audit_changed_by_idx ON public.tender_score_audit USING btree (changed_by);

CREATE INDEX tender_score_audit_operation_type_idx ON public.tender_score_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_score_audit_pkey ON public.tender_score_audit USING btree (audit_id);

CREATE INDEX tender_score_audit_tender_id_idx ON public.tender_score_audit USING btree (tender_id);

CREATE INDEX tender_score_audit_tender_score_id_idx ON public.tender_score_audit USING btree (tender_score_id);

CREATE INDEX tender_score_audit_tender_scoring_criteria_id_idx ON public.tender_score_audit USING btree (tender_scoring_criteria_id);

CREATE INDEX tender_score_created_by_user_id_idx ON public.tender_score USING btree (created_by_user_id);

CREATE UNIQUE INDEX tender_score_pkey ON public.tender_score USING btree (tender_score_id);

CREATE INDEX tender_score_score_idx ON public.tender_score USING btree (score);

CREATE INDEX tender_score_tender_id_idx ON public.tender_score USING btree (tender_id);

CREATE INDEX tender_score_tender_scoring_criteria_id_idx ON public.tender_score USING btree (tender_scoring_criteria_id);

CREATE UNIQUE INDEX tender_score_unique ON public.tender_score USING btree (tender_id, tender_scoring_criteria_id);

CREATE INDEX tender_scoring_criteria_audit_changed_at_idx ON public.tender_scoring_criteria_audit USING btree (changed_at);

CREATE INDEX tender_scoring_criteria_audit_changed_by_idx ON public.tender_scoring_criteria_audit USING btree (changed_by);

CREATE INDEX tender_scoring_criteria_audit_operation_type_idx ON public.tender_scoring_criteria_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_scoring_criteria_audit_pkey ON public.tender_scoring_criteria_audit USING btree (audit_id);

CREATE INDEX tender_scoring_criteria_audit_project_id_idx ON public.tender_scoring_criteria_audit USING btree (project_id);

CREATE INDEX tender_scoring_criteria_audit_tender_scoring_criteria_id_idx ON public.tender_scoring_criteria_audit USING btree (tender_scoring_criteria_id);

CREATE INDEX tender_scoring_criteria_created_by_user_id_idx ON public.tender_scoring_criteria USING btree (created_by_user_id);

CREATE INDEX tender_scoring_criteria_is_active_idx ON public.tender_scoring_criteria USING btree (is_active);

CREATE UNIQUE INDEX tender_scoring_criteria_name_project_unique ON public.tender_scoring_criteria USING btree (criteria_name, project_id);

CREATE UNIQUE INDEX tender_scoring_criteria_pkey ON public.tender_scoring_criteria USING btree (tender_scoring_criteria_id);

CREATE INDEX tender_scoring_criteria_project_id_idx ON public.tender_scoring_criteria USING btree (project_id);

CREATE INDEX tender_scoring_criteria_weight_idx ON public.tender_scoring_criteria USING btree (weight);

CREATE INDEX tender_status_idx ON public.tender USING btree (status);

CREATE INDEX tender_submission_date_idx ON public.tender USING btree (submission_date);

CREATE INDEX tender_vendor_id_idx ON public.tender USING btree (vendor_id);

CREATE INDEX tender_wbs_mapping_audit_changed_at_idx ON public.tender_wbs_mapping_audit USING btree (changed_at);

CREATE INDEX tender_wbs_mapping_audit_changed_by_idx ON public.tender_wbs_mapping_audit USING btree (changed_by);

CREATE INDEX tender_wbs_mapping_audit_operation_type_idx ON public.tender_wbs_mapping_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_wbs_mapping_audit_pkey ON public.tender_wbs_mapping_audit USING btree (audit_id);

CREATE INDEX tender_wbs_mapping_audit_tender_line_item_id_idx ON public.tender_wbs_mapping_audit USING btree (tender_line_item_id);

CREATE INDEX tender_wbs_mapping_audit_tender_wbs_mapping_id_idx ON public.tender_wbs_mapping_audit USING btree (tender_wbs_mapping_id);

CREATE INDEX tender_wbs_mapping_audit_wbs_library_item_id_idx ON public.tender_wbs_mapping_audit USING btree (wbs_library_item_id);

CREATE INDEX tender_wbs_mapping_coverage_percentage_idx ON public.tender_wbs_mapping USING btree (coverage_percentage);

CREATE INDEX tender_wbs_mapping_created_by_user_id_idx ON public.tender_wbs_mapping USING btree (created_by_user_id);

CREATE UNIQUE INDEX tender_wbs_mapping_pkey ON public.tender_wbs_mapping USING btree (tender_wbs_mapping_id);

CREATE INDEX tender_wbs_mapping_tender_line_item_id_idx ON public.tender_wbs_mapping USING btree (tender_line_item_id);

CREATE UNIQUE INDEX tender_wbs_mapping_unique ON public.tender_wbs_mapping USING btree (tender_line_item_id, wbs_library_item_id);

CREATE INDEX tender_wbs_mapping_wbs_library_item_id_idx ON public.tender_wbs_mapping USING btree (wbs_library_item_id);

CREATE INDEX tender_work_package_audit_changed_at_idx ON public.tender_work_package_audit USING btree (changed_at);

CREATE INDEX tender_work_package_audit_changed_by_idx ON public.tender_work_package_audit USING btree (changed_by);

CREATE INDEX tender_work_package_audit_operation_type_idx ON public.tender_work_package_audit USING btree (operation_type);

CREATE UNIQUE INDEX tender_work_package_audit_pkey ON public.tender_work_package_audit USING btree (audit_id);

CREATE INDEX tender_work_package_audit_tender_id_idx ON public.tender_work_package_audit USING btree (tender_id);

CREATE INDEX tender_work_package_audit_tender_work_package_id_idx ON public.tender_work_package_audit USING btree (tender_work_package_id);

CREATE INDEX tender_work_package_conversion_date_idx ON public.tender_work_package USING btree (conversion_date);

CREATE INDEX tender_work_package_created_by_user_id_idx ON public.tender_work_package USING btree (created_by_user_id);

CREATE UNIQUE INDEX tender_work_package_pkey ON public.tender_work_package USING btree (tender_work_package_id);

CREATE INDEX tender_work_package_tender_id_idx ON public.tender_work_package USING btree (tender_id);

CREATE UNIQUE INDEX tender_work_package_tender_unique ON public.tender_work_package USING btree (tender_id);

alter table "public"."budget_transfer"
add constraint "budget_transfer_pkey" PRIMARY KEY using index "budget_transfer_pkey";

alter table "public"."budget_transfer_audit"
add constraint "budget_transfer_audit_pkey" PRIMARY KEY using index "budget_transfer_audit_pkey";

alter table "public"."currency"
add constraint "currency_pkey" PRIMARY KEY using index "currency_pkey";

alter table "public"."tender"
add constraint "tender_pkey" PRIMARY KEY using index "tender_pkey";

alter table "public"."tender_audit"
add constraint "tender_audit_pkey" PRIMARY KEY using index "tender_audit_pkey";

alter table "public"."tender_line_item"
add constraint "tender_line_item_pkey" PRIMARY KEY using index "tender_line_item_pkey";

alter table "public"."tender_line_item_audit"
add constraint "tender_line_item_audit_pkey" PRIMARY KEY using index "tender_line_item_audit_pkey";

alter table "public"."tender_revision"
add constraint "tender_revision_pkey" PRIMARY KEY using index "tender_revision_pkey";

alter table "public"."tender_revision_audit"
add constraint "tender_revision_audit_pkey" PRIMARY KEY using index "tender_revision_audit_pkey";

alter table "public"."tender_score"
add constraint "tender_score_pkey" PRIMARY KEY using index "tender_score_pkey";

alter table "public"."tender_score_audit"
add constraint "tender_score_audit_pkey" PRIMARY KEY using index "tender_score_audit_pkey";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_pkey" PRIMARY KEY using index "tender_scoring_criteria_pkey";

alter table "public"."tender_scoring_criteria_audit"
add constraint "tender_scoring_criteria_audit_pkey" PRIMARY KEY using index "tender_scoring_criteria_audit_pkey";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_pkey" PRIMARY KEY using index "tender_wbs_mapping_pkey";

alter table "public"."tender_wbs_mapping_audit"
add constraint "tender_wbs_mapping_audit_pkey" PRIMARY KEY using index "tender_wbs_mapping_audit_pkey";

alter table "public"."tender_work_package"
add constraint "tender_work_package_pkey" PRIMARY KEY using index "tender_work_package_pkey";

alter table "public"."tender_work_package_audit"
add constraint "tender_work_package_audit_pkey" PRIMARY KEY using index "tender_work_package_audit_pkey";

alter table "public"."budget_transfer"
add constraint "budget_transfer_amount_positive_check" CHECK ((transfer_amount > (0)::numeric)) not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_amount_positive_check";

alter table "public"."budget_transfer"
add constraint "budget_transfer_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_created_by_user_id_fkey";

alter table "public"."budget_transfer"
add constraint "budget_transfer_different_wbs_check" CHECK (
	(
		from_wbs_library_item_id <> to_wbs_library_item_id
	)
) not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_different_wbs_check";

alter table "public"."budget_transfer"
add constraint "budget_transfer_from_wbs_library_item_id_fkey" FOREIGN KEY (from_wbs_library_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_from_wbs_library_item_id_fkey";

alter table "public"."budget_transfer"
add constraint "budget_transfer_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_project_id_fkey";

alter table "public"."budget_transfer"
add constraint "budget_transfer_tender_line_item_id_fkey" FOREIGN KEY (tender_line_item_id) REFERENCES tender_line_item (tender_line_item_id) ON UPDATE RESTRICT ON DELETE SET NULL not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_tender_line_item_id_fkey";

alter table "public"."budget_transfer"
add constraint "budget_transfer_to_wbs_library_item_id_fkey" FOREIGN KEY (to_wbs_library_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_transfer" validate constraint "budget_transfer_to_wbs_library_item_id_fkey";

alter table "public"."budget_transfer_audit"
add constraint "budget_transfer_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."budget_transfer_audit" validate constraint "budget_transfer_audit_changed_by_fkey";

alter table "public"."budget_transfer_audit"
add constraint "budget_transfer_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."budget_transfer_audit" validate constraint "budget_transfer_audit_operation_type_check";

alter table "public"."currency"
add constraint "currency_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."currency" validate constraint "currency_created_by_user_id_fkey";

alter table "public"."currency"
add constraint "currency_symbol_position_check" CHECK (
	(
		symbol_position = ANY (ARRAY['before'::text, 'after'::text])
	)
) not valid;

alter table "public"."currency" validate constraint "currency_symbol_position_check";

alter table "public"."tender"
add constraint "tender_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender" validate constraint "tender_created_by_user_id_fkey";

alter table "public"."tender"
add constraint "tender_currency_code_fkey" FOREIGN KEY (currency_code) REFERENCES currency (currency_code) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender" validate constraint "tender_currency_code_fkey";

alter table "public"."tender"
add constraint "tender_name_project_unique" UNIQUE using index "tender_name_project_unique";

alter table "public"."tender"
add constraint "tender_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender" validate constraint "tender_project_id_fkey";

alter table "public"."tender"
add constraint "tender_vendor_id_fkey" FOREIGN KEY (vendor_id) REFERENCES vendor (vendor_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender" validate constraint "tender_vendor_id_fkey";

alter table "public"."tender_audit"
add constraint "tender_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_audit" validate constraint "tender_audit_changed_by_fkey";

alter table "public"."tender_audit"
add constraint "tender_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_audit" validate constraint "tender_audit_operation_type_check";

alter table "public"."tender_line_item"
add constraint "tender_line_item_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_line_item" validate constraint "tender_line_item_created_by_user_id_fkey";

alter table "public"."tender_line_item"
add constraint "tender_line_item_line_number_unique" UNIQUE using index "tender_line_item_line_number_unique";

alter table "public"."tender_line_item"
add constraint "tender_line_item_normalization_check" CHECK (
	(
		(
			(normalization_type = 'amount'::normalization_type)
			AND (normalization_amount IS NOT NULL)
		)
		OR (
			(
				normalization_type = 'percentage'::normalization_type
			)
			AND (normalization_percentage IS NOT NULL)
		)
		OR (
			(normalization_amount IS NULL)
			AND (normalization_percentage IS NULL)
		)
	)
) not valid;

alter table "public"."tender_line_item" validate constraint "tender_line_item_normalization_check";

alter table "public"."tender_line_item"
add constraint "tender_line_item_tender_revision_id_fkey" FOREIGN KEY (tender_revision_id) REFERENCES tender_revision (tender_revision_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."tender_line_item" validate constraint "tender_line_item_tender_revision_id_fkey";

alter table "public"."tender_line_item_audit"
add constraint "tender_line_item_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_line_item_audit" validate constraint "tender_line_item_audit_changed_by_fkey";

alter table "public"."tender_line_item_audit"
add constraint "tender_line_item_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_line_item_audit" validate constraint "tender_line_item_audit_operation_type_check";

alter table "public"."tender_revision"
add constraint "tender_revision_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_revision" validate constraint "tender_revision_created_by_user_id_fkey";

alter table "public"."tender_revision"
add constraint "tender_revision_number_unique" UNIQUE using index "tender_revision_number_unique";

alter table "public"."tender_revision"
add constraint "tender_revision_tender_id_fkey" FOREIGN KEY (tender_id) REFERENCES tender (tender_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."tender_revision" validate constraint "tender_revision_tender_id_fkey";

alter table "public"."tender_revision_audit"
add constraint "tender_revision_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_revision_audit" validate constraint "tender_revision_audit_changed_by_fkey";

alter table "public"."tender_revision_audit"
add constraint "tender_revision_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_revision_audit" validate constraint "tender_revision_audit_operation_type_check";

alter table "public"."tender_score"
add constraint "tender_score_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_score" validate constraint "tender_score_created_by_user_id_fkey";

alter table "public"."tender_score"
add constraint "tender_score_range_check" CHECK (
	(
		(score >= (0)::numeric)
		AND (score <= (10)::numeric)
	)
) not valid;

alter table "public"."tender_score" validate constraint "tender_score_range_check";

alter table "public"."tender_score"
add constraint "tender_score_tender_id_fkey" FOREIGN KEY (tender_id) REFERENCES tender (tender_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."tender_score" validate constraint "tender_score_tender_id_fkey";

alter table "public"."tender_score"
add constraint "tender_score_tender_scoring_criteria_id_fkey" FOREIGN KEY (tender_scoring_criteria_id) REFERENCES tender_scoring_criteria (tender_scoring_criteria_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."tender_score" validate constraint "tender_score_tender_scoring_criteria_id_fkey";

alter table "public"."tender_score"
add constraint "tender_score_unique" UNIQUE using index "tender_score_unique";

alter table "public"."tender_score_audit"
add constraint "tender_score_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_score_audit" validate constraint "tender_score_audit_changed_by_fkey";

alter table "public"."tender_score_audit"
add constraint "tender_score_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_score_audit" validate constraint "tender_score_audit_operation_type_check";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_scoring_criteria" validate constraint "tender_scoring_criteria_created_by_user_id_fkey";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_max_score_check" CHECK ((max_score > (0)::numeric)) not valid;

alter table "public"."tender_scoring_criteria" validate constraint "tender_scoring_criteria_max_score_check";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_name_project_unique" UNIQUE using index "tender_scoring_criteria_name_project_unique";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_scoring_criteria" validate constraint "tender_scoring_criteria_project_id_fkey";

alter table "public"."tender_scoring_criteria"
add constraint "tender_scoring_criteria_weight_check" CHECK (
	(
		(weight >= (0)::numeric)
		AND (weight <= (100)::numeric)
	)
) not valid;

alter table "public"."tender_scoring_criteria" validate constraint "tender_scoring_criteria_weight_check";

alter table "public"."tender_scoring_criteria_audit"
add constraint "tender_scoring_criteria_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_scoring_criteria_audit" validate constraint "tender_scoring_criteria_audit_changed_by_fkey";

alter table "public"."tender_scoring_criteria_audit"
add constraint "tender_scoring_criteria_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_scoring_criteria_audit" validate constraint "tender_scoring_criteria_audit_operation_type_check";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_coverage_percentage_check" CHECK (
	(
		(coverage_percentage >= (0)::numeric)
		AND (coverage_percentage <= (100)::numeric)
	)
) not valid;

alter table "public"."tender_wbs_mapping" validate constraint "tender_wbs_mapping_coverage_percentage_check";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_coverage_quantity_check" CHECK ((coverage_quantity >= (0)::numeric)) not valid;

alter table "public"."tender_wbs_mapping" validate constraint "tender_wbs_mapping_coverage_quantity_check";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_wbs_mapping" validate constraint "tender_wbs_mapping_created_by_user_id_fkey";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_tender_line_item_id_fkey" FOREIGN KEY (tender_line_item_id) REFERENCES tender_line_item (tender_line_item_id) ON UPDATE RESTRICT ON DELETE CASCADE not valid;

alter table "public"."tender_wbs_mapping" validate constraint "tender_wbs_mapping_tender_line_item_id_fkey";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_unique" UNIQUE using index "tender_wbs_mapping_unique";

alter table "public"."tender_wbs_mapping"
add constraint "tender_wbs_mapping_wbs_library_item_id_fkey" FOREIGN KEY (wbs_library_item_id) REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_wbs_mapping" validate constraint "tender_wbs_mapping_wbs_library_item_id_fkey";

alter table "public"."tender_wbs_mapping_audit"
add constraint "tender_wbs_mapping_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_wbs_mapping_audit" validate constraint "tender_wbs_mapping_audit_changed_by_fkey";

alter table "public"."tender_wbs_mapping_audit"
add constraint "tender_wbs_mapping_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_wbs_mapping_audit" validate constraint "tender_wbs_mapping_audit_operation_type_check";

alter table "public"."tender_work_package"
add constraint "tender_work_package_created_by_user_id_fkey" FOREIGN KEY (created_by_user_id) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_work_package" validate constraint "tender_work_package_created_by_user_id_fkey";

alter table "public"."tender_work_package"
add constraint "tender_work_package_tender_id_fkey" FOREIGN KEY (tender_id) REFERENCES tender (tender_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_work_package" validate constraint "tender_work_package_tender_id_fkey";

alter table "public"."tender_work_package"
add constraint "tender_work_package_tender_unique" UNIQUE using index "tender_work_package_tender_unique";

alter table "public"."tender_work_package_audit"
add constraint "tender_work_package_audit_changed_by_fkey" FOREIGN KEY (changed_by) REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."tender_work_package_audit" validate constraint "tender_work_package_audit_changed_by_fkey";

alter table "public"."tender_work_package_audit"
add constraint "tender_work_package_audit_operation_type_check" CHECK (
	(
		operation_type = ANY (
			ARRAY['INSERT'::text, 'UPDATE'::text, 'DELETE'::text]
		)
	)
) not valid;

alter table "public"."tender_work_package_audit" validate constraint "tender_work_package_audit_operation_type_check";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_budget_transfer_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_transfer_id, OLD.project_id, OLD.tender_line_item_id,
            OLD.from_wbs_library_item_id, OLD.to_wbs_library_item_id, OLD.transfer_amount, OLD.reason,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_transfer_id, NEW.project_id, NEW.tender_line_item_id,
            NEW.from_wbs_library_item_id, NEW.to_wbs_library_item_id, NEW.transfer_amount, NEW.reason,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_transfer_id, NEW.project_id, NEW.tender_line_item_id,
            NEW.from_wbs_library_item_id, NEW.to_wbs_library_item_id, NEW.transfer_amount, NEW.reason,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_id, OLD.project_id, OLD.vendor_id, OLD.tender_name, OLD.description,
            OLD.submission_date, OLD.currency_code, OLD.status, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_id, NEW.project_id, NEW.vendor_id, NEW.tender_name, NEW.description,
            NEW.submission_date, NEW.currency_code, NEW.status, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_id, NEW.project_id, NEW.vendor_id, NEW.tender_name, NEW.description,
            NEW.submission_date, NEW.currency_code, NEW.status, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_line_item_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_line_item_id, OLD.tender_revision_id, OLD.line_number, OLD.description, OLD.quantity,
            OLD.unit, OLD.unit_rate, OLD.material_rate, OLD.labor_rate, OLD.productivity_factor, OLD.subtotal,
            OLD.normalization_type, OLD.normalization_amount, OLD.normalization_percentage, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_line_item_id, NEW.tender_revision_id, NEW.line_number, NEW.description, NEW.quantity,
            NEW.unit, NEW.unit_rate, NEW.material_rate, NEW.labor_rate, NEW.productivity_factor, NEW.subtotal,
            NEW.normalization_type, NEW.normalization_amount, NEW.normalization_percentage, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_line_item_id, NEW.tender_revision_id, NEW.line_number, NEW.description, NEW.quantity,
            NEW.unit, NEW.unit_rate, NEW.material_rate, NEW.labor_rate, NEW.productivity_factor, NEW.subtotal,
            NEW.normalization_type, NEW.normalization_amount, NEW.normalization_percentage, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_revision_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_revision_id, OLD.tender_id, OLD.revision_number, OLD.is_current, OLD.revision_notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_revision_id, NEW.tender_id, NEW.revision_number, NEW.is_current, NEW.revision_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_revision_id, NEW.tender_id, NEW.revision_number, NEW.is_current, NEW.revision_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_score_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_score_id, OLD.tender_id, OLD.tender_scoring_criteria_id, OLD.score, OLD.comments,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_score_id, NEW.tender_id, NEW.tender_scoring_criteria_id, NEW.score, NEW.comments,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_score_id, NEW.tender_id, NEW.tender_scoring_criteria_id, NEW.score, NEW.comments,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_scoring_criteria_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_scoring_criteria_id, OLD.project_id, OLD.criteria_name, OLD.description,
            OLD.weight, OLD.max_score, OLD.is_active,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_scoring_criteria_id, NEW.project_id, NEW.criteria_name, NEW.description,
            NEW.weight, NEW.max_score, NEW.is_active,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_scoring_criteria_id, NEW.project_id, NEW.criteria_name, NEW.description,
            NEW.weight, NEW.max_score, NEW.is_active,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_wbs_mapping_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_wbs_mapping_id, OLD.tender_line_item_id, OLD.wbs_library_item_id,
            OLD.coverage_percentage, OLD.coverage_quantity, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_wbs_mapping_id, NEW.tender_line_item_id, NEW.wbs_library_item_id,
            NEW.coverage_percentage, NEW.coverage_quantity, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_wbs_mapping_id, NEW.tender_line_item_id, NEW.wbs_library_item_id,
            NEW.coverage_percentage, NEW.coverage_quantity, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.audit_tender_work_package_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_work_package_id, OLD.tender_id, OLD.work_package_name, OLD.work_package_description,
            OLD.conversion_date, OLD.conversion_notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_work_package_id, NEW.tender_id, NEW.work_package_name, NEW.work_package_description,
            NEW.conversion_date, NEW.conversion_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_work_package_id, NEW.tender_id, NEW.work_package_name, NEW.work_package_description,
            NEW.conversion_date, NEW.conversion_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.calculate_normalization_amount (
	tender_line_item_id_param uuid,
	normalization_percentage_param numeric
) RETURNS TABLE (
	calculated_amount numeric,
	total_budget_amount numeric,
	mapping_count integer
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    project_id_var uuid;
    total_budget numeric(20, 4) := 0;
    mapping_count_var integer := 0;
BEGIN
    -- Get project ID for access control
    SELECT t.project_id INTO project_id_var
    FROM public.tender_line_item tli
    JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    JOIN public.tender t ON tr.tender_id = t.tender_id
    WHERE tli.tender_line_item_id = tender_line_item_id_param;

    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_var, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Calculate total budget amount from mapped WBS items with coverage
    SELECT 
        COALESCE(SUM(
            (bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)) * 
            COALESCE(twm.coverage_percentage / 100.0, 1.0)
        ), 0),
        COUNT(twm.tender_wbs_mapping_id)
    INTO total_budget, mapping_count_var
    FROM public.tender_wbs_mapping twm
    JOIN public.wbs_library_item wli ON twm.wbs_library_item_id = wli.wbs_library_item_id
    JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE twm.tender_line_item_id = tender_line_item_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    RETURN QUERY
    SELECT 
        (total_budget * normalization_percentage_param / 100.0) as calculated_amount,
        total_budget as total_budget_amount,
        mapping_count_var as mapping_count;
END;
$function$;

CREATE OR REPLACE FUNCTION public.create_budget_transfer (
	project_id_param uuid,
	line_item_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric,
	reason text
) RETURNS TABLE (
	budget_transfer_id uuid,
	is_valid boolean,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    validation_result record;
    new_transfer_id uuid;
    line_item_project_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Ensure the optional tender line item belongs to the same project
    IF line_item_id_param IS NOT NULL THEN
        SELECT t.project_id
        INTO line_item_project_id
        FROM public.tender_line_item tli
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
        JOIN public.tender t ON tr.tender_id = t.tender_id
        WHERE tli.tender_line_item_id = line_item_id_param;

        IF line_item_project_id IS NULL THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item not found';
            RETURN;
        ELSIF line_item_project_id <> project_id_param THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item does not belong to project';
            RETURN;
        END IF;
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid as budget_transfer_id,
            false as is_valid,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true as is_valid,
        NULL::text as error_message;
END;
$function$;

CREATE OR REPLACE FUNCTION public.ensure_single_current_revision () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
    IF NEW.is_current = true THEN
        UPDATE public.tender_revision 
        SET is_current = false 
        WHERE tender_id = NEW.tender_id 
        AND tender_revision_id != NEW.tender_revision_id 
        AND is_current = true;
    END IF;
    RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_project_budget_transfers (
	project_id_param uuid,
	line_item_filter uuid DEFAULT NULL::uuid,
	limit_param integer DEFAULT 50,
	offset_param integer DEFAULT 0
) RETURNS TABLE (
	budget_transfer_id uuid,
	tender_name text,
	line_item_description text,
	from_wbs_code text,
	from_wbs_description text,
	to_wbs_code text,
	to_wbs_description text,
	transfer_amount numeric,
	reason text,
	created_by_name text,
	created_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        bt.budget_transfer_id,
        t.tender_name,
        tli.description as line_item_description,
        from_wli.wbs_code as from_wbs_code,
        from_wli.description as from_wbs_description,
        to_wli.wbs_code as to_wbs_code,
        to_wli.description as to_wbs_description,
        bt.transfer_amount,
        bt.reason,
        p.full_name as created_by_name,
        bt.created_at
    FROM public.budget_transfer bt
    LEFT JOIN public.tender_line_item tli ON bt.tender_line_item_id = tli.tender_line_item_id
    LEFT JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    LEFT JOIN public.tender t ON tr.tender_id = t.tender_id
    JOIN public.wbs_library_item from_wli ON bt.from_wbs_library_item_id = from_wli.wbs_library_item_id
    JOIN public.wbs_library_item to_wli ON bt.to_wbs_library_item_id = to_wli.wbs_library_item_id
    JOIN public.profile p ON bt.created_by_user_id = p.user_id
    WHERE bt.project_id = project_id_param
    AND (line_item_filter IS NULL OR bt.tender_line_item_id = line_item_filter)
    ORDER BY bt.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_project_tenders (
	project_id_param uuid,
	status_filter tender_status DEFAULT NULL::tender_status,
	vendor_filter uuid DEFAULT NULL::uuid,
	limit_param integer DEFAULT 50,
	offset_param integer DEFAULT 0
) RETURNS TABLE (
	tender_id uuid,
	vendor_name text,
	tender_name text,
	description text,
	submission_date date,
	currency_code text,
	currency_symbol text,
	status tender_status,
	notes text,
	current_revision_id uuid,
	revision_number integer,
	line_item_count bigint,
	total_amount numeric,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        t.tender_id,
        v.name as vendor_name,
        t.tender_name,
        t.description,
        t.submission_date,
        t.currency_code,
        c.symbol as currency_symbol,
        t.status,
        t.notes,
        tr.tender_revision_id as current_revision_id,
        tr.revision_number,
        COALESCE(line_counts.line_item_count, 0) as line_item_count,
        COALESCE(line_totals.total_amount, 0) as total_amount,
        t.created_at,
        t.updated_at
    FROM public.tender t
    JOIN public.vendor v ON t.vendor_id = v.vendor_id
    JOIN public.currency c ON t.currency_code = c.currency_code
    LEFT JOIN public.tender_revision tr ON t.tender_id = tr.tender_id AND tr.is_current = true
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            COUNT(tli.tender_line_item_id) as line_item_count
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_counts ON t.tender_id = line_counts.tender_id
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            SUM(tli.subtotal) as total_amount
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_totals ON t.tender_id = line_totals.tender_id
    WHERE t.project_id = project_id_param
    AND (status_filter IS NULL OR t.status = status_filter)
    AND (vendor_filter IS NULL OR t.vendor_id = vendor_filter)
    ORDER BY t.submission_date DESC, t.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_tender_comparison_data (
	project_id_param uuid,
	tender_ids uuid[] DEFAULT NULL::uuid[],
	status_filter tender_status[] DEFAULT NULL::tender_status[]
) RETURNS TABLE (
	wbs_code text,
	wbs_description text,
	wbs_library_item_id uuid,
	budget_amount numeric,
	tender_data jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    WITH project_wbs AS (
        -- Get all WBS items for the project with budget amounts
        SELECT 
            wli.wbs_library_item_id,
            wli.wbs_code,
            wli.description,
            bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1) as budget_amount
        FROM public.wbs_library_item wli
        JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
        JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
        JOIN public.project p ON bv.project_id = p.project_id
        WHERE p.project_id = project_id_param
        AND p.active_budget_version_id = bv.budget_version_id
    ),
    filtered_tenders AS (
        -- Get tenders based on filters
        SELECT t.tender_id, t.tender_name, t.status
        FROM public.tender t
        WHERE t.project_id = project_id_param
        AND (tender_ids IS NULL OR t.tender_id = ANY(tender_ids))
        AND (status_filter IS NULL OR t.status = ANY(status_filter))
    ),
    tender_line_data AS (
        -- Get line item data for each tender mapped to WBS items
        SELECT 
            twm.wbs_library_item_id,
            ft.tender_id,
            ft.tender_name,
            ft.status,
            jsonb_build_object(
                'tender_id', ft.tender_id,
                'tender_name', ft.tender_name,
                'line_items', jsonb_agg(
                    jsonb_build_object(
                        'line_item_id', tli.tender_line_item_id,
                        'description', tli.description,
                        'quantity', tli.quantity,
                        'unit', tli.unit,
                        'unit_rate', tli.unit_rate,
                        'subtotal', tli.subtotal,
                        'coverage_percentage', twm.coverage_percentage,
                        'coverage_quantity', twm.coverage_quantity,
                        'normalization_type', tli.normalization_type,
                        'normalization_amount', tli.normalization_amount,
                        'normalization_percentage', tli.normalization_percentage
                    ) ORDER BY tli.line_number
                )
            ) as tender_data
        FROM public.tender_wbs_mapping twm
        JOIN public.tender_line_item tli ON twm.tender_line_item_id = tli.tender_line_item_id
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id AND tr.is_current = true
        JOIN filtered_tenders ft ON tr.tender_id = ft.tender_id
        GROUP BY twm.wbs_library_item_id, ft.tender_id, ft.tender_name, ft.status
    )
    SELECT 
        pw.wbs_code,
        pw.description as wbs_description,
        pw.wbs_library_item_id,
        pw.budget_amount,
        COALESCE(
            jsonb_agg(tld.tender_data ORDER BY tld.tender_name) FILTER (WHERE tld.tender_data IS NOT NULL),
            '[]'::jsonb
        ) as tender_data
    FROM project_wbs pw
    LEFT JOIN tender_line_data tld ON pw.wbs_library_item_id = tld.wbs_library_item_id
    GROUP BY pw.wbs_library_item_id, pw.wbs_code, pw.description, pw.budget_amount
    ORDER BY pw.wbs_code;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_budget_transfer (
	project_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric
) RETURNS TABLE (
	is_valid boolean,
	error_message text,
	from_available_amount numeric,
	to_current_amount numeric,
	existing_transfers numeric
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    from_budget_amount numeric(20, 4);
    from_transferred_out numeric(20, 4);
    from_transferred_in numeric(20, 4);
    available_amount numeric(20, 4);
    to_budget_amount numeric(20, 4);
    to_transferred_in numeric(20, 4);
    to_transferred_out numeric(20, 4);
    current_to_amount numeric(20, 4);
    existing_transfer_amount numeric(20, 4);
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Basic input validation before looking up budget data
    IF transfer_amount IS NULL THEN
        RETURN QUERY
        SELECT
            false,
            'Transfer amount is required',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    IF from_wbs_item_id = to_wbs_item_id THEN
        RETURN QUERY
        SELECT
            false,
            'Source and target WBS items must be different',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    -- Get original budget amount for from_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO from_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = from_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Get original budget amount for to_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO to_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = to_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate existing transfers
    SELECT
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO from_transferred_out, from_transferred_in
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    SELECT
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO to_transferred_in, to_transferred_out
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    -- Check for existing transfer between these specific items
    SELECT COALESCE(SUM(transfer_amount), 0)
    INTO existing_transfer_amount
    FROM public.budget_transfer
    WHERE project_id = project_id_param
    AND from_wbs_library_item_id = from_wbs_item_id
    AND to_wbs_library_item_id = to_wbs_item_id;

    -- Calculate available amount (original budget + transfers in - transfers out)
    available_amount := COALESCE(from_budget_amount, 0) + COALESCE(from_transferred_in, 0) - COALESCE(from_transferred_out, 0);
    current_to_amount := COALESCE(to_budget_amount, 0) + COALESCE(to_transferred_in, 0) - COALESCE(to_transferred_out, 0);

    -- Validate transfer
    RETURN QUERY
    SELECT
        CASE
            WHEN from_budget_amount IS NULL THEN false
            WHEN to_budget_amount IS NULL THEN false
            WHEN transfer_amount <= 0 THEN false
            WHEN transfer_amount > available_amount THEN false
            ELSE true
        END as is_valid,
        CASE
            WHEN from_budget_amount IS NULL THEN 'Source WBS item not found in budget'
            WHEN to_budget_amount IS NULL THEN 'Target WBS item not found in budget'
            WHEN transfer_amount <= 0 THEN 'Transfer amount must be positive'
            WHEN transfer_amount > available_amount THEN 'Transfer amount exceeds available budget'
            ELSE NULL
        END as error_message,
        available_amount as from_available_amount,
        current_to_amount as to_current_amount,
        existing_transfer_amount as existing_transfers;
END;
$function$;

grant delete on table "public"."budget_transfer" to "anon";

grant insert on table "public"."budget_transfer" to "anon";

grant references on table "public"."budget_transfer" to "anon";

grant
select
	on table "public"."budget_transfer" to "anon";

grant trigger on table "public"."budget_transfer" to "anon";

grant
truncate on table "public"."budget_transfer" to "anon";

grant
update on table "public"."budget_transfer" to "anon";

grant delete on table "public"."budget_transfer" to "authenticated";

grant insert on table "public"."budget_transfer" to "authenticated";

grant references on table "public"."budget_transfer" to "authenticated";

grant
select
	on table "public"."budget_transfer" to "authenticated";

grant trigger on table "public"."budget_transfer" to "authenticated";

grant
truncate on table "public"."budget_transfer" to "authenticated";

grant
update on table "public"."budget_transfer" to "authenticated";

grant delete on table "public"."budget_transfer" to "service_role";

grant insert on table "public"."budget_transfer" to "service_role";

grant references on table "public"."budget_transfer" to "service_role";

grant
select
	on table "public"."budget_transfer" to "service_role";

grant trigger on table "public"."budget_transfer" to "service_role";

grant
truncate on table "public"."budget_transfer" to "service_role";

grant
update on table "public"."budget_transfer" to "service_role";

grant delete on table "public"."budget_transfer_audit" to "anon";

grant insert on table "public"."budget_transfer_audit" to "anon";

grant references on table "public"."budget_transfer_audit" to "anon";

grant
select
	on table "public"."budget_transfer_audit" to "anon";

grant trigger on table "public"."budget_transfer_audit" to "anon";

grant
truncate on table "public"."budget_transfer_audit" to "anon";

grant
update on table "public"."budget_transfer_audit" to "anon";

grant delete on table "public"."budget_transfer_audit" to "authenticated";

grant insert on table "public"."budget_transfer_audit" to "authenticated";

grant references on table "public"."budget_transfer_audit" to "authenticated";

grant
select
	on table "public"."budget_transfer_audit" to "authenticated";

grant trigger on table "public"."budget_transfer_audit" to "authenticated";

grant
truncate on table "public"."budget_transfer_audit" to "authenticated";

grant
update on table "public"."budget_transfer_audit" to "authenticated";

grant delete on table "public"."budget_transfer_audit" to "service_role";

grant insert on table "public"."budget_transfer_audit" to "service_role";

grant references on table "public"."budget_transfer_audit" to "service_role";

grant
select
	on table "public"."budget_transfer_audit" to "service_role";

grant trigger on table "public"."budget_transfer_audit" to "service_role";

grant
truncate on table "public"."budget_transfer_audit" to "service_role";

grant
update on table "public"."budget_transfer_audit" to "service_role";

grant delete on table "public"."currency" to "anon";

grant insert on table "public"."currency" to "anon";

grant references on table "public"."currency" to "anon";

grant
select
	on table "public"."currency" to "anon";

grant trigger on table "public"."currency" to "anon";

grant
truncate on table "public"."currency" to "anon";

grant
update on table "public"."currency" to "anon";

grant delete on table "public"."currency" to "authenticated";

grant insert on table "public"."currency" to "authenticated";

grant references on table "public"."currency" to "authenticated";

grant
select
	on table "public"."currency" to "authenticated";

grant trigger on table "public"."currency" to "authenticated";

grant
truncate on table "public"."currency" to "authenticated";

grant
update on table "public"."currency" to "authenticated";

grant delete on table "public"."currency" to "service_role";

grant insert on table "public"."currency" to "service_role";

grant references on table "public"."currency" to "service_role";

grant
select
	on table "public"."currency" to "service_role";

grant trigger on table "public"."currency" to "service_role";

grant
truncate on table "public"."currency" to "service_role";

grant
update on table "public"."currency" to "service_role";

grant delete on table "public"."tender" to "anon";

grant insert on table "public"."tender" to "anon";

grant references on table "public"."tender" to "anon";

grant
select
	on table "public"."tender" to "anon";

grant trigger on table "public"."tender" to "anon";

grant
truncate on table "public"."tender" to "anon";

grant
update on table "public"."tender" to "anon";

grant delete on table "public"."tender" to "authenticated";

grant insert on table "public"."tender" to "authenticated";

grant references on table "public"."tender" to "authenticated";

grant
select
	on table "public"."tender" to "authenticated";

grant trigger on table "public"."tender" to "authenticated";

grant
truncate on table "public"."tender" to "authenticated";

grant
update on table "public"."tender" to "authenticated";

grant delete on table "public"."tender" to "service_role";

grant insert on table "public"."tender" to "service_role";

grant references on table "public"."tender" to "service_role";

grant
select
	on table "public"."tender" to "service_role";

grant trigger on table "public"."tender" to "service_role";

grant
truncate on table "public"."tender" to "service_role";

grant
update on table "public"."tender" to "service_role";

grant delete on table "public"."tender_audit" to "anon";

grant insert on table "public"."tender_audit" to "anon";

grant references on table "public"."tender_audit" to "anon";

grant
select
	on table "public"."tender_audit" to "anon";

grant trigger on table "public"."tender_audit" to "anon";

grant
truncate on table "public"."tender_audit" to "anon";

grant
update on table "public"."tender_audit" to "anon";

grant delete on table "public"."tender_audit" to "authenticated";

grant insert on table "public"."tender_audit" to "authenticated";

grant references on table "public"."tender_audit" to "authenticated";

grant
select
	on table "public"."tender_audit" to "authenticated";

grant trigger on table "public"."tender_audit" to "authenticated";

grant
truncate on table "public"."tender_audit" to "authenticated";

grant
update on table "public"."tender_audit" to "authenticated";

grant delete on table "public"."tender_audit" to "service_role";

grant insert on table "public"."tender_audit" to "service_role";

grant references on table "public"."tender_audit" to "service_role";

grant
select
	on table "public"."tender_audit" to "service_role";

grant trigger on table "public"."tender_audit" to "service_role";

grant
truncate on table "public"."tender_audit" to "service_role";

grant
update on table "public"."tender_audit" to "service_role";

grant delete on table "public"."tender_line_item" to "anon";

grant insert on table "public"."tender_line_item" to "anon";

grant references on table "public"."tender_line_item" to "anon";

grant
select
	on table "public"."tender_line_item" to "anon";

grant trigger on table "public"."tender_line_item" to "anon";

grant
truncate on table "public"."tender_line_item" to "anon";

grant
update on table "public"."tender_line_item" to "anon";

grant delete on table "public"."tender_line_item" to "authenticated";

grant insert on table "public"."tender_line_item" to "authenticated";

grant references on table "public"."tender_line_item" to "authenticated";

grant
select
	on table "public"."tender_line_item" to "authenticated";

grant trigger on table "public"."tender_line_item" to "authenticated";

grant
truncate on table "public"."tender_line_item" to "authenticated";

grant
update on table "public"."tender_line_item" to "authenticated";

grant delete on table "public"."tender_line_item" to "service_role";

grant insert on table "public"."tender_line_item" to "service_role";

grant references on table "public"."tender_line_item" to "service_role";

grant
select
	on table "public"."tender_line_item" to "service_role";

grant trigger on table "public"."tender_line_item" to "service_role";

grant
truncate on table "public"."tender_line_item" to "service_role";

grant
update on table "public"."tender_line_item" to "service_role";

grant delete on table "public"."tender_line_item_audit" to "anon";

grant insert on table "public"."tender_line_item_audit" to "anon";

grant references on table "public"."tender_line_item_audit" to "anon";

grant
select
	on table "public"."tender_line_item_audit" to "anon";

grant trigger on table "public"."tender_line_item_audit" to "anon";

grant
truncate on table "public"."tender_line_item_audit" to "anon";

grant
update on table "public"."tender_line_item_audit" to "anon";

grant delete on table "public"."tender_line_item_audit" to "authenticated";

grant insert on table "public"."tender_line_item_audit" to "authenticated";

grant references on table "public"."tender_line_item_audit" to "authenticated";

grant
select
	on table "public"."tender_line_item_audit" to "authenticated";

grant trigger on table "public"."tender_line_item_audit" to "authenticated";

grant
truncate on table "public"."tender_line_item_audit" to "authenticated";

grant
update on table "public"."tender_line_item_audit" to "authenticated";

grant delete on table "public"."tender_line_item_audit" to "service_role";

grant insert on table "public"."tender_line_item_audit" to "service_role";

grant references on table "public"."tender_line_item_audit" to "service_role";

grant
select
	on table "public"."tender_line_item_audit" to "service_role";

grant trigger on table "public"."tender_line_item_audit" to "service_role";

grant
truncate on table "public"."tender_line_item_audit" to "service_role";

grant
update on table "public"."tender_line_item_audit" to "service_role";

grant delete on table "public"."tender_revision" to "anon";

grant insert on table "public"."tender_revision" to "anon";

grant references on table "public"."tender_revision" to "anon";

grant
select
	on table "public"."tender_revision" to "anon";

grant trigger on table "public"."tender_revision" to "anon";

grant
truncate on table "public"."tender_revision" to "anon";

grant
update on table "public"."tender_revision" to "anon";

grant delete on table "public"."tender_revision" to "authenticated";

grant insert on table "public"."tender_revision" to "authenticated";

grant references on table "public"."tender_revision" to "authenticated";

grant
select
	on table "public"."tender_revision" to "authenticated";

grant trigger on table "public"."tender_revision" to "authenticated";

grant
truncate on table "public"."tender_revision" to "authenticated";

grant
update on table "public"."tender_revision" to "authenticated";

grant delete on table "public"."tender_revision" to "service_role";

grant insert on table "public"."tender_revision" to "service_role";

grant references on table "public"."tender_revision" to "service_role";

grant
select
	on table "public"."tender_revision" to "service_role";

grant trigger on table "public"."tender_revision" to "service_role";

grant
truncate on table "public"."tender_revision" to "service_role";

grant
update on table "public"."tender_revision" to "service_role";

grant delete on table "public"."tender_revision_audit" to "anon";

grant insert on table "public"."tender_revision_audit" to "anon";

grant references on table "public"."tender_revision_audit" to "anon";

grant
select
	on table "public"."tender_revision_audit" to "anon";

grant trigger on table "public"."tender_revision_audit" to "anon";

grant
truncate on table "public"."tender_revision_audit" to "anon";

grant
update on table "public"."tender_revision_audit" to "anon";

grant delete on table "public"."tender_revision_audit" to "authenticated";

grant insert on table "public"."tender_revision_audit" to "authenticated";

grant references on table "public"."tender_revision_audit" to "authenticated";

grant
select
	on table "public"."tender_revision_audit" to "authenticated";

grant trigger on table "public"."tender_revision_audit" to "authenticated";

grant
truncate on table "public"."tender_revision_audit" to "authenticated";

grant
update on table "public"."tender_revision_audit" to "authenticated";

grant delete on table "public"."tender_revision_audit" to "service_role";

grant insert on table "public"."tender_revision_audit" to "service_role";

grant references on table "public"."tender_revision_audit" to "service_role";

grant
select
	on table "public"."tender_revision_audit" to "service_role";

grant trigger on table "public"."tender_revision_audit" to "service_role";

grant
truncate on table "public"."tender_revision_audit" to "service_role";

grant
update on table "public"."tender_revision_audit" to "service_role";

grant delete on table "public"."tender_score" to "anon";

grant insert on table "public"."tender_score" to "anon";

grant references on table "public"."tender_score" to "anon";

grant
select
	on table "public"."tender_score" to "anon";

grant trigger on table "public"."tender_score" to "anon";

grant
truncate on table "public"."tender_score" to "anon";

grant
update on table "public"."tender_score" to "anon";

grant delete on table "public"."tender_score" to "authenticated";

grant insert on table "public"."tender_score" to "authenticated";

grant references on table "public"."tender_score" to "authenticated";

grant
select
	on table "public"."tender_score" to "authenticated";

grant trigger on table "public"."tender_score" to "authenticated";

grant
truncate on table "public"."tender_score" to "authenticated";

grant
update on table "public"."tender_score" to "authenticated";

grant delete on table "public"."tender_score" to "service_role";

grant insert on table "public"."tender_score" to "service_role";

grant references on table "public"."tender_score" to "service_role";

grant
select
	on table "public"."tender_score" to "service_role";

grant trigger on table "public"."tender_score" to "service_role";

grant
truncate on table "public"."tender_score" to "service_role";

grant
update on table "public"."tender_score" to "service_role";

grant delete on table "public"."tender_score_audit" to "anon";

grant insert on table "public"."tender_score_audit" to "anon";

grant references on table "public"."tender_score_audit" to "anon";

grant
select
	on table "public"."tender_score_audit" to "anon";

grant trigger on table "public"."tender_score_audit" to "anon";

grant
truncate on table "public"."tender_score_audit" to "anon";

grant
update on table "public"."tender_score_audit" to "anon";

grant delete on table "public"."tender_score_audit" to "authenticated";

grant insert on table "public"."tender_score_audit" to "authenticated";

grant references on table "public"."tender_score_audit" to "authenticated";

grant
select
	on table "public"."tender_score_audit" to "authenticated";

grant trigger on table "public"."tender_score_audit" to "authenticated";

grant
truncate on table "public"."tender_score_audit" to "authenticated";

grant
update on table "public"."tender_score_audit" to "authenticated";

grant delete on table "public"."tender_score_audit" to "service_role";

grant insert on table "public"."tender_score_audit" to "service_role";

grant references on table "public"."tender_score_audit" to "service_role";

grant
select
	on table "public"."tender_score_audit" to "service_role";

grant trigger on table "public"."tender_score_audit" to "service_role";

grant
truncate on table "public"."tender_score_audit" to "service_role";

grant
update on table "public"."tender_score_audit" to "service_role";

grant delete on table "public"."tender_scoring_criteria" to "anon";

grant insert on table "public"."tender_scoring_criteria" to "anon";

grant references on table "public"."tender_scoring_criteria" to "anon";

grant
select
	on table "public"."tender_scoring_criteria" to "anon";

grant trigger on table "public"."tender_scoring_criteria" to "anon";

grant
truncate on table "public"."tender_scoring_criteria" to "anon";

grant
update on table "public"."tender_scoring_criteria" to "anon";

grant delete on table "public"."tender_scoring_criteria" to "authenticated";

grant insert on table "public"."tender_scoring_criteria" to "authenticated";

grant references on table "public"."tender_scoring_criteria" to "authenticated";

grant
select
	on table "public"."tender_scoring_criteria" to "authenticated";

grant trigger on table "public"."tender_scoring_criteria" to "authenticated";

grant
truncate on table "public"."tender_scoring_criteria" to "authenticated";

grant
update on table "public"."tender_scoring_criteria" to "authenticated";

grant delete on table "public"."tender_scoring_criteria" to "service_role";

grant insert on table "public"."tender_scoring_criteria" to "service_role";

grant references on table "public"."tender_scoring_criteria" to "service_role";

grant
select
	on table "public"."tender_scoring_criteria" to "service_role";

grant trigger on table "public"."tender_scoring_criteria" to "service_role";

grant
truncate on table "public"."tender_scoring_criteria" to "service_role";

grant
update on table "public"."tender_scoring_criteria" to "service_role";

grant delete on table "public"."tender_scoring_criteria_audit" to "anon";

grant insert on table "public"."tender_scoring_criteria_audit" to "anon";

grant references on table "public"."tender_scoring_criteria_audit" to "anon";

grant
select
	on table "public"."tender_scoring_criteria_audit" to "anon";

grant trigger on table "public"."tender_scoring_criteria_audit" to "anon";

grant
truncate on table "public"."tender_scoring_criteria_audit" to "anon";

grant
update on table "public"."tender_scoring_criteria_audit" to "anon";

grant delete on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant insert on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant references on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant
select
	on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant trigger on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant
truncate on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant
update on table "public"."tender_scoring_criteria_audit" to "authenticated";

grant delete on table "public"."tender_scoring_criteria_audit" to "service_role";

grant insert on table "public"."tender_scoring_criteria_audit" to "service_role";

grant references on table "public"."tender_scoring_criteria_audit" to "service_role";

grant
select
	on table "public"."tender_scoring_criteria_audit" to "service_role";

grant trigger on table "public"."tender_scoring_criteria_audit" to "service_role";

grant
truncate on table "public"."tender_scoring_criteria_audit" to "service_role";

grant
update on table "public"."tender_scoring_criteria_audit" to "service_role";

grant delete on table "public"."tender_wbs_mapping" to "anon";

grant insert on table "public"."tender_wbs_mapping" to "anon";

grant references on table "public"."tender_wbs_mapping" to "anon";

grant
select
	on table "public"."tender_wbs_mapping" to "anon";

grant trigger on table "public"."tender_wbs_mapping" to "anon";

grant
truncate on table "public"."tender_wbs_mapping" to "anon";

grant
update on table "public"."tender_wbs_mapping" to "anon";

grant delete on table "public"."tender_wbs_mapping" to "authenticated";

grant insert on table "public"."tender_wbs_mapping" to "authenticated";

grant references on table "public"."tender_wbs_mapping" to "authenticated";

grant
select
	on table "public"."tender_wbs_mapping" to "authenticated";

grant trigger on table "public"."tender_wbs_mapping" to "authenticated";

grant
truncate on table "public"."tender_wbs_mapping" to "authenticated";

grant
update on table "public"."tender_wbs_mapping" to "authenticated";

grant delete on table "public"."tender_wbs_mapping" to "service_role";

grant insert on table "public"."tender_wbs_mapping" to "service_role";

grant references on table "public"."tender_wbs_mapping" to "service_role";

grant
select
	on table "public"."tender_wbs_mapping" to "service_role";

grant trigger on table "public"."tender_wbs_mapping" to "service_role";

grant
truncate on table "public"."tender_wbs_mapping" to "service_role";

grant
update on table "public"."tender_wbs_mapping" to "service_role";

grant delete on table "public"."tender_wbs_mapping_audit" to "anon";

grant insert on table "public"."tender_wbs_mapping_audit" to "anon";

grant references on table "public"."tender_wbs_mapping_audit" to "anon";

grant
select
	on table "public"."tender_wbs_mapping_audit" to "anon";

grant trigger on table "public"."tender_wbs_mapping_audit" to "anon";

grant
truncate on table "public"."tender_wbs_mapping_audit" to "anon";

grant
update on table "public"."tender_wbs_mapping_audit" to "anon";

grant delete on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant insert on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant references on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant
select
	on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant trigger on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant
truncate on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant
update on table "public"."tender_wbs_mapping_audit" to "authenticated";

grant delete on table "public"."tender_wbs_mapping_audit" to "service_role";

grant insert on table "public"."tender_wbs_mapping_audit" to "service_role";

grant references on table "public"."tender_wbs_mapping_audit" to "service_role";

grant
select
	on table "public"."tender_wbs_mapping_audit" to "service_role";

grant trigger on table "public"."tender_wbs_mapping_audit" to "service_role";

grant
truncate on table "public"."tender_wbs_mapping_audit" to "service_role";

grant
update on table "public"."tender_wbs_mapping_audit" to "service_role";

grant delete on table "public"."tender_work_package" to "anon";

grant insert on table "public"."tender_work_package" to "anon";

grant references on table "public"."tender_work_package" to "anon";

grant
select
	on table "public"."tender_work_package" to "anon";

grant trigger on table "public"."tender_work_package" to "anon";

grant
truncate on table "public"."tender_work_package" to "anon";

grant
update on table "public"."tender_work_package" to "anon";

grant delete on table "public"."tender_work_package" to "authenticated";

grant insert on table "public"."tender_work_package" to "authenticated";

grant references on table "public"."tender_work_package" to "authenticated";

grant
select
	on table "public"."tender_work_package" to "authenticated";

grant trigger on table "public"."tender_work_package" to "authenticated";

grant
truncate on table "public"."tender_work_package" to "authenticated";

grant
update on table "public"."tender_work_package" to "authenticated";

grant delete on table "public"."tender_work_package" to "service_role";

grant insert on table "public"."tender_work_package" to "service_role";

grant references on table "public"."tender_work_package" to "service_role";

grant
select
	on table "public"."tender_work_package" to "service_role";

grant trigger on table "public"."tender_work_package" to "service_role";

grant
truncate on table "public"."tender_work_package" to "service_role";

grant
update on table "public"."tender_work_package" to "service_role";

grant delete on table "public"."tender_work_package_audit" to "anon";

grant insert on table "public"."tender_work_package_audit" to "anon";

grant references on table "public"."tender_work_package_audit" to "anon";

grant
select
	on table "public"."tender_work_package_audit" to "anon";

grant trigger on table "public"."tender_work_package_audit" to "anon";

grant
truncate on table "public"."tender_work_package_audit" to "anon";

grant
update on table "public"."tender_work_package_audit" to "anon";

grant delete on table "public"."tender_work_package_audit" to "authenticated";

grant insert on table "public"."tender_work_package_audit" to "authenticated";

grant references on table "public"."tender_work_package_audit" to "authenticated";

grant
select
	on table "public"."tender_work_package_audit" to "authenticated";

grant trigger on table "public"."tender_work_package_audit" to "authenticated";

grant
truncate on table "public"."tender_work_package_audit" to "authenticated";

grant
update on table "public"."tender_work_package_audit" to "authenticated";

grant delete on table "public"."tender_work_package_audit" to "service_role";

grant insert on table "public"."tender_work_package_audit" to "service_role";

grant references on table "public"."tender_work_package_audit" to "service_role";

grant
select
	on table "public"."tender_work_package_audit" to "service_role";

grant trigger on table "public"."tender_work_package_audit" to "service_role";

grant
truncate on table "public"."tender_work_package_audit" to "service_role";

grant
update on table "public"."tender_work_package_audit" to "service_role";

create policy "Users can create budget transfers for projects they have editor" on "public"."budget_transfer" as permissive for insert to authenticated
with
	check (
		(
			current_user_has_entity_role (
				'project'::entity_type,
				project_id,
				'editor'::membership_role
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete budget transfers for projects they have admin " on "public"."budget_transfer" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'admin'::membership_role
	)
);

create policy "Users can update budget transfers for projects they have editor" on "public"."budget_transfer" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view budget transfers for projects they have access t" on "public"."budget_transfer" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "Users can view audit records for budget transfers they have acc" on "public"."budget_transfer_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						budget_transfer bt
					WHERE
						(
							(
								bt.budget_transfer_id = budget_transfer_audit.budget_transfer_id
							)
							AND current_user_has_entity_access ('project'::entity_type, bt.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (project_id IS NOT NULL)
				AND current_user_has_entity_access ('project'::entity_type, project_id)
			)
		)
	);

create policy "All authenticated users can view currencies" on "public"."currency" as permissive for
select
	to authenticated using (true);

create policy "Only admin users can create currencies" on "public"."currency" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						membership m
					WHERE
						(
							(m.user_id = auth.uid ())
							AND (m.role >= 'admin'::membership_role)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Only admin users can delete currencies" on "public"."currency" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				membership m
			WHERE
				(
					(m.user_id = auth.uid ())
					AND (m.role >= 'admin'::membership_role)
				)
		)
	)
);

create policy "Only admin users can update currencies" on "public"."currency" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					membership m
				WHERE
					(
						(m.user_id = auth.uid ())
						AND (m.role >= 'admin'::membership_role)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					membership m
				WHERE
					(
						(m.user_id = auth.uid ())
						AND (m.role >= 'admin'::membership_role)
					)
			)
		)
	);

create policy "Users can create tenders for projects they have editor access t" on "public"."tender" as permissive for insert to authenticated
with
	check (
		(
			current_user_has_entity_role (
				'project'::entity_type,
				project_id,
				'editor'::membership_role
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete tenders for projects they have admin access to" on "public"."tender" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'admin'::membership_role
	)
);

create policy "Users can update tenders for projects they have editor access t" on "public"."tender" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view tenders for projects they have access to" on "public"."tender" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "Users can view audit records for tenders they have access to" on "public"."tender_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						tender t
					WHERE
						(
							(t.tender_id = tender_audit.tender_id)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (project_id IS NOT NULL)
				AND current_user_has_entity_access ('project'::entity_type, project_id)
			)
		)
	);

create policy "Users can create line items for tenders they have editor access" on "public"."tender_line_item" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							tender_revision tr
							JOIN tender t ON ((tr.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								tr.tender_revision_id = tender_line_item.tender_revision_id
							)
							AND current_user_has_entity_role (
								'project'::entity_type,
								t.project_id,
								'editor'::membership_role
							)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete line items for tenders they have editor access" on "public"."tender_line_item" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				(
					tender_revision tr
					JOIN tender t ON ((tr.tender_id = t.tender_id))
				)
			WHERE
				(
					(
						tr.tender_revision_id = tender_line_item.tender_revision_id
					)
					AND current_user_has_entity_role (
						'project'::entity_type,
						t.project_id,
						'editor'::membership_role
					)
				)
		)
	)
);

create policy "Users can update line items for tenders they have editor access" on "public"."tender_line_item" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						tender_revision tr
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tr.tender_revision_id = tender_line_item.tender_revision_id
						)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						tender_revision tr
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tr.tender_revision_id = tender_line_item.tender_revision_id
						)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	);

create policy "Users can view line items for tenders they have access to" on "public"."tender_line_item" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						tender_revision tr
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tr.tender_revision_id = tender_line_item.tender_revision_id
						)
						AND current_user_has_entity_access ('project'::entity_type, t.project_id)
					)
			)
		)
	);

create policy "Users can view audit records for tender line items they have ac" on "public"."tender_line_item_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							(
								tender_line_item tli
								JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
							)
							JOIN tender t ON ((tr.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								tli.tender_line_item_id = tender_line_item_audit.tender_line_item_id
							)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (tender_revision_id IS NOT NULL)
				AND (
					EXISTS (
						SELECT
							1
						FROM
							(
								tender_revision tr
								JOIN tender t ON ((tr.tender_id = t.tender_id))
							)
						WHERE
							(
								(
									tr.tender_revision_id = tender_line_item_audit.tender_revision_id
								)
								AND current_user_has_entity_access ('project'::entity_type, t.project_id)
							)
					)
				)
			)
		)
	);

create policy "Users can create revisions for tenders they have editor access " on "public"."tender_revision" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						tender t
					WHERE
						(
							(t.tender_id = tender_revision.tender_id)
							AND current_user_has_entity_role (
								'project'::entity_type,
								t.project_id,
								'editor'::membership_role
							)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete revisions for tenders they have admin access t" on "public"."tender_revision" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				tender t
			WHERE
				(
					(t.tender_id = tender_revision.tender_id)
					AND current_user_has_entity_role (
						'project'::entity_type,
						t.project_id,
						'admin'::membership_role
					)
				)
		)
	)
);

create policy "Users can update revisions for tenders they have editor access " on "public"."tender_revision" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_revision.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_revision.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	);

create policy "Users can view revisions for tenders they have access to" on "public"."tender_revision" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_revision.tender_id)
						AND current_user_has_entity_access ('project'::entity_type, t.project_id)
					)
			)
		)
	);

create policy "Users can view audit records for tender revisions they have acc" on "public"."tender_revision_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							tender_revision tr
							JOIN tender t ON ((tr.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								tr.tender_revision_id = tender_revision_audit.tender_revision_id
							)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (tender_id IS NOT NULL)
				AND (
					EXISTS (
						SELECT
							1
						FROM
							tender t
						WHERE
							(
								(t.tender_id = tender_revision_audit.tender_id)
								AND current_user_has_entity_access ('project'::entity_type, t.project_id)
							)
					)
				)
			)
		)
	);

create policy "Users can create scores for tenders they have editor access to" on "public"."tender_score" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						tender t
					WHERE
						(
							(t.tender_id = tender_score.tender_id)
							AND current_user_has_entity_role (
								'project'::entity_type,
								t.project_id,
								'editor'::membership_role
							)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete scores for tenders they have editor access to" on "public"."tender_score" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				tender t
			WHERE
				(
					(t.tender_id = tender_score.tender_id)
					AND current_user_has_entity_role (
						'project'::entity_type,
						t.project_id,
						'editor'::membership_role
					)
				)
		)
	)
);

create policy "Users can update scores for tenders they have editor access to" on "public"."tender_score" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_score.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_score.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	);

create policy "Users can view scores for tenders they have access to" on "public"."tender_score" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_score.tender_id)
						AND current_user_has_entity_access ('project'::entity_type, t.project_id)
					)
			)
		)
	);

create policy "Users can view audit records for tender scores they have access" on "public"."tender_score_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							tender_score ts
							JOIN tender t ON ((ts.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								ts.tender_score_id = tender_score_audit.tender_score_id
							)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (tender_id IS NOT NULL)
				AND (
					EXISTS (
						SELECT
							1
						FROM
							tender t
						WHERE
							(
								(t.tender_id = tender_score_audit.tender_id)
								AND current_user_has_entity_access ('project'::entity_type, t.project_id)
							)
					)
				)
			)
		)
	);

create policy "Users can create scoring criteria for projects they have editor" on "public"."tender_scoring_criteria" as permissive for insert to authenticated
with
	check (
		(
			current_user_has_entity_role (
				'project'::entity_type,
				project_id,
				'editor'::membership_role
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete scoring criteria for projects they have admin " on "public"."tender_scoring_criteria" as permissive for delete to authenticated using (
	current_user_has_entity_role (
		'project'::entity_type,
		project_id,
		'admin'::membership_role
	)
);

create policy "Users can update scoring criteria for projects they have editor" on "public"."tender_scoring_criteria" as permissive
for update
	to authenticated using (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	)
with
	check (
		current_user_has_entity_role (
			'project'::entity_type,
			project_id,
			'editor'::membership_role
		)
	);

create policy "Users can view scoring criteria for projects they have access t" on "public"."tender_scoring_criteria" as permissive for
select
	to authenticated using (
		current_user_has_entity_access ('project'::entity_type, project_id)
	);

create policy "Users can view audit records for tender scoring criteria they h" on "public"."tender_scoring_criteria_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						tender_scoring_criteria tsc
					WHERE
						(
							(
								tsc.tender_scoring_criteria_id = tender_scoring_criteria_audit.tender_scoring_criteria_id
							)
							AND current_user_has_entity_access ('project'::entity_type, tsc.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (project_id IS NOT NULL)
				AND current_user_has_entity_access ('project'::entity_type, project_id)
			)
		)
	);

create policy "Users can create mappings for tenders they have editor access t" on "public"."tender_wbs_mapping" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							(
								tender_line_item tli
								JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
							)
							JOIN tender t ON ((tr.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								tli.tender_line_item_id = tender_wbs_mapping.tender_line_item_id
							)
							AND current_user_has_entity_role (
								'project'::entity_type,
								t.project_id,
								'editor'::membership_role
							)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete mappings for tenders they have editor access t" on "public"."tender_wbs_mapping" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				(
					(
						tender_line_item tli
						JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
					)
					JOIN tender t ON ((tr.tender_id = t.tender_id))
				)
			WHERE
				(
					(
						tli.tender_line_item_id = tender_wbs_mapping.tender_line_item_id
					)
					AND current_user_has_entity_role (
						'project'::entity_type,
						t.project_id,
						'editor'::membership_role
					)
				)
		)
	)
);

create policy "Users can update mappings for tenders they have editor access t" on "public"."tender_wbs_mapping" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						(
							tender_line_item tli
							JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
						)
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tli.tender_line_item_id = tender_wbs_mapping.tender_line_item_id
						)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						(
							tender_line_item tli
							JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
						)
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tli.tender_line_item_id = tender_wbs_mapping.tender_line_item_id
						)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	);

create policy "Users can view mappings for tenders they have access to" on "public"."tender_wbs_mapping" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					(
						(
							tender_line_item tli
							JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
						)
						JOIN tender t ON ((tr.tender_id = t.tender_id))
					)
				WHERE
					(
						(
							tli.tender_line_item_id = tender_wbs_mapping.tender_line_item_id
						)
						AND current_user_has_entity_access ('project'::entity_type, t.project_id)
					)
			)
		)
	);

create policy "Users can view audit records for tender WBS mappings they have " on "public"."tender_wbs_mapping_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							(
								(
									tender_wbs_mapping twm
									JOIN tender_line_item tli ON (
										(twm.tender_line_item_id = tli.tender_line_item_id)
									)
								)
								JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
							)
							JOIN tender t ON ((tr.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								twm.tender_wbs_mapping_id = tender_wbs_mapping_audit.tender_wbs_mapping_id
							)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (tender_line_item_id IS NOT NULL)
				AND (
					EXISTS (
						SELECT
							1
						FROM
							(
								(
									tender_line_item tli
									JOIN tender_revision tr ON ((tli.tender_revision_id = tr.tender_revision_id))
								)
								JOIN tender t ON ((tr.tender_id = t.tender_id))
							)
						WHERE
							(
								(
									tli.tender_line_item_id = tender_wbs_mapping_audit.tender_line_item_id
								)
								AND current_user_has_entity_access ('project'::entity_type, t.project_id)
							)
					)
				)
			)
		)
	);

create policy "Users can create work package conversions for tenders they have" on "public"."tender_work_package" as permissive for insert to authenticated
with
	check (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						tender t
					WHERE
						(
							(t.tender_id = tender_work_package.tender_id)
							AND current_user_has_entity_role (
								'project'::entity_type,
								t.project_id,
								'editor'::membership_role
							)
						)
				)
			)
			AND (created_by_user_id = auth.uid ())
		)
	);

create policy "Users can delete work package conversions for tenders they have" on "public"."tender_work_package" as permissive for delete to authenticated using (
	(
		EXISTS (
			SELECT
				1
			FROM
				tender t
			WHERE
				(
					(t.tender_id = tender_work_package.tender_id)
					AND current_user_has_entity_role (
						'project'::entity_type,
						t.project_id,
						'admin'::membership_role
					)
				)
		)
	)
);

create policy "Users can update work package conversions for tenders they have" on "public"."tender_work_package" as permissive
for update
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_work_package.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	)
with
	check (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_work_package.tender_id)
						AND current_user_has_entity_role (
							'project'::entity_type,
							t.project_id,
							'editor'::membership_role
						)
					)
			)
		)
	);

create policy "Users can view work package conversions for tenders they have a" on "public"."tender_work_package" as permissive for
select
	to authenticated using (
		(
			EXISTS (
				SELECT
					1
				FROM
					tender t
				WHERE
					(
						(t.tender_id = tender_work_package.tender_id)
						AND current_user_has_entity_access ('project'::entity_type, t.project_id)
					)
			)
		)
	);

create policy "Users can view audit records for tender work packages they have" on "public"."tender_work_package_audit" as permissive for
select
	to authenticated using (
		(
			(
				EXISTS (
					SELECT
						1
					FROM
						(
							tender_work_package twp
							JOIN tender t ON ((twp.tender_id = t.tender_id))
						)
					WHERE
						(
							(
								twp.tender_work_package_id = tender_work_package_audit.tender_work_package_id
							)
							AND current_user_has_entity_access ('project'::entity_type, t.project_id)
						)
				)
			)
			OR (
				(operation_type = 'DELETE'::text)
				AND (tender_id IS NOT NULL)
				AND (
					EXISTS (
						SELECT
							1
						FROM
							tender t
						WHERE
							(
								(t.tender_id = tender_work_package_audit.tender_id)
								AND current_user_has_entity_access ('project'::entity_type, t.project_id)
							)
					)
				)
			)
		)
	);

CREATE TRIGGER audit_budget_transfer_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.budget_transfer FOR EACH ROW
EXECUTE FUNCTION audit_budget_transfer_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.budget_transfer FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.currency FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender FOR EACH ROW
EXECUTE FUNCTION audit_tender_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_line_item_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_line_item FOR EACH ROW
EXECUTE FUNCTION audit_tender_line_item_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_line_item FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_revision_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_revision FOR EACH ROW
EXECUTE FUNCTION audit_tender_revision_changes ();

CREATE TRIGGER ensure_single_current_revision_trigger BEFORE INSERT
OR
UPDATE ON public.tender_revision FOR EACH ROW
EXECUTE FUNCTION ensure_single_current_revision ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_revision FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_score_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_score FOR EACH ROW
EXECUTE FUNCTION audit_tender_score_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_score FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_scoring_criteria_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_scoring_criteria FOR EACH ROW
EXECUTE FUNCTION audit_tender_scoring_criteria_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_scoring_criteria FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_wbs_mapping_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_wbs_mapping FOR EACH ROW
EXECUTE FUNCTION audit_tender_wbs_mapping_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_wbs_mapping FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();

CREATE TRIGGER audit_tender_work_package_trigger
AFTER INSERT
OR DELETE
OR
UPDATE ON public.tender_work_package FOR EACH ROW
EXECUTE FUNCTION audit_tender_work_package_changes ();

CREATE TRIGGER update_updated_at BEFORE
UPDATE ON public.tender_work_package FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column ();
