set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_and_activate_working_version (
	p_project_id uuid,
	p_label text DEFAULT 'Initial Working Budget'::text
) RETURNS uuid LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
    v_active_version_id uuid;
    v_active_kind public.budget_version_kind;
    v_new_version_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permission check
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('create_and_activate_working_version'),
        hashtext(p_project_id::text)
    );

    -- Resolve active id with row lock on project
    SELECT p.active_budget_version_id
      INTO v_active_version_id
    FROM public.project p
    WHERE p.project_id = p_project_id
    FOR UPDATE;

    -- Resolve active kind separately if present
    IF v_active_version_id IS NOT NULL THEN
        SELECT kind INTO v_active_kind
        FROM public.budget_version
        WHERE budget_version_id = v_active_version_id;
    ELSE
        v_active_kind := NULL;
    END IF;

    -- If not a bootstrap 'system' version, nothing to do
    IF v_active_version_id IS NOT NULL AND v_active_kind IS DISTINCT FROM 'system'::public.budget_version_kind THEN
        RETURN v_active_version_id;
    END IF;

    -- Create a manual version cloned from the current active (if present)
    SELECT public.create_budget_version(
        p_project_id := p_project_id,
        p_label      := p_label,
        p_kind       := 'manual'::public.budget_version_kind,
        p_stage_id   := NULL,
        p_prev_version_id := v_active_version_id
    ) INTO v_new_version_id;

    -- Activate it
    UPDATE public.project
       SET active_budget_version_id = v_new_version_id
     WHERE project_id = p_project_id;

    RETURN v_new_version_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.ensure_editable_and_upsert_budget_item (
	p_project_id uuid,
	p_wbs_library_item_id uuid,
	p_quantity numeric,
	p_unit text DEFAULT NULL::text,
	p_material_rate numeric DEFAULT 0,
	p_labor_rate numeric DEFAULT NULL::numeric,
	p_productivity_per_hour numeric DEFAULT NULL::numeric,
	p_unit_rate_manual_override boolean DEFAULT false,
	p_unit_rate numeric DEFAULT NULL::numeric,
	p_factor numeric DEFAULT NULL::numeric,
	p_remarks text DEFAULT NULL::text,
	p_cost_certainty numeric DEFAULT NULL::numeric,
	p_design_certainty numeric DEFAULT NULL::numeric,
	p_budget_line_item_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
    v_active_version_id uuid;
    v_active_kind public.budget_version_kind;
    v_unit_rate_out numeric;
    v_budget_version_item_id_out uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permissions: must be able to modify the project
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('ensure_editable_and_upsert_budget_item'),
        hashtext(p_project_id::text)
    );

    -- Resolve active version with row lock on project
    SELECT p.active_budget_version_id
      INTO v_active_version_id
    FROM public.project p
    WHERE p.project_id = p_project_id
    FOR UPDATE;

    -- Resolve active kind separately if present
    IF v_active_version_id IS NOT NULL THEN
        SELECT kind INTO v_active_kind
        FROM public.budget_version
        WHERE budget_version_id = v_active_version_id;
    ELSE
        v_active_kind := NULL;
    END IF;

    -- If bootstrap or missing, create+activate a manual working version
    IF v_active_version_id IS NULL OR v_active_kind = 'system'::public.budget_version_kind THEN
        SELECT public.create_and_activate_working_version(p_project_id, 'Initial Working Budget')
        INTO v_active_version_id;
    END IF;

    -- Compute unit rate if not manual override
    IF COALESCE(p_unit_rate_manual_override, FALSE) THEN
        v_unit_rate_out := p_unit_rate;
    ELSE
        v_unit_rate_out := public.calculate_unit_item_cost(
            p_material_rate,
            p_labor_rate,
            p_productivity_per_hour
        );
    END IF;

    -- Update by id when provided, scoped to active version
    IF p_budget_line_item_id IS NOT NULL THEN
        UPDATE public.budget_version_item
           SET wbs_library_item_id = p_wbs_library_item_id,
               quantity = p_quantity,
               unit = p_unit,
               material_rate = p_material_rate,
               labor_rate = p_labor_rate,
               productivity_per_hour = p_productivity_per_hour,
               unit_rate_manual_override = COALESCE(p_unit_rate_manual_override, FALSE),
               unit_rate = v_unit_rate_out,
               factor = p_factor,
               remarks = p_remarks,
               cost_certainty = p_cost_certainty,
               design_certainty = p_design_certainty
         WHERE budget_version_item_id = p_budget_line_item_id
           AND budget_version_id = v_active_version_id
        RETURNING budget_version_item_id INTO v_budget_version_item_id_out;
    END IF;

    -- If no row updated, insert/upsert by key
    IF v_budget_version_item_id_out IS NULL THEN
        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        ) VALUES (
            v_active_version_id,
            p_wbs_library_item_id,
            p_quantity,
            p_unit,
            p_material_rate,
            p_labor_rate,
            p_productivity_per_hour,
            COALESCE(p_unit_rate_manual_override, FALSE),
            v_unit_rate_out,
            p_factor,
            p_remarks,
            p_cost_certainty,
            p_design_certainty
        )
        ON CONFLICT (budget_version_id, wbs_library_item_id) DO UPDATE
        SET
            quantity = EXCLUDED.quantity,
            unit = EXCLUDED.unit,
            material_rate = EXCLUDED.material_rate,
            labor_rate = EXCLUDED.labor_rate,
            productivity_per_hour = EXCLUDED.productivity_per_hour,
            unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
            unit_rate = EXCLUDED.unit_rate,
            factor = EXCLUDED.factor,
            remarks = EXCLUDED.remarks,
            cost_certainty = EXCLUDED.cost_certainty,
            design_certainty = EXCLUDED.design_certainty
        RETURNING budget_version_item_id
        INTO v_budget_version_item_id_out;
    END IF;

    RETURN v_budget_version_item_id_out;
END;
$function$;
