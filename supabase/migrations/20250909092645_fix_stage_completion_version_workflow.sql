drop function if exists "public"."list_budget_versions" (
	p_project_id uuid,
	p_limit integer,
	p_cursor timestamp with time zone
);

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.touch_budget_version_updated_at () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
BEGIN
  UPDATE public.budget_version
     SET updated_at = timezone('utc', now())
   WHERE budget_version_id = COALESCE(NEW.budget_version_id, OLD.budget_version_id);
  RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.complete_project_stage (
	p_project_stage_id uuid,
	p_completion_notes text DEFAULT NULL::text
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_snapshot_id UUID;
	v_project_id UUID;
	v_is_ready BOOLEAN;
	v_stage_version_id UUID;
	v_active_version_id UUID;
	v_user_id UUID;
	v_stage_name TEXT;
    -- No new working version is created here; we only freeze a stage version
BEGIN
	-- Get current user ID
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
	END IF;

	-- Get project_id and stage name for permission check and labeling
	SELECT ps.project_id, ps.name INTO v_project_id, v_stage_name
	FROM public.project_stage ps
	WHERE ps.project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;

	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;

	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;

	-- Get current active budget version for lineage
	SELECT active_budget_version_id INTO v_active_version_id
	FROM public.project
	WHERE project_id = v_project_id;

	-- Create a budget version for this stage completion
	INSERT INTO public.budget_version (
		project_id,
		label,
		kind,
		stage_id,
		prev_version_id,
		created_by_user_id
	) VALUES (
		v_project_id,
		'Stage Completion: ' || v_stage_name,
		'stage'::public.budget_version_kind,
		p_project_stage_id,
		v_active_version_id,
		v_user_id
	) RETURNING budget_version_id INTO v_stage_version_id;

	-- Copy budget items from active version to the new stage version
	INSERT INTO public.budget_version_item (
		budget_version_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
	SELECT
		v_stage_version_id,
		bvi.wbs_library_item_id,
		bvi.quantity,
		bvi.unit,
		bvi.material_rate,
		bvi.labor_rate,
		bvi.productivity_per_hour,
		bvi.unit_rate_manual_override,
		bvi.unit_rate,
		bvi.factor,
		bvi.remarks,
		bvi.cost_certainty,
		bvi.design_certainty
	FROM public.budget_version_item bvi
	WHERE bvi.budget_version_id = v_active_version_id;

	-- Update the stage
	UPDATE public.project_stage
	SET
		date_completed = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;

    -- Create a budget snapshot linked to the stage version (read-only record of the stage)
    SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes, v_stage_version_id) INTO v_snapshot_id;

    -- Do not change the active budget version here to avoid creating redundant
    -- "working" versions per stage. The existing active version continues as the
    -- working copy, while the new stage version + snapshot preserve history.

	RETURN v_snapshot_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.list_budget_versions (
	p_project_id uuid,
	p_limit integer DEFAULT 50,
	p_cursor timestamp with time zone DEFAULT NULL::timestamp with time zone
) RETURNS TABLE (
	budget_version_id uuid,
	label text,
	kind budget_version_kind,
	is_active boolean,
	item_count bigint,
	total_cost numeric,
	created_at timestamp with time zone,
	effective_at timestamp with time zone
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Access check
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: no access to project %', p_project_id;
    END IF;

    RETURN QUERY
    WITH v AS (
        SELECT v.budget_version_id,
               v.label,
               v.kind,
               v.created_at,
               (v.budget_version_id = p.active_budget_version_id) AS is_active
        FROM public.budget_version v
        JOIN public.project p ON p.project_id = v.project_id
        WHERE v.project_id = list_budget_versions.p_project_id
          AND (p_cursor IS NULL OR v.created_at < p_cursor)
        ORDER BY v.created_at DESC
        LIMIT COALESCE(p_limit, 50)
    ), agg AS (
        SELECT i.budget_version_id,
               COUNT(*)::bigint AS item_count,
               COALESCE(SUM(i.quantity * i.unit_rate * COALESCE(i.factor, 1)), 0)::numeric AS total_cost,
               MAX(i.updated_at) AS max_updated_at
        FROM public.budget_version_item i
        WHERE i.budget_version_id IN (SELECT v.budget_version_id FROM v)
        GROUP BY i.budget_version_id
    )
    SELECT v.budget_version_id,
           v.label,
           v.kind,
           v.is_active,
           COALESCE(agg.item_count, 0) AS item_count,
           COALESCE(agg.total_cost, 0) AS total_cost,
           v.created_at,
           COALESCE(agg.max_updated_at, v.created_at) AS effective_at
    FROM v
    LEFT JOIN agg ON v.budget_version_id = agg.budget_version_id
    ORDER BY COALESCE(agg.max_updated_at, v.created_at) DESC;
END;
$function$;

CREATE TRIGGER touch_parent_on_delete
AFTER DELETE ON public.budget_version_item FOR EACH ROW
EXECUTE FUNCTION touch_budget_version_updated_at ();

CREATE TRIGGER touch_parent_on_insert
AFTER INSERT ON public.budget_version_item FOR EACH ROW
EXECUTE FUNCTION touch_budget_version_updated_at ();

CREATE TRIGGER touch_parent_on_update
AFTER
UPDATE ON public.budget_version_item FOR EACH ROW
EXECUTE FUNCTION touch_budget_version_updated_at ();
