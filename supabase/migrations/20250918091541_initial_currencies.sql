-- Seed an initial batch of currencies used by the vendor and tender workflows
INSERT INTO
	public.currency (
		currency_code,
		symbol,
		symbol_position,
		description,
		created_by_user_id
	)
VALUES
	(
		'SEK',
		'kr',
		'after',
		'Swedish Krona',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'EUR',
		'€',
		'before',
		'Euro',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'NOK',
		'kr',
		'after',
		'Norwegian Krone',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'DKK',
		'kr',
		'after',
		'Danish Krone',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'GBP',
		'£',
		'before',
		'British Pound Sterling',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'USD',
		'$',
		'before',
		'United States Dollar',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'CAD',
		'C$',
		'before',
		'Canadian Dollar',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'AUD',
		'A$',
		'before',
		'Australian Dollar',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'JPY',
		'¥',
		'before',
		'Japanese Yen',
		'00000000-0000-0000-0000-000000000000'
	),
	(
		'CHF',
		'CHF',
		'before',
		'Swiss Franc',
		'00000000-0000-0000-0000-000000000000'
	)
ON CONFLICT (currency_code) DO UPDATE
SET
	symbol = EXCLUDED.symbol,
	symbol_position = EXCLUDED.symbol_position,
	description = EXCLUDED.description,
	updated_at = timezone ('utc'::text, now());
