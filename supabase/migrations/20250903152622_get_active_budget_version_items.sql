set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_active_budget_version_items (p_project_id uuid) RETURNS TABLE (
	budget_line_item_id uuid,
	project_id uuid,
	wbs_library_item_id uuid,
	quantity numeric,
	unit text,
	material_rate numeric,
	labor_rate numeric,
	productivity_per_hour numeric,
	unit_rate_manual_override boolean,
	unit_rate numeric,
	factor numeric,
	remarks text,
	cost_certainty numeric,
	design_certainty numeric,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
    v_active_version_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Access check
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: no access to project %', p_project_id;
    END IF;

    -- Get the active budget version for this project
    SELECT p.active_budget_version_id INTO v_active_version_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    -- If no active version exists, return empty result
    IF v_active_version_id IS NULL THEN
        RETURN;
    END IF;

    -- Return budget version items in the same format as budget_line_item_current
    RETURN QUERY
    SELECT
        bvi.budget_version_item_id AS budget_line_item_id,
        p_project_id AS project_id,
        bvi.wbs_library_item_id,
        bvi.quantity,
        bvi.unit,
        bvi.material_rate,
        bvi.labor_rate,
        bvi.productivity_per_hour,
        bvi.unit_rate_manual_override,
        bvi.unit_rate,
        bvi.factor,
        bvi.remarks,
        bvi.cost_certainty,
        bvi.design_certainty,
        bvi.created_at,
        bvi.updated_at
    FROM public.budget_version_item bvi
    WHERE bvi.budget_version_id = v_active_version_id
    ORDER BY bvi.wbs_library_item_id;
END;
$function$;
