set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id uuid,
	p_freeze_reason text DEFAULT NULL::text,
	p_budget_version_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
	v_user_id UUID;
	v_budget_version_id UUID;
BEGIN
	-- Get the current user ID, fallback to a system user if not authenticated
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		-- Use a system user ID for operations not performed by authenticated users
		v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
	END IF;

	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	-- Use provided budget version ID or fall back to active version
	v_budget_version_id := p_budget_version_id;
	IF v_budget_version_id IS NULL THEN
		SELECT active_budget_version_id INTO v_budget_version_id
		FROM public.project
		WHERE project_id = v_project_id;
	END IF;

	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id,
		budget_version_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		v_user_id,
		v_budget_version_id
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;

	-- Copy budget items from the specified budget version (or active version if none specified)
	IF v_budget_version_id IS NOT NULL THEN
		-- Copy from budget version items
		FOR v_item IN (
			SELECT *
			FROM public.budget_version_item
			WHERE budget_version_id = v_budget_version_id
		) LOOP
			INSERT INTO public.budget_snapshot_line_item (
				budget_snapshot_id,
				wbs_library_item_id,
				quantity,
				unit,
				material_rate,
				labor_rate,
				productivity_per_hour,
				unit_rate_manual_override,
				unit_rate,
				factor,
				remarks,
				cost_certainty,
				design_certainty
			)
			VALUES (
				v_snapshot_id,
				v_item.wbs_library_item_id,
				v_item.quantity,
				v_item.unit,
				v_item.material_rate,
				v_item.labor_rate,
				v_item.productivity_per_hour,
				v_item.unit_rate_manual_override,
				v_item.unit_rate,
				v_item.factor,
				v_item.remarks,
				v_item.cost_certainty,
				v_item.design_certainty
			);
		END LOOP;
    END IF;
	
	RETURN v_snapshot_id;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_cost_detail_data (project_id_param uuid) RETURNS TABLE (
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	wbs_level integer,
	parent_item_id uuid,
	budget_amount numeric,
	quantity numeric,
	unit_rate numeric,
	factor numeric,
	work_packages jsonb,
	purchase_orders jsonb
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
	v_client_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Get the client_id for the project
	SELECT p.client_id INTO v_client_id
	FROM public.project p
	WHERE p.project_id = project_id_param;

	-- Return cost detail data with hierarchical WBS structure
	RETURN QUERY
	WITH wbs_budget AS (
		-- Get all WBS items for the project with their budget data
		SELECT
			wli.wbs_library_item_id,
			wli.code AS wbs_code,
			wli.description AS wbs_description,
			wli.level AS wbs_level,
			wli.parent_item_id,
			bli.quantity,
			bli.unit_rate,
			bli.factor,
			CASE
				WHEN bli.quantity IS NOT NULL AND bli.unit_rate IS NOT NULL THEN
					bli.quantity * bli.unit_rate * COALESCE(bli.factor, 1)
				ELSE 0
			END AS budget_amount
        FROM public.wbs_library_item wli
        LEFT JOIN public.budget_version_item bli
            ON bli.wbs_library_item_id = wli.wbs_library_item_id
           AND bli.budget_version_id = (
                SELECT p.active_budget_version_id
                FROM public.project p
                WHERE p.project_id = project_id_param
           )
		WHERE wli.wbs_library_id = (
			SELECT p.wbs_library_id
			FROM public.project p
			WHERE p.project_id = project_id_param
		)
		AND (
			wli.item_type = 'Standard'::public.wbs_item_type
			OR (wli.item_type = 'Custom'::public.wbs_item_type AND (
				wli.client_id = v_client_id OR wli.project_id = project_id_param
			))
		)
	),
	work_package_data AS (
		-- Get work packages grouped by WBS item
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				jsonb_build_object(
					'work_package_id', wp.work_package_id,
					'name', wp.name,
					'description', wp.description,
					'purchase_order_count', COALESCE(po_count.count, 0)
				)
			) AS work_packages
		FROM public.work_package wp
		LEFT JOIN (
			SELECT
				po.work_package_id,
				COUNT(po.purchase_order_id) AS count
			FROM public.purchase_order po
			WHERE po.project_id = project_id_param
			GROUP BY po.work_package_id
		) po_count ON wp.work_package_id = po_count.work_package_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	),
	purchase_order_data AS (
		-- Get purchase orders for each WBS item through work packages
		SELECT
			wp.wbs_library_item_id,
			jsonb_agg(
				DISTINCT jsonb_build_object(
					'purchase_order_id', po.purchase_order_id,
					'po_number', po.po_number,
					'description', po.description,
					'vendor_name', v.name,
					'original_amount', po.original_amount,
					'co_amount', po.co_amount,
					'work_package_id', po.work_package_id
				)
			) FILTER (WHERE po.purchase_order_id IS NOT NULL) AS purchase_orders
		FROM public.work_package wp
		LEFT JOIN public.purchase_order po ON wp.work_package_id = po.work_package_id
		LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
		WHERE wp.project_id = project_id_param
		GROUP BY wp.wbs_library_item_id
	)
	SELECT
		wb.wbs_library_item_id,
		wb.wbs_code,
		wb.wbs_description,
		wb.wbs_level,
		wb.parent_item_id,
		wb.budget_amount,
		wb.quantity,
		wb.unit_rate,
		wb.factor,
		COALESCE(wpd.work_packages, '[]'::jsonb) AS work_packages,
		COALESCE(pod.purchase_orders, '[]'::jsonb) AS purchase_orders
	FROM wbs_budget wb
	LEFT JOIN work_package_data wpd ON wb.wbs_library_item_id = wpd.wbs_library_item_id
	LEFT JOIN purchase_order_data pod ON wb.wbs_library_item_id = pod.wbs_library_item_id
	ORDER BY wb.wbs_code;
END;
$function$;

CREATE OR REPLACE FUNCTION public.revert_to_budget_snapshot (
	p_budget_snapshot_id uuid,
	p_revert_reason text DEFAULT 'Reverted to snapshot'::text
) RETURNS boolean LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_project_id UUID;
    v_active_version_id UUID;
    v_item RECORD;
BEGIN
	-- Get the project_id from the snapshot and stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
	WHERE bs.budget_snapshot_id = p_budget_snapshot_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
	END IF;

    -- Resolve active budget version for this project
    SELECT active_budget_version_id INTO v_active_version_id
    FROM public.project
    WHERE project_id = v_project_id;

    IF v_active_version_id IS NULL THEN
        RAISE EXCEPTION 'No active budget version for project %', v_project_id;
    END IF;

    FOR v_item IN (
        SELECT *
        FROM public.budget_snapshot_line_item
        WHERE budget_snapshot_id = p_budget_snapshot_id
    ) LOOP
        -- Upsert each snapshot item into the active budget version
        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        ) VALUES (
            v_active_version_id,
            v_item.wbs_library_item_id,
            v_item.quantity,
            v_item.unit,
            v_item.material_rate,
            v_item.labor_rate,
            v_item.productivity_per_hour,
            v_item.unit_rate_manual_override,
            v_item.unit_rate,
            v_item.factor,
            v_item.remarks,
            v_item.cost_certainty,
            v_item.design_certainty
        )
        ON CONFLICT (budget_version_id, wbs_library_item_id) DO UPDATE
        SET
            quantity = EXCLUDED.quantity,
            unit = EXCLUDED.unit,
            material_rate = EXCLUDED.material_rate,
            labor_rate = EXCLUDED.labor_rate,
            productivity_per_hour = EXCLUDED.productivity_per_hour,
            unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
            unit_rate = EXCLUDED.unit_rate,
            factor = EXCLUDED.factor,
            remarks = EXCLUDED.remarks,
            cost_certainty = EXCLUDED.cost_certainty,
            design_certainty = EXCLUDED.design_certainty;
    END LOOP;

	RETURN TRUE;
END;
$function$;

CREATE OR REPLACE FUNCTION public.undo_budget_import (
	p_budget_import_id uuid,
	p_reason text DEFAULT 'Import undone'::text
) RETURNS jsonb LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id uuid;
    v_project_id uuid;
    v_pre_version_id uuid;
    v_new_version_id uuid;
    v_import_record record;
    v_custom_wbs_items uuid[];
    v_wbs_item_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    v_user_id := auth.uid();

    -- Get import record and validate
    SELECT * INTO v_import_record
    FROM public.budget_import
    WHERE budget_import_id = p_budget_import_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Budget import not found: %', p_budget_import_id;
    END IF;

    IF v_import_record.is_undone THEN
        RAISE EXCEPTION 'Budget import already undone: %', p_budget_import_id;
    END IF;

    v_project_id := v_import_record.project_id;
    v_pre_version_id := v_import_record.pre_version_id;
    v_new_version_id := v_import_record.new_version_id;

    -- Permission check
    IF NOT public.can_modify_project(v_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', v_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('undo_budget_import'),
        hashtext(v_project_id::text)
    );

    -- Check if the import version is currently active
    IF EXISTS (
        SELECT 1 FROM public.project
        WHERE project_id = v_project_id
        AND active_budget_version_id = v_new_version_id
    ) THEN
        -- Revert to pre-import version
        UPDATE public.project
        SET active_budget_version_id = v_pre_version_id
        WHERE project_id = v_project_id;
    END IF;

    -- Find custom WBS items created by this import
    -- These are project-scoped WBS items that were created during the import
    SELECT ARRAY_AGG(DISTINCT wli.wbs_library_item_id) INTO v_custom_wbs_items
    FROM public.budget_version_item bvi
    JOIN public.wbs_library_item wli ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    WHERE bvi.budget_version_id = v_new_version_id
    AND wli.project_id = v_project_id
    AND wli.created_at >= v_import_record.created_at;

    -- Remove custom WBS items created by this import
    -- Only remove if they're not used in other versions or current budget
    IF v_custom_wbs_items IS NOT NULL THEN
        FOREACH v_wbs_item_id IN ARRAY v_custom_wbs_items
        LOOP
            -- Check if this WBS item is used elsewhere (in other versions)
            IF NOT EXISTS (
                SELECT 1 FROM public.budget_version_item bvi
                JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
                WHERE bvi.wbs_library_item_id = v_wbs_item_id
                AND bv.project_id = v_project_id
                AND bvi.budget_version_id != v_new_version_id
            ) THEN
                -- Safe to delete this custom WBS item
                DELETE FROM public.wbs_library_item
                WHERE wbs_library_item_id = v_wbs_item_id;
            END IF;
        END LOOP;
    END IF;

    -- Mark import as undone
    UPDATE public.budget_import
    SET is_undone = true,
        undone_at = NOW(),
        undone_by_user_id = v_user_id
    WHERE budget_import_id = p_budget_import_id;

    RETURN jsonb_build_object(
        'success', true,
        'budget_import_id', p_budget_import_id,
        'reverted_to_version_id', v_pre_version_id,
        'removed_wbs_items_count', COALESCE(array_length(v_custom_wbs_items, 1), 0),
        'reason', p_reason
    );
END;
$function$;

CREATE OR REPLACE FUNCTION public.upsert_budget_line_item (
	p_project_id uuid,
	p_wbs_library_item_id uuid,
	p_quantity numeric,
	p_unit text DEFAULT NULL::text,
	p_material_rate numeric DEFAULT 0,
	p_labor_rate numeric DEFAULT NULL::numeric,
	p_productivity_per_hour numeric DEFAULT NULL::numeric,
	p_unit_rate_manual_override boolean DEFAULT false,
	p_unit_rate numeric DEFAULT NULL::numeric,
	p_factor numeric DEFAULT NULL::numeric,
	p_remarks text DEFAULT NULL::text,
	p_cost_certainty numeric DEFAULT NULL::numeric,
	p_design_certainty numeric DEFAULT NULL::numeric,
	p_change_reason text DEFAULT NULL::text,
	p_budget_line_item_id uuid DEFAULT NULL::uuid
) RETURNS uuid LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_calculated_cost NUMERIC;
    v_cost_to_use     NUMERIC;
    v_budget_version_item_id_out UUID;
    v_active_version_id UUID;
BEGIN
    -- 1. calculate base cost via helper
    v_calculated_cost := public.calculate_unit_item_cost(
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour
    );

    -- 2. decide which cost to use
    IF COALESCE(p_unit_rate_manual_override, FALSE) THEN
        IF p_unit_rate IS NULL THEN
            v_cost_to_use := NULL;
        ELSE
            v_cost_to_use := p_unit_rate;
        END IF;
    ELSE
        v_cost_to_use := v_calculated_cost;
    END IF;

    -- 3. Upsert into active budget version items
    SELECT active_budget_version_id INTO v_active_version_id
    FROM public.project
    WHERE project_id = p_project_id;

    IF v_active_version_id IS NULL THEN
        RAISE EXCEPTION 'No active budget version for project %', p_project_id;
    END IF;

    INSERT INTO public.budget_version_item (
        budget_version_id,
        wbs_library_item_id,
        quantity,
        unit,
        material_rate,
        labor_rate,
        productivity_per_hour,
        unit_rate_manual_override,
        unit_rate,
        factor,
        remarks,
        cost_certainty,
        design_certainty
    ) VALUES (
        v_active_version_id,
        p_wbs_library_item_id,
        p_quantity,
        p_unit,
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour,
        p_unit_rate_manual_override,
        v_cost_to_use,
        p_factor,
        p_remarks,
        p_cost_certainty,
        p_design_certainty
    )
    ON CONFLICT (budget_version_id, wbs_library_item_id) DO UPDATE
    SET
        quantity                = EXCLUDED.quantity,
        unit                    = EXCLUDED.unit,
        material_rate           = EXCLUDED.material_rate,
        labor_rate              = EXCLUDED.labor_rate,
        productivity_per_hour   = EXCLUDED.productivity_per_hour,
        unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
        unit_rate               = EXCLUDED.unit_rate,
        factor                  = EXCLUDED.factor,
        remarks                 = EXCLUDED.remarks,
        cost_certainty          = EXCLUDED.cost_certainty,
        design_certainty        = EXCLUDED.design_certainty
    RETURNING budget_version_item_id
    INTO v_budget_version_item_id_out;

    RETURN v_budget_version_item_id_out;
END;
$function$;
