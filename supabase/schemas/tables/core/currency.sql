-- Currency Table Schema
-- Contains currency information for tender management
-- Primary key is currency_code (ISO 4217 standard)
CREATE TABLE IF NOT EXISTS "public"."currency" (
	"currency_code" "text" NOT NULL,
	"symbol" "text" NOT NULL,
	"symbol_position" "text" NOT NULL DEFAULT 'before',
	"description" "text" NOT NULL,
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."currency" OWNER TO "postgres";

COMMENT ON TABLE "public"."currency" IS 'Currency information for tender management with ISO 4217 currency codes';

COMMENT ON COLUMN "public"."currency"."currency_code" IS 'ISO 4217 currency code (e.g., USD, EUR, SEK)';

COMMENT ON COLUMN "public"."currency"."symbol" IS 'Currency symbol (e.g., $, €, kr)';

COMMENT ON COLUMN "public"."currency"."symbol_position" IS 'Position of currency symbol relative to amount (before/after)';

COMMENT ON COLUMN "public"."currency"."description" IS 'Human-readable currency description (e.g., US Dollar, Euro, Swedish Krona)';

-- Primary key constraint
ALTER TABLE ONLY "public"."currency"
ADD CONSTRAINT "currency_pkey" PRIMARY KEY ("currency_code");

-- Check constraint for symbol position
ALTER TABLE ONLY "public"."currency"
ADD CONSTRAINT "currency_symbol_position_check" CHECK ("symbol_position" IN ('before', 'after'));

-- Foreign key constraints
ALTER TABLE ONLY "public"."currency"
ADD CONSTRAINT "currency_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "currency_description_idx" ON "public"."currency" USING "btree" ("description");

CREATE INDEX "currency_created_by_user_id_idx" ON "public"."currency" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."currency" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."currency" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
-- SELECT policies - all authenticated users can view currencies
CREATE POLICY "All authenticated users can view currencies" ON "public"."currency" FOR
SELECT
	TO "authenticated" USING (true);

-- INSERT policies - only admin users can create currencies
CREATE POLICY "Only admin users can create currencies" ON "public"."currency" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."membership" m
			WHERE
				m."user_id" = "auth"."uid" ()
				AND m."role" >= 'admin'::"public"."membership_role"
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - only admin users can update currencies
CREATE POLICY "Only admin users can update currencies" ON "public"."currency"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."membership" m
			WHERE
				m."user_id" = "auth"."uid" ()
				AND m."role" >= 'admin'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."membership" m
			WHERE
				m."user_id" = "auth"."uid" ()
				AND m."role" >= 'admin'::"public"."membership_role"
		)
	);

-- DELETE policies - only admin users can delete currencies
CREATE POLICY "Only admin users can delete currencies" ON "public"."currency" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."membership" m
		WHERE
			m."user_id" = "auth"."uid" ()
			AND m."role" >= 'admin'::"public"."membership_role"
	)
);
