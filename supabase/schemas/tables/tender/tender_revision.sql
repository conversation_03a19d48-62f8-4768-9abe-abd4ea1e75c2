-- Tender Revision Table Schema
-- Contains revision history for tenders with version tracking
CREATE TABLE IF NOT EXISTS "public"."tender_revision" (
	"tender_revision_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"tender_id" "uuid" NOT NULL,
	"revision_number" integer NOT NULL,
	"is_current" boolean DEFAULT false NOT NULL,
	"revision_notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_revision" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_revision" IS 'Revision history for tenders with version tracking';

COMMENT ON COLUMN "public"."tender_revision"."tender_id" IS 'Tender this revision belongs to';

COMMENT ON COLUMN "public"."tender_revision"."revision_number" IS 'Sequential revision number starting from 1';

COMMENT ON COLUMN "public"."tender_revision"."is_current" IS 'Whether this is the current active revision';

COMMENT ON COLUMN "public"."tender_revision"."revision_notes" IS 'Notes about changes in this revision';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_revision"
ADD CONSTRAINT "tender_revision_pkey" PRIMARY KEY ("tender_revision_id");

-- Unique constraint on revision number within tender
ALTER TABLE ONLY "public"."tender_revision"
ADD CONSTRAINT "tender_revision_number_unique" UNIQUE ("tender_id", "revision_number");

-- Partial unique index to ensure only one current revision per tender
CREATE UNIQUE INDEX "tender_current_rev_uniq" ON "public"."tender_revision" ("tender_id")
WHERE
	"is_current" = true;

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_revision"
ADD CONSTRAINT "tender_revision_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."tender_revision"
ADD CONSTRAINT "tender_revision_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_revision_tender_id_idx" ON "public"."tender_revision" USING "btree" ("tender_id");

CREATE INDEX "tender_revision_is_current_idx" ON "public"."tender_revision" USING "btree" ("is_current");

CREATE INDEX "tender_revision_created_by_user_id_idx" ON "public"."tender_revision" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_revision" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_revision" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger to ensure single current revision
CREATE OR REPLACE FUNCTION "public"."ensure_single_current_revision" () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
    IF NEW.is_current = true THEN
        UPDATE public.tender_revision 
        SET is_current = false 
        WHERE tender_id = NEW.tender_id 
        AND tender_revision_id != NEW.tender_revision_id 
        AND is_current = true;
    END IF;
    RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."ensure_single_current_revision" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."ensure_single_current_revision" () IS 'Ensures only one revision per tender is marked as current';

CREATE OR REPLACE TRIGGER "ensure_single_current_revision_trigger" BEFORE INSERT
OR
UPDATE ON "public"."tender_revision" FOR EACH ROW
EXECUTE FUNCTION "public"."ensure_single_current_revision" ();

-- Audit trigger for tender_revision table
CREATE OR REPLACE TRIGGER "audit_tender_revision_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_revision" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_revision_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view revisions for tenders they have access to
CREATE POLICY "Users can view revisions for tenders they have access to" ON "public"."tender_revision" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_revision"."tender_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
	);

-- INSERT policies - users can create revisions for tenders they have editor access to
CREATE POLICY "Users can create revisions for tenders they have editor access to" ON "public"."tender_revision" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_revision"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update revisions for tenders they have editor access to
CREATE POLICY "Users can update revisions for tenders they have editor access to" ON "public"."tender_revision"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_revision"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_revision"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete revisions for tenders they have admin access to
CREATE POLICY "Users can delete revisions for tenders they have admin access to" ON "public"."tender_revision" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."tender" t
		WHERE
			t."tender_id" = "tender_revision"."tender_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				t."project_id",
				'admin'::"public"."membership_role"
			)
	)
);
