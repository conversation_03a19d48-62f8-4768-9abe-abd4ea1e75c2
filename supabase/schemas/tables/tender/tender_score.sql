-- Tender Score Table Schema
-- Stores individual scores for tenders against scoring criteria
CREATE TABLE IF NOT EXISTS "public"."tender_score" (
	"tender_score_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"tender_id" "uuid" NOT NULL,
	"tender_scoring_criteria_id" "uuid" NOT NULL,
	"score" numeric(5, 2) NOT NULL,
	"comments" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_score" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_score" IS 'Stores individual scores for tenders against scoring criteria';

COMMENT ON COLUMN "public"."tender_score"."tender_id" IS 'Tender being scored';

COMMENT ON COLUMN "public"."tender_score"."tender_scoring_criteria_id" IS 'Scoring criteria this score applies to';

COMMENT ON COLUMN "public"."tender_score"."score" IS 'Score given for this criteria (0-10 scale)';

COMMENT ON COLUMN "public"."tender_score"."comments" IS 'Comments about this score';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_pkey" PRIMARY KEY ("tender_score_id");

-- Unique constraint to prevent duplicate scores for same tender/criteria
ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_unique" UNIQUE ("tender_id", "tender_scoring_criteria_id");

-- Check constraint for score range
ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_range_check" CHECK (
	"score" >= 0
	AND "score" <= 10
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_tender_scoring_criteria_id_fkey" FOREIGN KEY ("tender_scoring_criteria_id") REFERENCES "public"."tender_scoring_criteria" ("tender_scoring_criteria_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."tender_score"
ADD CONSTRAINT "tender_score_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_score_tender_id_idx" ON "public"."tender_score" USING "btree" ("tender_id");

CREATE INDEX "tender_score_tender_scoring_criteria_id_idx" ON "public"."tender_score" USING "btree" ("tender_scoring_criteria_id");

CREATE INDEX "tender_score_score_idx" ON "public"."tender_score" USING "btree" ("score");

CREATE INDEX "tender_score_created_by_user_id_idx" ON "public"."tender_score" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_score" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_score" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender_score table
CREATE OR REPLACE TRIGGER "audit_tender_score_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_score" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_score_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view scores for tenders they have access to
CREATE POLICY "Users can view scores for tenders they have access to" ON "public"."tender_score" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_score"."tender_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
	);

-- INSERT policies - users can create scores for tenders they have editor access to
CREATE POLICY "Users can create scores for tenders they have editor access to" ON "public"."tender_score" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_score"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update scores for tenders they have editor access to
CREATE POLICY "Users can update scores for tenders they have editor access to" ON "public"."tender_score"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_score"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_score"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete scores for tenders they have editor access to
CREATE POLICY "Users can delete scores for tenders they have editor access to" ON "public"."tender_score" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."tender" t
		WHERE
			t."tender_id" = "tender_score"."tender_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				t."project_id",
				'editor'::"public"."membership_role"
			)
	)
);
