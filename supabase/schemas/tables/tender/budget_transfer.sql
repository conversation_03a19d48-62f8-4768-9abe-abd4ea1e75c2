-- Budget Transfer Table Schema
-- Tracks budget transfers between WBS items for tender analysis
CREATE TABLE IF NOT EXISTS "public"."budget_transfer" (
	"budget_transfer_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"tender_line_item_id" "uuid",
	"from_wbs_library_item_id" "uuid" NOT NULL,
	"to_wbs_library_item_id" "uuid" NOT NULL,
	"transfer_amount" numeric(20, 4) NOT NULL,
	"reason" "text" NOT NULL,
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_transfer" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_transfer" IS 'Tracks budget transfers between WBS items for tender analysis';

COMMENT ON COLUMN "public"."budget_transfer"."project_id" IS 'Project this budget transfer belongs to';

COMMENT ON COLUMN "public"."budget_transfer"."tender_line_item_id" IS 'Optional tender line item that triggered this transfer';

COMMENT ON COLUMN "public"."budget_transfer"."from_wbs_library_item_id" IS 'WBS item budget is being transferred from';

COMMENT ON COLUMN "public"."budget_transfer"."to_wbs_library_item_id" IS 'WBS item budget is being transferred to';

COMMENT ON COLUMN "public"."budget_transfer"."transfer_amount" IS 'Amount being transferred';

COMMENT ON COLUMN "public"."budget_transfer"."reason" IS 'Reason for the budget transfer';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_pkey" PRIMARY KEY ("budget_transfer_id");

-- Check constraint for positive transfer amount
ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_amount_positive_check" CHECK ("transfer_amount" > 0);

-- Check constraint to prevent self-transfer
ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_different_wbs_check" CHECK (
	"from_wbs_library_item_id" != "to_wbs_library_item_id"
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_tender_line_item_id_fkey" FOREIGN KEY ("tender_line_item_id") REFERENCES "public"."tender_line_item" ("tender_line_item_id") ON UPDATE RESTRICT ON DELETE SET NULL;

ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_from_wbs_library_item_id_fkey" FOREIGN KEY ("from_wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_to_wbs_library_item_id_fkey" FOREIGN KEY ("to_wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_transfer"
ADD CONSTRAINT "budget_transfer_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_transfer_project_id_idx" ON "public"."budget_transfer" USING "btree" ("project_id");

CREATE INDEX "budget_transfer_tender_line_item_id_idx" ON "public"."budget_transfer" USING "btree" ("tender_line_item_id");

CREATE INDEX "budget_transfer_from_wbs_library_item_id_idx" ON "public"."budget_transfer" USING "btree" ("from_wbs_library_item_id");

CREATE INDEX "budget_transfer_to_wbs_library_item_id_idx" ON "public"."budget_transfer" USING "btree" ("to_wbs_library_item_id");

CREATE INDEX "budget_transfer_created_at_idx" ON "public"."budget_transfer" USING "btree" ("created_at");

CREATE INDEX "budget_transfer_created_by_user_id_idx" ON "public"."budget_transfer" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_transfer" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_transfer" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for budget_transfer table
CREATE OR REPLACE TRIGGER "audit_budget_transfer_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."budget_transfer" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_transfer_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view budget transfers for projects they have access to
CREATE POLICY "Users can view budget transfers for projects they have access to" ON "public"."budget_transfer" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create budget transfers for projects they have editor access to
CREATE POLICY "Users can create budget transfers for projects they have editor access to" ON "public"."budget_transfer" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update budget transfers for projects they have editor access to
CREATE POLICY "Users can update budget transfers for projects they have editor access to" ON "public"."budget_transfer"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete budget transfers for projects they have admin access to
CREATE POLICY "Users can delete budget transfers for projects they have admin access to" ON "public"."budget_transfer" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);
