-- Tender WBS Mapping Table Schema
-- Maps tender line items to WBS library items with coverage tracking
CREATE TABLE IF NOT EXISTS "public"."tender_wbs_mapping" (
	"tender_wbs_mapping_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"tender_line_item_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"coverage_percentage" numeric(5, 2),
	"coverage_quantity" numeric(20, 4),
	"notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_wbs_mapping" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_wbs_mapping" IS 'Maps tender line items to WBS library items with coverage tracking';

COMMENT ON COLUMN "public"."tender_wbs_mapping"."tender_line_item_id" IS 'Tender line item being mapped';

COMMENT ON COLUMN "public"."tender_wbs_mapping"."wbs_library_item_id" IS 'WBS library item being mapped to';

COMMENT ON COLUMN "public"."tender_wbs_mapping"."coverage_percentage" IS 'Percentage of WBS item covered by this line item';

COMMENT ON COLUMN "public"."tender_wbs_mapping"."coverage_quantity" IS 'Quantity of WBS item covered by this line item';

COMMENT ON COLUMN "public"."tender_wbs_mapping"."notes" IS 'Additional notes about this mapping';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_pkey" PRIMARY KEY ("tender_wbs_mapping_id");

-- Unique constraint to prevent duplicate mappings
ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_unique" UNIQUE ("tender_line_item_id", "wbs_library_item_id");

-- Check constraint for coverage percentage
ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_coverage_percentage_check" CHECK (
	"coverage_percentage" >= 0
	AND "coverage_percentage" <= 100
);

-- Check constraint for coverage quantity
ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_coverage_quantity_check" CHECK ("coverage_quantity" >= 0);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_tender_line_item_id_fkey" FOREIGN KEY ("tender_line_item_id") REFERENCES "public"."tender_line_item" ("tender_line_item_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender_wbs_mapping"
ADD CONSTRAINT "tender_wbs_mapping_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_wbs_mapping_tender_line_item_id_idx" ON "public"."tender_wbs_mapping" USING "btree" ("tender_line_item_id");

CREATE INDEX "tender_wbs_mapping_wbs_library_item_id_idx" ON "public"."tender_wbs_mapping" USING "btree" ("wbs_library_item_id");

CREATE INDEX "tender_wbs_mapping_coverage_percentage_idx" ON "public"."tender_wbs_mapping" USING "btree" ("coverage_percentage");

CREATE INDEX "tender_wbs_mapping_created_by_user_id_idx" ON "public"."tender_wbs_mapping" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_wbs_mapping" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_wbs_mapping" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender_wbs_mapping table
CREATE OR REPLACE TRIGGER "audit_tender_wbs_mapping_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_wbs_mapping" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_wbs_mapping_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view mappings for tenders they have access to
CREATE POLICY "Users can view mappings for tenders they have access to" ON "public"."tender_wbs_mapping" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_line_item" tli
				JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tli."tender_line_item_id" = "tender_wbs_mapping"."tender_line_item_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
	);

-- INSERT policies - users can create mappings for tenders they have editor access to
CREATE POLICY "Users can create mappings for tenders they have editor access to" ON "public"."tender_wbs_mapping" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_line_item" tli
				JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tli."tender_line_item_id" = "tender_wbs_mapping"."tender_line_item_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update mappings for tenders they have editor access to
CREATE POLICY "Users can update mappings for tenders they have editor access to" ON "public"."tender_wbs_mapping"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_line_item" tli
				JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tli."tender_line_item_id" = "tender_wbs_mapping"."tender_line_item_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_line_item" tli
				JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tli."tender_line_item_id" = "tender_wbs_mapping"."tender_line_item_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete mappings for tenders they have editor access to
CREATE POLICY "Users can delete mappings for tenders they have editor access to" ON "public"."tender_wbs_mapping" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."tender_line_item" tli
			JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
			JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
		WHERE
			tli."tender_line_item_id" = "tender_wbs_mapping"."tender_line_item_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				t."project_id",
				'editor'::"public"."membership_role"
			)
	)
);
