-- Tender Work Package Table Schema
-- Tracks conversion of tenders to work packages (self-contained, not referencing legacy work_package table)
CREATE TABLE IF NOT EXISTS "public"."tender_work_package" (
	"tender_work_package_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"tender_id" "uuid" NOT NULL,
	"work_package_name" "text" NOT NULL,
	"work_package_description" "text",
	"conversion_date" "date" NOT NULL,
	"conversion_notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_work_package" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_work_package" IS 'Tracks conversion of tenders to work packages (self-contained)';

COMMENT ON COLUMN "public"."tender_work_package"."tender_id" IS 'Tender that was converted to work package';

COMMENT ON COLUMN "public"."tender_work_package"."work_package_name" IS 'Name of the created work package';

COMMENT ON COLUMN "public"."tender_work_package"."work_package_description" IS 'Description of the created work package';

COMMENT ON COLUMN "public"."tender_work_package"."conversion_date" IS 'Date when the tender was converted';

COMMENT ON COLUMN "public"."tender_work_package"."conversion_notes" IS 'Notes about the conversion process';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_work_package"
ADD CONSTRAINT "tender_work_package_pkey" PRIMARY KEY ("tender_work_package_id");

-- Unique constraint to prevent duplicate conversions
ALTER TABLE ONLY "public"."tender_work_package"
ADD CONSTRAINT "tender_work_package_tender_unique" UNIQUE ("tender_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_work_package"
ADD CONSTRAINT "tender_work_package_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender_work_package"
ADD CONSTRAINT "tender_work_package_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_work_package_tender_id_idx" ON "public"."tender_work_package" USING "btree" ("tender_id");

CREATE INDEX "tender_work_package_conversion_date_idx" ON "public"."tender_work_package" USING "btree" ("conversion_date");

CREATE INDEX "tender_work_package_created_by_user_id_idx" ON "public"."tender_work_package" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_work_package" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender_work_package table
CREATE OR REPLACE TRIGGER "audit_tender_work_package_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_work_package" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_work_package_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view work package conversions for tenders they have access to
CREATE POLICY "Users can view work package conversions for tenders they have access to" ON "public"."tender_work_package" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_work_package"."tender_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
	);

-- INSERT policies - users can create work package conversions for tenders they have editor access to
CREATE POLICY "Users can create work package conversions for tenders they have editor access to" ON "public"."tender_work_package" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_work_package"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update work package conversions for tenders they have editor access to
CREATE POLICY "Users can update work package conversions for tenders they have editor access to" ON "public"."tender_work_package"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_work_package"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender" t
			WHERE
				t."tender_id" = "tender_work_package"."tender_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete work package conversions for tenders they have admin access to
CREATE POLICY "Users can delete work package conversions for tenders they have admin access to" ON "public"."tender_work_package" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."tender" t
		WHERE
			t."tender_id" = "tender_work_package"."tender_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				t."project_id",
				'admin'::"public"."membership_role"
			)
	)
);
