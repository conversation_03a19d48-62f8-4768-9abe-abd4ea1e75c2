-- Tender Line Item Table Schema
-- Contains individual line items for tender revisions with normalization support
CREATE TYPE "public"."normalization_type" AS ENUM('amount', 'percentage');

ALTER TYPE "public"."normalization_type" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."tender_line_item" (
	"tender_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"tender_revision_id" "uuid" NOT NULL,
	"line_number" integer NOT NULL,
	"description" "text" NOT NULL,
	"quantity" numeric(20, 4),
	"unit" "text",
	"unit_rate" numeric(20, 4),
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_factor" numeric(10, 4),
	"subtotal" numeric(20, 4) NOT NULL,
	-- Normalization fields
	"normalization_type" "public"."normalization_type",
	"normalization_amount" numeric(20, 4),
	"normalization_percentage" numeric(5, 2),
	"notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_line_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_line_item" IS 'Individual line items for tender revisions with normalization support';

COMMENT ON COLUMN "public"."tender_line_item"."tender_revision_id" IS 'Tender revision this line item belongs to';

COMMENT ON COLUMN "public"."tender_line_item"."line_number" IS 'Sequential line number within the revision';

COMMENT ON COLUMN "public"."tender_line_item"."description" IS 'Description of the line item';

COMMENT ON COLUMN "public"."tender_line_item"."quantity" IS 'Quantity for this line item';

COMMENT ON COLUMN "public"."tender_line_item"."unit" IS 'Unit of measurement (e.g., m², kg, hours)';

COMMENT ON COLUMN "public"."tender_line_item"."unit_rate" IS 'Rate per unit';

COMMENT ON COLUMN "public"."tender_line_item"."material_rate" IS 'Material cost component';

COMMENT ON COLUMN "public"."tender_line_item"."labor_rate" IS 'Labor cost component';

COMMENT ON COLUMN "public"."tender_line_item"."productivity_factor" IS 'Productivity factor for labor calculations';

COMMENT ON COLUMN "public"."tender_line_item"."subtotal" IS 'Calculated subtotal for this line item';

COMMENT ON COLUMN "public"."tender_line_item"."normalization_type" IS 'Type of normalization input (amount or percentage)';

COMMENT ON COLUMN "public"."tender_line_item"."normalization_amount" IS 'Normalization amount (entered directly or calculated from percentage)';

COMMENT ON COLUMN "public"."tender_line_item"."normalization_percentage" IS 'Normalization percentage of mapped WBS budget amounts';

COMMENT ON COLUMN "public"."tender_line_item"."notes" IS 'Additional notes for this line item';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_line_item"
ADD CONSTRAINT "tender_line_item_pkey" PRIMARY KEY ("tender_line_item_id");

-- Unique constraint on line number within revision
ALTER TABLE ONLY "public"."tender_line_item"
ADD CONSTRAINT "tender_line_item_line_number_unique" UNIQUE ("tender_revision_id", "line_number");

-- Check constraint for normalization validation
ALTER TABLE ONLY "public"."tender_line_item"
ADD CONSTRAINT "tender_line_item_normalization_check" CHECK (
	(
		normalization_type = 'amount'
		AND normalization_amount IS NOT NULL
	)
	OR (
		normalization_type = 'percentage'
		AND normalization_percentage IS NOT NULL
	)
	OR (
		normalization_amount IS NULL
		AND normalization_percentage IS NULL
	)
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_line_item"
ADD CONSTRAINT "tender_line_item_tender_revision_id_fkey" FOREIGN KEY ("tender_revision_id") REFERENCES "public"."tender_revision" ("tender_revision_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."tender_line_item"
ADD CONSTRAINT "tender_line_item_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_line_item_tender_revision_id_idx" ON "public"."tender_line_item" USING "btree" ("tender_revision_id");

CREATE INDEX "tender_line_item_line_number_idx" ON "public"."tender_line_item" USING "btree" ("line_number");

CREATE INDEX "tender_line_item_subtotal_idx" ON "public"."tender_line_item" USING "btree" ("subtotal");

CREATE INDEX "tender_line_item_normalization_type_idx" ON "public"."tender_line_item" USING "btree" ("normalization_type");

CREATE INDEX "tender_line_item_created_by_user_id_idx" ON "public"."tender_line_item" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_line_item" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_line_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender_line_item table
CREATE OR REPLACE TRIGGER "audit_tender_line_item_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_line_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_line_item_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view line items for tenders they have access to
CREATE POLICY "Users can view line items for tenders they have access to" ON "public"."tender_line_item" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_revision" tr
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tr."tender_revision_id" = "tender_line_item"."tender_revision_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
	);

-- INSERT policies - users can create line items for tenders they have editor access to
CREATE POLICY "Users can create line items for tenders they have editor access to" ON "public"."tender_line_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_revision" tr
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tr."tender_revision_id" = "tender_line_item"."tender_revision_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update line items for tenders they have editor access to
CREATE POLICY "Users can update line items for tenders they have editor access to" ON "public"."tender_line_item"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_revision" tr
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tr."tender_revision_id" = "tender_line_item"."tender_revision_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_revision" tr
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tr."tender_revision_id" = "tender_line_item"."tender_revision_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					t."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete line items for tenders they have editor access to
CREATE POLICY "Users can delete line items for tenders they have editor access to" ON "public"."tender_line_item" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."tender_revision" tr
			JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
		WHERE
			tr."tender_revision_id" = "tender_line_item"."tender_revision_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				t."project_id",
				'editor'::"public"."membership_role"
			)
	)
);
