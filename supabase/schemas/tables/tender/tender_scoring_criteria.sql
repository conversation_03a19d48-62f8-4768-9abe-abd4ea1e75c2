-- Tender Scoring Criteria Table Schema
-- Defines scoring criteria for tender evaluation
CREATE TABLE IF NOT EXISTS "public"."tender_scoring_criteria" (
	"tender_scoring_criteria_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"criteria_name" "text" NOT NULL,
	"description" "text",
	"weight" numeric(5, 2) NOT NULL,
	"max_score" numeric(5, 2) DEFAULT 10 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender_scoring_criteria" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_scoring_criteria" IS 'Defines scoring criteria for tender evaluation';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."project_id" IS 'Project this scoring criteria belongs to';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."criteria_name" IS 'Name of the scoring criteria';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."description" IS 'Detailed description of the criteria';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."weight" IS 'Weight of this criteria in overall scoring (percentage)';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."max_score" IS 'Maximum score possible for this criteria';

COMMENT ON COLUMN "public"."tender_scoring_criteria"."is_active" IS 'Whether this criteria is currently active';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_pkey" PRIMARY KEY ("tender_scoring_criteria_id");

-- Unique constraint on criteria name within project
ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_name_project_unique" UNIQUE ("criteria_name", "project_id");

-- Check constraint for weight percentage
ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_weight_check" CHECK (
	"weight" >= 0
	AND "weight" <= 100
);

-- Check constraint for max score
ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_max_score_check" CHECK ("max_score" > 0);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender_scoring_criteria"
ADD CONSTRAINT "tender_scoring_criteria_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_scoring_criteria_project_id_idx" ON "public"."tender_scoring_criteria" USING "btree" ("project_id");

CREATE INDEX "tender_scoring_criteria_is_active_idx" ON "public"."tender_scoring_criteria" USING "btree" ("is_active");

CREATE INDEX "tender_scoring_criteria_weight_idx" ON "public"."tender_scoring_criteria" USING "btree" ("weight");

CREATE INDEX "tender_scoring_criteria_created_by_user_id_idx" ON "public"."tender_scoring_criteria" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender_scoring_criteria" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender_scoring_criteria" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender_scoring_criteria table
CREATE OR REPLACE TRIGGER "audit_tender_scoring_criteria_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender_scoring_criteria" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_scoring_criteria_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view scoring criteria for projects they have access to
CREATE POLICY "Users can view scoring criteria for projects they have access to" ON "public"."tender_scoring_criteria" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create scoring criteria for projects they have editor access to
CREATE POLICY "Users can create scoring criteria for projects they have editor access to" ON "public"."tender_scoring_criteria" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update scoring criteria for projects they have editor access to
CREATE POLICY "Users can update scoring criteria for projects they have editor access to" ON "public"."tender_scoring_criteria"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete scoring criteria for projects they have admin access to
CREATE POLICY "Users can delete scoring criteria for projects they have admin access to" ON "public"."tender_scoring_criteria" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);
