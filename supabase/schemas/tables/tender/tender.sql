-- Tender Table Schema
-- Contains tender information with project-level access control
CREATE TYPE "public"."tender_status" AS ENUM(
	'submitted',
	'under_review',
	'selected',
	'rejected'
);

ALTER TYPE "public"."tender_status" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."tender" (
	"tender_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"vendor_id" "uuid" NOT NULL,
	"tender_name" "text" NOT NULL,
	"description" "text",
	"submission_date" "date" NOT NULL,
	"currency_code" "text" NOT NULL,
	"status" "public"."tender_status" DEFAULT 'submitted' NOT NULL,
	"notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."tender" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender" IS 'Tender information with project-level access control';

COMMENT ON COLUMN "public"."tender"."project_id" IS 'Project this tender belongs to';

COMMENT ON COLUMN "public"."tender"."vendor_id" IS 'Vendor submitting this tender';

COMMENT ON COLUMN "public"."tender"."tender_name" IS 'Name/title of the tender';

COMMENT ON COLUMN "public"."tender"."description" IS 'Detailed description of the tender';

COMMENT ON COLUMN "public"."tender"."submission_date" IS 'Date when the tender was submitted';

COMMENT ON COLUMN "public"."tender"."currency_code" IS 'Currency used for this tender (references currency table)';

COMMENT ON COLUMN "public"."tender"."status" IS 'Current status of the tender';

COMMENT ON COLUMN "public"."tender"."notes" IS 'Additional notes about the tender';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_pkey" PRIMARY KEY ("tender_id");

-- Unique constraint on tender name within project
ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_name_project_unique" UNIQUE ("tender_name", "project_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_vendor_id_fkey" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_currency_code_fkey" FOREIGN KEY ("currency_code") REFERENCES "public"."currency" ("currency_code") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."tender"
ADD CONSTRAINT "tender_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_project_id_idx" ON "public"."tender" USING "btree" ("project_id");

CREATE INDEX "tender_vendor_id_idx" ON "public"."tender" USING "btree" ("vendor_id");

CREATE INDEX "tender_currency_code_idx" ON "public"."tender" USING "btree" ("currency_code");

CREATE INDEX "tender_status_idx" ON "public"."tender" USING "btree" ("status");

CREATE INDEX "tender_submission_date_idx" ON "public"."tender" USING "btree" ("submission_date");

CREATE INDEX "tender_created_by_user_id_idx" ON "public"."tender" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."tender" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."tender" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for tender table
CREATE OR REPLACE TRIGGER "audit_tender_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."tender" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_tender_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view tenders for projects they have access to
CREATE POLICY "Users can view tenders for projects they have access to" ON "public"."tender" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- INSERT policies - users can create tenders for projects they have editor access to
CREATE POLICY "Users can create tenders for projects they have editor access to" ON "public"."tender" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
		AND "created_by_user_id" = "auth"."uid" ()
	);

-- UPDATE policies - users can update tenders for projects they have editor access to
CREATE POLICY "Users can update tenders for projects they have editor access to" ON "public"."tender"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

-- DELETE policies - users can delete tenders for projects they have admin access to
CREATE POLICY "Users can delete tenders for projects they have admin access to" ON "public"."tender" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'admin'::"public"."membership_role"
	)
);
