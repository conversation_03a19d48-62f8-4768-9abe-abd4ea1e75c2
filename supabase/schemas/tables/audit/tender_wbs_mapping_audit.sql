-- Tender WBS Mapping Audit Table Schema
-- Tracks all changes to the tender_wbs_mapping table
CREATE TABLE IF NOT EXISTS "public"."tender_wbs_mapping_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Tender WBS mapping fields
	"tender_wbs_mapping_id" "uuid" NOT NULL,
	"tender_line_item_id" "uuid",
	"wbs_library_item_id" "uuid",
	"coverage_percentage" numeric(5, 2),
	"coverage_quantity" numeric(20, 4),
	"notes" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."tender_wbs_mapping_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_wbs_mapping_audit" IS 'Audit trail for tender_wbs_mapping table changes';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_wbs_mapping_audit"
ADD CONSTRAINT "tender_wbs_mapping_audit_pkey" PRIMARY KEY ("audit_id");

-- Check constraint for operation type
ALTER TABLE ONLY "public"."tender_wbs_mapping_audit"
ADD CONSTRAINT "tender_wbs_mapping_audit_operation_type_check" CHECK (
	"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_wbs_mapping_audit"
ADD CONSTRAINT "tender_wbs_mapping_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_wbs_mapping_audit_tender_wbs_mapping_id_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("tender_wbs_mapping_id");

CREATE INDEX "tender_wbs_mapping_audit_tender_line_item_id_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("tender_line_item_id");

CREATE INDEX "tender_wbs_mapping_audit_wbs_library_item_id_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("wbs_library_item_id");

CREATE INDEX "tender_wbs_mapping_audit_changed_by_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("changed_by");

CREATE INDEX "tender_wbs_mapping_audit_changed_at_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("changed_at");

CREATE INDEX "tender_wbs_mapping_audit_operation_type_idx" ON "public"."tender_wbs_mapping_audit" USING "btree" ("operation_type");

-- Enable Row Level Security
ALTER TABLE "public"."tender_wbs_mapping_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies
-- SELECT policies - users can view audit records for tender WBS mappings they have access to
CREATE POLICY "Users can view audit records for tender WBS mappings they have access to" ON "public"."tender_wbs_mapping_audit" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_wbs_mapping" twm
				JOIN "public"."tender_line_item" tli ON twm."tender_line_item_id" = tli."tender_line_item_id"
				JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				twm."tender_wbs_mapping_id" = "tender_wbs_mapping_audit"."tender_wbs_mapping_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
		OR
		-- For deleted records, check if user has access to the tender line item from audit data
		(
			"operation_type" = 'DELETE'
			AND "tender_line_item_id" IS NOT NULL
			AND EXISTS (
				SELECT
					1
				FROM
					"public"."tender_line_item" tli
					JOIN "public"."tender_revision" tr ON tli."tender_revision_id" = tr."tender_revision_id"
					JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
				WHERE
					tli."tender_line_item_id" = "tender_wbs_mapping_audit"."tender_line_item_id"
					AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
			)
		)
	);

-- No INSERT, UPDATE, or DELETE policies - audit records are only created by triggers
