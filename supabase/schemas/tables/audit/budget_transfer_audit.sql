-- Budget Transfer Audit Table Schema
-- Tracks all changes to the budget_transfer table
CREATE TABLE IF NOT EXISTS "public"."budget_transfer_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Budget transfer fields
	"budget_transfer_id" "uuid" NOT NULL,
	"project_id" "uuid",
	"tender_line_item_id" "uuid",
	"from_wbs_library_item_id" "uuid",
	"to_wbs_library_item_id" "uuid",
	"transfer_amount" numeric(20, 4),
	"reason" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."budget_transfer_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_transfer_audit" IS 'Audit trail for budget_transfer table changes';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_transfer_audit"
ADD CONSTRAINT "budget_transfer_audit_pkey" PRIMARY KEY ("audit_id");

-- Check constraint for operation type
ALTER TABLE ONLY "public"."budget_transfer_audit"
ADD CONSTRAINT "budget_transfer_audit_operation_type_check" CHECK (
	"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_transfer_audit"
ADD CONSTRAINT "budget_transfer_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_transfer_audit_budget_transfer_id_idx" ON "public"."budget_transfer_audit" USING "btree" ("budget_transfer_id");

CREATE INDEX "budget_transfer_audit_project_id_idx" ON "public"."budget_transfer_audit" USING "btree" ("project_id");

CREATE INDEX "budget_transfer_audit_tender_line_item_id_idx" ON "public"."budget_transfer_audit" USING "btree" ("tender_line_item_id");

CREATE INDEX "budget_transfer_audit_changed_by_idx" ON "public"."budget_transfer_audit" USING "btree" ("changed_by");

CREATE INDEX "budget_transfer_audit_changed_at_idx" ON "public"."budget_transfer_audit" USING "btree" ("changed_at");

CREATE INDEX "budget_transfer_audit_operation_type_idx" ON "public"."budget_transfer_audit" USING "btree" ("operation_type");

-- Enable Row Level Security
ALTER TABLE "public"."budget_transfer_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies
-- SELECT policies - users can view audit records for budget transfers they have access to
CREATE POLICY "Users can view audit records for budget transfers they have access to" ON "public"."budget_transfer_audit" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."budget_transfer" bt
			WHERE
				bt."budget_transfer_id" = "budget_transfer_audit"."budget_transfer_id"
				AND "public"."current_user_has_entity_access" (
					'project'::"public"."entity_type",
					bt."project_id"
				)
		)
		OR
		-- For deleted records, check if user has access to the project from audit data
		(
			"operation_type" = 'DELETE'
			AND "project_id" IS NOT NULL
			AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
		)
	);

-- No INSERT, UPDATE, or DELETE policies - audit records are only created by triggers
