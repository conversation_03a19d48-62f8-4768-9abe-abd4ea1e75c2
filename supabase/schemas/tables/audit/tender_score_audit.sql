-- Tender Score Audit Table Schema
-- Tracks all changes to the tender_score table
CREATE TABLE IF NOT EXISTS "public"."tender_score_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Tender score fields
	"tender_score_id" "uuid" NOT NULL,
	"tender_id" "uuid",
	"tender_scoring_criteria_id" "uuid",
	"score" numeric(5, 2),
	"comments" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."tender_score_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_score_audit" IS 'Audit trail for tender_score table changes';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_score_audit"
ADD CONSTRAINT "tender_score_audit_pkey" PRIMARY KEY ("audit_id");

-- Check constraint for operation type
ALTER TABLE ONLY "public"."tender_score_audit"
ADD CONSTRAINT "tender_score_audit_operation_type_check" CHECK (
	"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_score_audit"
ADD CONSTRAINT "tender_score_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_score_audit_tender_score_id_idx" ON "public"."tender_score_audit" USING "btree" ("tender_score_id");

CREATE INDEX "tender_score_audit_tender_id_idx" ON "public"."tender_score_audit" USING "btree" ("tender_id");

CREATE INDEX "tender_score_audit_tender_scoring_criteria_id_idx" ON "public"."tender_score_audit" USING "btree" ("tender_scoring_criteria_id");

CREATE INDEX "tender_score_audit_changed_by_idx" ON "public"."tender_score_audit" USING "btree" ("changed_by");

CREATE INDEX "tender_score_audit_changed_at_idx" ON "public"."tender_score_audit" USING "btree" ("changed_at");

CREATE INDEX "tender_score_audit_operation_type_idx" ON "public"."tender_score_audit" USING "btree" ("operation_type");

-- Enable Row Level Security
ALTER TABLE "public"."tender_score_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies
-- SELECT policies - users can view audit records for tender scores they have access to
CREATE POLICY "Users can view audit records for tender scores they have access to" ON "public"."tender_score_audit" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_score" ts
				JOIN "public"."tender" t ON ts."tender_id" = t."tender_id"
			WHERE
				ts."tender_score_id" = "tender_score_audit"."tender_score_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
		OR
		-- For deleted records, check if user has access to the tender from audit data
		(
			"operation_type" = 'DELETE'
			AND "tender_id" IS NOT NULL
			AND EXISTS (
				SELECT
					1
				FROM
					"public"."tender" t
				WHERE
					t."tender_id" = "tender_score_audit"."tender_id"
					AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
			)
		)
	);

-- No INSERT, UPDATE, or DELETE policies - audit records are only created by triggers
