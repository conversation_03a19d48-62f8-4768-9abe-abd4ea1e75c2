-- Tender Revision Audit Table Schema
-- Tracks all changes to the tender_revision table
CREATE TABLE IF NOT EXISTS "public"."tender_revision_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Tender revision fields
	"tender_revision_id" "uuid" NOT NULL,
	"tender_id" "uuid",
	"revision_number" integer,
	"is_current" boolean,
	"revision_notes" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."tender_revision_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_revision_audit" IS 'Audit trail for tender_revision table changes';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_revision_audit"
ADD CONSTRAINT "tender_revision_audit_pkey" PRIMARY KEY ("audit_id");

-- Check constraint for operation type
ALTER TABLE ONLY "public"."tender_revision_audit"
ADD CONSTRAINT "tender_revision_audit_operation_type_check" CHECK (
	"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_revision_audit"
ADD CONSTRAINT "tender_revision_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_revision_audit_tender_revision_id_idx" ON "public"."tender_revision_audit" USING "btree" ("tender_revision_id");

CREATE INDEX "tender_revision_audit_tender_id_idx" ON "public"."tender_revision_audit" USING "btree" ("tender_id");

CREATE INDEX "tender_revision_audit_changed_by_idx" ON "public"."tender_revision_audit" USING "btree" ("changed_by");

CREATE INDEX "tender_revision_audit_changed_at_idx" ON "public"."tender_revision_audit" USING "btree" ("changed_at");

CREATE INDEX "tender_revision_audit_operation_type_idx" ON "public"."tender_revision_audit" USING "btree" ("operation_type");

-- Enable Row Level Security
ALTER TABLE "public"."tender_revision_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies
-- SELECT policies - users can view audit records for tender revisions they have access to
CREATE POLICY "Users can view audit records for tender revisions they have access to" ON "public"."tender_revision_audit" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_revision" tr
				JOIN "public"."tender" t ON tr."tender_id" = t."tender_id"
			WHERE
				tr."tender_revision_id" = "tender_revision_audit"."tender_revision_id"
				AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
		)
		OR
		-- For deleted records, check if user has access to the tender from audit data
		(
			"operation_type" = 'DELETE'
			AND "tender_id" IS NOT NULL
			AND EXISTS (
				SELECT
					1
				FROM
					"public"."tender" t
				WHERE
					t."tender_id" = "tender_revision_audit"."tender_id"
					AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", t."project_id")
			)
		)
	);

-- No INSERT, UPDATE, or DELETE policies - audit records are only created by triggers
