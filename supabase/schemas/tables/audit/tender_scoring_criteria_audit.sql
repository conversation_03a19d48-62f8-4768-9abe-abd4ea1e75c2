-- Tender Scoring Criteria Audit Table Schema
-- Tracks all changes to the tender_scoring_criteria table
CREATE TABLE IF NOT EXISTS "public"."tender_scoring_criteria_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	-- Tender scoring criteria fields
	"tender_scoring_criteria_id" "uuid" NOT NULL,
	"project_id" "uuid",
	"criteria_name" "text",
	"description" "text",
	"weight" numeric(5, 2),
	"max_score" numeric(5, 2),
	"is_active" boolean,
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);

ALTER TABLE "public"."tender_scoring_criteria_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."tender_scoring_criteria_audit" IS 'Audit trail for tender_scoring_criteria table changes';

-- Primary key constraint
ALTER TABLE ONLY "public"."tender_scoring_criteria_audit"
ADD CONSTRAINT "tender_scoring_criteria_audit_pkey" PRIMARY KEY ("audit_id");

-- Check constraint for operation type
ALTER TABLE ONLY "public"."tender_scoring_criteria_audit"
ADD CONSTRAINT "tender_scoring_criteria_audit_operation_type_check" CHECK (
	"operation_type" IN ('INSERT', 'UPDATE', 'DELETE')
);

-- Foreign key constraints
ALTER TABLE ONLY "public"."tender_scoring_criteria_audit"
ADD CONSTRAINT "tender_scoring_criteria_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "tender_scoring_criteria_audit_tender_scoring_criteria_id_idx" ON "public"."tender_scoring_criteria_audit" USING "btree" ("tender_scoring_criteria_id");

CREATE INDEX "tender_scoring_criteria_audit_project_id_idx" ON "public"."tender_scoring_criteria_audit" USING "btree" ("project_id");

CREATE INDEX "tender_scoring_criteria_audit_changed_by_idx" ON "public"."tender_scoring_criteria_audit" USING "btree" ("changed_by");

CREATE INDEX "tender_scoring_criteria_audit_changed_at_idx" ON "public"."tender_scoring_criteria_audit" USING "btree" ("changed_at");

CREATE INDEX "tender_scoring_criteria_audit_operation_type_idx" ON "public"."tender_scoring_criteria_audit" USING "btree" ("operation_type");

-- Enable Row Level Security
ALTER TABLE "public"."tender_scoring_criteria_audit" ENABLE ROW LEVEL SECURITY;

-- Row Level Security Policies
-- SELECT policies - users can view audit records for tender scoring criteria they have access to
CREATE POLICY "Users can view audit records for tender scoring criteria they have access to" ON "public"."tender_scoring_criteria_audit" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."tender_scoring_criteria" tsc
			WHERE
				tsc."tender_scoring_criteria_id" = "tender_scoring_criteria_audit"."tender_scoring_criteria_id"
				AND "public"."current_user_has_entity_access" (
					'project'::"public"."entity_type",
					tsc."project_id"
				)
		)
		OR
		-- For deleted records, check if user has access to the project from audit data
		(
			"operation_type" = 'DELETE'
			AND "project_id" IS NOT NULL
			AND "public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
		)
	);

-- No INSERT, UPDATE, or DELETE policies - audit records are only created by triggers
