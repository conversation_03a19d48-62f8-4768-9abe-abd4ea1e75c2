-- Budget Versioning RPC Functions (Phase 2)
-- Implements core version management, import, listing, activation, and diff
-- SECURITY NOTE
-- All functions use SECURITY INVOKER and rely on RLS policies for access control.
-- RLS policies allow operations on stage/import/manual versions for users with modify permissions.
-- All functions validate auth.uid() and enforce can_modify_project/can_access_project.
-- ==========================================
-- create_budget_version
-- ==========================================
CREATE OR REPLACE FUNCTION public.create_budget_version (
	p_project_id uuid,
	p_label text DEFAULT NULL,
	p_kind public.budget_version_kind DEFAULT 'manual',
	p_stage_id uuid DEFAULT NULL,
	p_prev_version_id uuid DEFAULT NULL
) RETURNS uuid LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_user_id uuid;
    v_prev_version_id uuid;
    v_new_version_id uuid;
    v_active_version_id uuid;
BEGIN
    -- Ensure authenticated user
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permissions: must be able to modify the project
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    -- Resolve previous version: default to current active version if not provided
    SELECT active_budget_version_id INTO v_active_version_id
    FROM public.project WHERE project_id = p_project_id;

    v_prev_version_id := COALESCE(p_prev_version_id, v_active_version_id);

    -- Create the version row
    INSERT INTO public.budget_version (
        project_id,
        label,
        kind,
        stage_id,
        prev_version_id,
        created_by_user_id
    ) VALUES (
        p_project_id,
        p_label,
        p_kind,
        CASE WHEN p_kind = 'stage'::public.budget_version_kind THEN p_stage_id ELSE NULL END,
        v_prev_version_id,
        v_user_id
    ) RETURNING budget_version_id INTO v_new_version_id;

    -- Clone items from previous version by default for non-import kinds
    IF v_prev_version_id IS NOT NULL AND p_kind <> 'import'::public.budget_version_kind THEN
        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        )
        SELECT
            v_new_version_id,
            i.wbs_library_item_id,
            i.quantity,
            i.unit,
            i.material_rate,
            i.labor_rate,
            i.productivity_per_hour,
            i.unit_rate_manual_override,
            i.unit_rate,
            i.factor,
            i.remarks,
            i.cost_certainty,
            i.design_certainty
        FROM public.budget_version_item i
        WHERE i.budget_version_id = v_prev_version_id;
    END IF;

    RETURN v_new_version_id;
END;
$$;

ALTER FUNCTION public.create_budget_version (
	p_project_id uuid,
	p_label text,
	p_kind public.budget_version_kind,
	p_stage_id uuid,
	p_prev_version_id uuid
) OWNER TO "postgres";

COMMENT ON FUNCTION public.create_budget_version (
	p_project_id uuid,
	p_label text,
	p_kind public.budget_version_kind,
	p_stage_id uuid,
	p_prev_version_id uuid
) IS 'Creates a new budget version. Clones from active/prev by default; empty when kind="import".';

-- ==========================================
-- activate_budget_version
-- ==========================================
CREATE OR REPLACE FUNCTION public.activate_budget_version (p_version_id uuid, p_reason text DEFAULT NULL) RETURNS boolean LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_project_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Resolve project of the version
    SELECT project_id INTO v_project_id
    FROM public.budget_version
    WHERE budget_version_id = p_version_id;

    IF v_project_id IS NULL THEN
        RAISE EXCEPTION 'Version not found: %', p_version_id;
    END IF;

    -- Permission check
    IF NOT public.can_modify_project(v_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', v_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('activate_budget_version'),
        hashtext(v_project_id::text)
    );

    -- Activate
    UPDATE public.project
    SET active_budget_version_id = p_version_id
    WHERE project_id = v_project_id;

    -- Optionally record audit in the future (p_reason)
    RETURN TRUE;
END;
$$;

COMMENT ON FUNCTION public.activate_budget_version (p_version_id uuid, p_reason text) IS 'Activates a budget version by updating project.active_budget_version_id with concurrency lock.';

ALTER FUNCTION public.activate_budget_version (p_version_id uuid, p_reason text) OWNER TO "postgres";

-- ==========================================
-- create_and_activate_working_version
-- Creates a manual version (cloned from current active) and activates it
-- If the active version is not 'system', returns the current active id (no-op)
-- ==========================================
CREATE OR REPLACE FUNCTION public.create_and_activate_working_version (
	p_project_id uuid,
	p_label text DEFAULT 'Initial Working Budget'
) RETURNS uuid LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_active_version_id uuid;
    v_active_kind public.budget_version_kind;
    v_new_version_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permission check
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('create_and_activate_working_version'),
        hashtext(p_project_id::text)
    );

    -- Resolve active id with row lock on project
    SELECT p.active_budget_version_id
      INTO v_active_version_id
    FROM public.project p
    WHERE p.project_id = p_project_id
    FOR UPDATE;

    -- Resolve active kind separately if present
    IF v_active_version_id IS NOT NULL THEN
        SELECT kind INTO v_active_kind
        FROM public.budget_version
        WHERE budget_version_id = v_active_version_id;
    ELSE
        v_active_kind := NULL;
    END IF;

    -- If not a bootstrap 'system' version, nothing to do
    IF v_active_version_id IS NOT NULL AND v_active_kind IS DISTINCT FROM 'system'::public.budget_version_kind THEN
        RETURN v_active_version_id;
    END IF;

    -- Create a manual version cloned from the current active (if present)
    SELECT public.create_budget_version(
        p_project_id := p_project_id,
        p_label      := p_label,
        p_kind       := 'manual'::public.budget_version_kind,
        p_stage_id   := NULL,
        p_prev_version_id := v_active_version_id
    ) INTO v_new_version_id;

    -- Activate it
    UPDATE public.project
       SET active_budget_version_id = v_new_version_id
     WHERE project_id = p_project_id;

    RETURN v_new_version_id;
END;
$$;

COMMENT ON FUNCTION public.create_and_activate_working_version (p_project_id uuid, p_label text) IS 'Creates a manual working version cloned from the current active (if any), activates it, and returns the new version id. No-op if active is not system.';

ALTER FUNCTION public.create_and_activate_working_version (p_project_id uuid, p_label text) OWNER TO "postgres";

-- ==========================================
-- list_budget_versions
-- ==========================================
CREATE OR REPLACE FUNCTION public.list_budget_versions (
	p_project_id uuid,
	p_limit integer DEFAULT 50,
	p_cursor timestamptz DEFAULT NULL
) RETURNS TABLE (
	budget_version_id uuid,
	label text,
	kind public.budget_version_kind,
	is_active boolean,
	item_count bigint,
	total_cost numeric,
	created_at timestamptz,
	effective_at timestamptz
) LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Access check
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: no access to project %', p_project_id;
    END IF;

    RETURN QUERY
    WITH v AS (
        SELECT v.budget_version_id,
               v.label,
               v.kind,
               v.created_at,
               (v.budget_version_id = p.active_budget_version_id) AS is_active
        FROM public.budget_version v
        JOIN public.project p ON p.project_id = v.project_id
        WHERE v.project_id = list_budget_versions.p_project_id
          AND (p_cursor IS NULL OR v.created_at < p_cursor)
        ORDER BY v.created_at DESC
        LIMIT COALESCE(p_limit, 50)
    ), agg AS (
        SELECT i.budget_version_id,
               COUNT(*)::bigint AS item_count,
               COALESCE(SUM(i.quantity * i.unit_rate * COALESCE(i.factor, 1)), 0)::numeric AS total_cost,
               MAX(i.updated_at) AS max_updated_at
        FROM public.budget_version_item i
        WHERE i.budget_version_id IN (SELECT v.budget_version_id FROM v)
        GROUP BY i.budget_version_id
    )
    SELECT v.budget_version_id,
           v.label,
           v.kind,
           v.is_active,
           COALESCE(agg.item_count, 0) AS item_count,
           COALESCE(agg.total_cost, 0) AS total_cost,
           v.created_at,
           COALESCE(agg.max_updated_at, v.created_at) AS effective_at
    FROM v
    LEFT JOIN agg ON v.budget_version_id = agg.budget_version_id
    ORDER BY COALESCE(agg.max_updated_at, v.created_at) DESC;
END;
$$;

COMMENT ON FUNCTION public.list_budget_versions (
	p_project_id uuid,
	p_limit integer,
	p_cursor timestamptz
) IS 'Lists versions for a project with aggregates and active marker. Ordered by effective time (item updates or creation), newest first.';

ALTER FUNCTION public.list_budget_versions (
	p_project_id uuid,
	p_limit integer,
	p_cursor timestamptz
) OWNER TO "postgres";

-- ==========================================
-- diff_budget_versions
-- ==========================================
CREATE OR REPLACE FUNCTION public.diff_budget_versions (p_version_a uuid, p_version_b uuid) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_proj_a uuid;
    v_proj_b uuid;
    result jsonb := '{}'::jsonb;
    total_a numeric := 0;
    total_b numeric := 0;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Verify versions and same project
    SELECT project_id INTO v_proj_a FROM public.budget_version WHERE budget_version_id = p_version_a;
    SELECT project_id INTO v_proj_b FROM public.budget_version WHERE budget_version_id = p_version_b;

    IF v_proj_a IS NULL OR v_proj_b IS NULL THEN
        RAISE EXCEPTION 'One or both versions not found';
    END IF;
    IF v_proj_a <> v_proj_b THEN
        RAISE EXCEPTION 'Versions belong to different projects';
    END IF;

    -- Access check
    IF NOT public.can_access_project(v_proj_a) THEN
        RAISE EXCEPTION 'Forbidden: no access to project %', v_proj_a;
    END IF;

    -- Totals
    SELECT COALESCE(SUM(quantity * unit_rate * COALESCE(factor, 1)), 0) INTO total_a
    FROM public.budget_version_item WHERE budget_version_id = p_version_a;
    SELECT COALESCE(SUM(quantity * unit_rate * COALESCE(factor, 1)), 0) INTO total_b
    FROM public.budget_version_item WHERE budget_version_id = p_version_b;

    -- Added: in B not in A
    result := jsonb_set(result, '{added}', (
        SELECT COALESCE(jsonb_agg(row_to_json(x)), '[]'::jsonb)
        FROM (
            SELECT b.wbs_library_item_id,
                   b.quantity,
                   b.unit,
                   b.material_rate,
                   b.labor_rate,
                   b.productivity_per_hour,
                   b.unit_rate_manual_override,
                   b.unit_rate,
                   b.factor,
                   b.remarks,
                   b.cost_certainty,
                   b.design_certainty
            FROM public.budget_version_item b
            LEFT JOIN public.budget_version_item a
              ON a.budget_version_id = p_version_a
             AND a.wbs_library_item_id = b.wbs_library_item_id
            WHERE b.budget_version_id = p_version_b
              AND a.budget_version_item_id IS NULL
        ) x
    ));

    -- Removed: in A not in B
    result := jsonb_set(result, '{removed}', (
        SELECT COALESCE(jsonb_agg(row_to_json(x)), '[]'::jsonb)
        FROM (
            SELECT a.wbs_library_item_id,
                   a.quantity,
                   a.unit,
                   a.material_rate,
                   a.labor_rate,
                   a.productivity_per_hour,
                   a.unit_rate_manual_override,
                   a.unit_rate,
                   a.factor,
                   a.remarks,
                   a.cost_certainty,
                   a.design_certainty
            FROM public.budget_version_item a
            LEFT JOIN public.budget_version_item b
              ON b.budget_version_id = p_version_b
             AND b.wbs_library_item_id = a.wbs_library_item_id
            WHERE a.budget_version_id = p_version_a
              AND b.budget_version_item_id IS NULL
        ) x
    ));

    -- Changed: in both but with differing fields
    result := jsonb_set(result, '{changed}', (
        SELECT COALESCE(jsonb_agg(row_to_json(x)), '[]'::jsonb)
        FROM (
            SELECT a.wbs_library_item_id,
                   jsonb_build_object(
                       'from', jsonb_build_object(
                           'quantity', a.quantity,
                           'unit', a.unit,
                           'material_rate', a.material_rate,
                           'labor_rate', a.labor_rate,
                           'productivity_per_hour', a.productivity_per_hour,
                           'unit_rate_manual_override', a.unit_rate_manual_override,
                           'unit_rate', a.unit_rate,
                           'factor', a.factor,
                           'remarks', a.remarks,
                           'cost_certainty', a.cost_certainty,
                           'design_certainty', a.design_certainty
                       ),
                       'to', jsonb_build_object(
                           'quantity', b.quantity,
                           'unit', b.unit,
                           'material_rate', b.material_rate,
                           'labor_rate', b.labor_rate,
                           'productivity_per_hour', b.productivity_per_hour,
                           'unit_rate_manual_override', b.unit_rate_manual_override,
                           'unit_rate', b.unit_rate,
                           'factor', b.factor,
                           'remarks', b.remarks,
                           'cost_certainty', b.cost_certainty,
                           'design_certainty', b.design_certainty
                       )
                   ) AS diff
            FROM public.budget_version_item a
            JOIN public.budget_version_item b
              ON b.budget_version_id = p_version_b
             AND b.wbs_library_item_id = a.wbs_library_item_id
            WHERE a.budget_version_id = p_version_a
              AND (
                    a.quantity IS DISTINCT FROM b.quantity OR
                    a.unit IS DISTINCT FROM b.unit OR
                    a.material_rate IS DISTINCT FROM b.material_rate OR
                    a.labor_rate IS DISTINCT FROM b.labor_rate OR
                    a.productivity_per_hour IS DISTINCT FROM b.productivity_per_hour OR
                    a.unit_rate_manual_override IS DISTINCT FROM b.unit_rate_manual_override OR
                    a.unit_rate IS DISTINCT FROM b.unit_rate OR
                    a.factor IS DISTINCT FROM b.factor OR
                    a.remarks IS DISTINCT FROM b.remarks OR
                    a.cost_certainty IS DISTINCT FROM b.cost_certainty OR
                    a.design_certainty IS DISTINCT FROM b.design_certainty
                  )
        ) x
    ));

    -- Summary
    result := jsonb_set(result, '{summary}', jsonb_build_object(
        'version_a', p_version_a,
        'version_b', p_version_b,
        'total_cost_a', total_a,
        'total_cost_b', total_b,
        'total_cost_delta', (total_b - total_a)
    ));

    RETURN result;
END;
$$;

COMMENT ON FUNCTION public.diff_budget_versions (p_version_a uuid, p_version_b uuid) IS 'Returns JSONB with arrays of added/removed/changed items and summary totals.';

ALTER FUNCTION public.diff_budget_versions (p_version_a uuid, p_version_b uuid) OWNER TO "postgres";

-- ==========================================
-- apply_budget_import
-- ==========================================
CREATE OR REPLACE FUNCTION public.apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_items jsonb,
	p_source_hash text DEFAULT NULL,
	p_notes text DEFAULT NULL
) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_user_id uuid;
    v_wbs_library_id uuid;
    v_client_id uuid;
    v_active_version_id uuid;
    v_new_version_id uuid;
    v_import_id uuid;
    v_item jsonb;
    v_wbs_code text;
    v_description text;
    v_quantity numeric;
    v_unit text;
    v_material_rate numeric;
    v_factor numeric;
    v_labor_rate numeric;
    v_productivity_per_hour numeric;
    v_unit_rate numeric;
    v_unit_rate_manual_override boolean;
    v_remarks text;
    v_wbs_item_id uuid;
    v_parent_code text;
    v_parent_item_id uuid;
    v_level integer;
    v_in_level_code text;
    v_inserted_count integer := 0;
    v_wbs_created_count integer := 0;
    v_start timestamptz;
    v_duration_ms numeric;
    v_map jsonb := '{}'::jsonb;
    v_existing_version_id uuid;
BEGIN
    -- Ensure authenticated user
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Permission check
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', p_project_id;
    END IF;

    v_start := clock_timestamp();

    -- Project context
    SELECT wbs_library_id, client_id, active_budget_version_id
      INTO v_wbs_library_id, v_client_id, v_active_version_id
    FROM public.project
    WHERE project_id = p_project_id;

    IF v_wbs_library_id IS NULL THEN
        RAISE EXCEPTION 'Project % not found or missing wbs_library_id', p_project_id;
    END IF;

    -- Safety check: if no active version exists, create an initial one
    -- This should not happen with the new trigger, but provides a fallback
    IF v_active_version_id IS NULL THEN
        INSERT INTO public.budget_version (
            project_id, label, kind, stage_id, prev_version_id, created_by_user_id
        ) VALUES (
            p_project_id,
            'Initial Budget (Auto-created)',
            'system'::public.budget_version_kind,
            NULL,
            NULL,
            v_user_id
        ) RETURNING budget_version_id INTO v_active_version_id;

        -- Set this as the active budget version
        UPDATE public.project
        SET active_budget_version_id = v_active_version_id
        WHERE project_id = p_project_id;
    END IF;

    -- Idempotency: if source_hash is provided and exists, reuse that version
    IF p_source_hash IS NOT NULL THEN
        SELECT new_version_id INTO v_existing_version_id
        FROM public.budget_import
        WHERE project_id = p_project_id
          AND source_hash = p_source_hash
        LIMIT 1;

        IF v_existing_version_id IS NOT NULL THEN
            RETURN jsonb_build_object(
                'reused', true,
                'new_version_id', v_existing_version_id,
                'inserted_count', NULL,
                'wbs_created_count', NULL,
                'duration_ms', NULL
            );
        END IF;
    END IF;

    -- Create empty version for import
    INSERT INTO public.budget_version (
        project_id, label, kind, stage_id, prev_version_id, created_by_user_id
    ) VALUES (
        p_project_id,
        COALESCE(p_source_filename, 'Import') || ' ' || to_char(timezone('utc', now()), 'YYYY-MM-DD HH24:MI:SS'),
        'import'::public.budget_version_kind,
        NULL,
        v_active_version_id,
        v_user_id
    ) RETURNING budget_version_id INTO v_new_version_id;

    -- Walk items and ensure WBS exists, then insert into version items
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        v_wbs_code := v_item->>'code';
        v_description := v_item->>'description';
        v_quantity := (v_item->>'quantity')::numeric;
        v_unit := v_item->>'unit';
        v_material_rate := (v_item->>'material_rate')::numeric;
        v_factor := CASE WHEN v_item->>'factor' IS NOT NULL THEN (v_item->>'factor')::numeric ELSE NULL END;
        v_labor_rate := CASE WHEN v_item->>'labor_rate' IS NOT NULL THEN (v_item->>'labor_rate')::numeric ELSE NULL END;
        v_productivity_per_hour := CASE WHEN v_item->>'productivity_per_hour' IS NOT NULL THEN (v_item->>'productivity_per_hour')::numeric ELSE NULL END;
        v_unit_rate := CASE WHEN v_item->>'unit_rate' IS NOT NULL THEN (v_item->>'unit_rate')::numeric ELSE NULL END;
        v_unit_rate_manual_override := COALESCE((v_item->>'unit_rate_manual_override')::boolean, false);
        v_remarks := v_item->>'remarks';

        IF v_wbs_code IS NULL OR v_wbs_code = '' THEN
            RAISE EXCEPTION 'WBS code is required for all items';
        END IF;
        IF v_description IS NULL OR v_description = '' THEN
            RAISE EXCEPTION 'Description is required for all items';
        END IF;

        -- Lookup or create WBS item
        v_wbs_item_id := NULL;
        IF v_map ? v_wbs_code THEN
            v_wbs_item_id := (v_map->>v_wbs_code)::uuid;
        ELSE
            SELECT wli.wbs_library_item_id INTO v_wbs_item_id
            FROM public.wbs_library_item wli
            WHERE wli.code = v_wbs_code
              AND wli.wbs_library_id = v_wbs_library_id
              AND wli.project_id = p_project_id
            LIMIT 1;
        END IF;

        IF v_wbs_item_id IS NULL THEN
            -- Parse hierarchy to find parent
            WITH parts AS (
                SELECT string_to_array(v_wbs_code, '.') AS a
            )
            SELECT array_length(a, 1) AS lvl,
                   a[array_length(a,1)] AS in_level,
                   CASE WHEN array_length(a,1) > 1 THEN array_to_string(a[1:array_length(a,1)-1], '.') ELSE NULL END AS parent_code
            INTO v_level, v_in_level_code, v_parent_code
            FROM parts;

            v_parent_item_id := NULL;
            IF v_parent_code IS NOT NULL THEN
                IF v_map ? v_parent_code THEN
                    v_parent_item_id := (v_map->>v_parent_code)::uuid;
                ELSE
                    SELECT wli.wbs_library_item_id INTO v_parent_item_id
                    FROM public.wbs_library_item wli
                    WHERE wli.code = v_parent_code
                      AND wli.wbs_library_id = v_wbs_library_id
                      AND (wli.project_id = p_project_id OR wli.client_id = v_client_id OR wli.item_type = 'Standard')
                    LIMIT 1;

                    IF v_parent_item_id IS NULL THEN
                        -- Create a parent placeholder (non-recursive for grandparents)
                        WITH parts2 AS (
                            SELECT string_to_array(v_parent_code, '.') AS a
                        )
                        INSERT INTO public.wbs_library_item (
                            wbs_library_id, level, in_level_code, parent_item_id,
                            code, description, cost_scope, item_type, client_id, project_id
                        )
                        SELECT v_wbs_library_id,
                               v_level - 1,
                               a[array_length(a,1)],
                               NULL,
                               v_parent_code,
                               'Auto-created parent for ' || v_parent_code,
                               NULL,
                               'Custom',
                               v_client_id,
                               p_project_id
                        FROM parts2
                        RETURNING wbs_library_item_id INTO v_parent_item_id;
                        v_wbs_created_count := v_wbs_created_count + 1;
                    END IF;
                    v_map := v_map || jsonb_build_object(v_parent_code, v_parent_item_id);
                END IF;
            END IF;

            INSERT INTO public.wbs_library_item (
                wbs_library_id, level, in_level_code, parent_item_id,
                code, description, cost_scope, item_type, client_id, project_id
            ) VALUES (
                v_wbs_library_id,
                v_level,
                v_in_level_code,
                v_parent_item_id,
                v_wbs_code,
                v_description,
                v_description,
                'Custom',
                v_client_id,
                p_project_id
            ) RETURNING wbs_library_item_id INTO v_wbs_item_id;

            v_wbs_created_count := v_wbs_created_count + 1;
        END IF;

        v_map := v_map || jsonb_build_object(v_wbs_code, v_wbs_item_id);

        -- Calculate unit_rate if not manual override
        IF COALESCE(v_unit_rate_manual_override, false) THEN
            -- keep provided v_unit_rate (may be NULL)
        ELSE
            v_unit_rate := public.calculate_unit_item_cost(v_material_rate, v_labor_rate, v_productivity_per_hour);
        END IF;

        INSERT INTO public.budget_version_item (
            budget_version_id,
            wbs_library_item_id,
            quantity,
            unit,
            material_rate,
            labor_rate,
            productivity_per_hour,
            unit_rate_manual_override,
            unit_rate,
            factor,
            remarks,
            cost_certainty,
            design_certainty
        ) VALUES (
            v_new_version_id,
            v_wbs_item_id,
            v_quantity,
            v_unit,
            v_material_rate,
            v_labor_rate,
            v_productivity_per_hour,
            COALESCE(v_unit_rate_manual_override, false),
            COALESCE(v_unit_rate, 0),
            v_factor,
            v_remarks,
            NULL,
            NULL
        );

        v_inserted_count := v_inserted_count + 1;
    END LOOP;

    -- Record import
    INSERT INTO public.budget_import (
        project_id, source_filename, source_hash, pre_version_id, new_version_id,
        created_by_user_id
    ) VALUES (
        p_project_id,
        p_source_filename,
        p_source_hash,
        v_active_version_id,
        v_new_version_id,
        v_user_id
    ) RETURNING budget_import_id INTO v_import_id;

    v_duration_ms := EXTRACT(EPOCH FROM (clock_timestamp() - v_start)) * 1000;

    RETURN jsonb_build_object(
        'reused', false,
        'budget_import_id', v_import_id,
        'new_version_id', v_new_version_id,
        'pre_version_id', v_active_version_id,
        'inserted_count', v_inserted_count,
        'wbs_created_count', v_wbs_created_count,
        'duration_ms', v_duration_ms
    );
END;
$$;

COMMENT ON FUNCTION public.apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_items jsonb,
	p_source_hash text,
	p_notes text
) IS 'Applies a budget import by creating a new import version, ensuring WBS items, and inserting version items with per-project idempotency.';

ALTER FUNCTION public.apply_budget_import (
	p_project_id uuid,
	p_source_filename text,
	p_items jsonb,
	p_source_hash text,
	p_notes text
) OWNER TO "postgres";

-- ==========================================
-- undo_budget_import
-- ==========================================
CREATE OR REPLACE FUNCTION public.undo_budget_import (
	p_budget_import_id uuid,
	p_reason text DEFAULT 'Import undone'
) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_user_id uuid;
    v_project_id uuid;
    v_pre_version_id uuid;
    v_new_version_id uuid;
    v_import_record record;
    v_custom_wbs_items uuid[];
    v_wbs_item_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    v_user_id := auth.uid();

    -- Get import record and validate
    SELECT * INTO v_import_record
    FROM public.budget_import
    WHERE budget_import_id = p_budget_import_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Budget import not found: %', p_budget_import_id;
    END IF;

    IF v_import_record.is_undone THEN
        RAISE EXCEPTION 'Budget import already undone: %', p_budget_import_id;
    END IF;

    v_project_id := v_import_record.project_id;
    v_pre_version_id := v_import_record.pre_version_id;
    v_new_version_id := v_import_record.new_version_id;

    -- Permission check
    IF NOT public.can_modify_project(v_project_id) THEN
        RAISE EXCEPTION 'Forbidden: insufficient permissions to modify project %', v_project_id;
    END IF;

    -- Concurrency lock keyed by project
    PERFORM pg_advisory_xact_lock(
        hashtext('undo_budget_import'),
        hashtext(v_project_id::text)
    );

    -- Check if the import version is currently active
    IF EXISTS (
        SELECT 1 FROM public.project
        WHERE project_id = v_project_id
        AND active_budget_version_id = v_new_version_id
    ) THEN
        -- Revert to pre-import version
        UPDATE public.project
        SET active_budget_version_id = v_pre_version_id
        WHERE project_id = v_project_id;
    END IF;

    -- Find custom WBS items created by this import
    -- These are project-scoped WBS items that were created during the import
    SELECT ARRAY_AGG(DISTINCT wli.wbs_library_item_id) INTO v_custom_wbs_items
    FROM public.budget_version_item bvi
    JOIN public.wbs_library_item wli ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    WHERE bvi.budget_version_id = v_new_version_id
    AND wli.project_id = v_project_id
    AND wli.created_at >= v_import_record.created_at;

    -- Remove custom WBS items created by this import
    -- Only remove if they're not used in other versions or current budget
    IF v_custom_wbs_items IS NOT NULL THEN
        FOREACH v_wbs_item_id IN ARRAY v_custom_wbs_items
        LOOP
            -- Check if this WBS item is used elsewhere (in other versions)
            IF NOT EXISTS (
                SELECT 1 FROM public.budget_version_item bvi
                JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
                WHERE bvi.wbs_library_item_id = v_wbs_item_id
                AND bv.project_id = v_project_id
                AND bvi.budget_version_id != v_new_version_id
            ) THEN
                -- Safe to delete this custom WBS item
                DELETE FROM public.wbs_library_item
                WHERE wbs_library_item_id = v_wbs_item_id;
            END IF;
        END LOOP;
    END IF;

    -- Mark import as undone
    UPDATE public.budget_import
    SET is_undone = true,
        undone_at = NOW(),
        undone_by_user_id = v_user_id
    WHERE budget_import_id = p_budget_import_id;

    RETURN jsonb_build_object(
        'success', true,
        'budget_import_id', p_budget_import_id,
        'reverted_to_version_id', v_pre_version_id,
        'removed_wbs_items_count', COALESCE(array_length(v_custom_wbs_items, 1), 0),
        'reason', p_reason
    );
END;
$$;

COMMENT ON FUNCTION public.undo_budget_import (p_budget_import_id uuid, p_reason text) IS 'Undoes a budget import by reverting to the pre-import version and optionally removing custom WBS items created during import.';

ALTER FUNCTION public.undo_budget_import (p_budget_import_id uuid, p_reason text) OWNER TO "postgres";

-- ==========================================
-- diff_active_vs_items
-- ==========================================
CREATE OR REPLACE FUNCTION public.diff_active_vs_items (p_project_id uuid, p_items jsonb) RETURNS jsonb LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_user_id uuid;
    v_active_version_id uuid;
    v_wbs_library_id uuid;
    v_client_id uuid;
    result jsonb := '{}'::jsonb;
    total_active numeric := 0;
    total_import numeric := 0;
    v_item jsonb;
    v_new_wbs_codes text[] := '{}';
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    v_user_id := auth.uid();

    -- Get project details and validate access
    SELECT p.active_budget_version_id, p.wbs_library_id, p.client_id
    INTO v_active_version_id, v_wbs_library_id, v_client_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Project not found or access denied';
    END IF;

    -- Verify user can access this project
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Calculate total cost for active version
    IF v_active_version_id IS NOT NULL THEN
        SELECT COALESCE(SUM(
            COALESCE(bvi.quantity, 0) *
            COALESCE(bvi.unit_rate, bvi.material_rate, 0) *
            COALESCE(bvi.factor, 1)
        ), 0)
        INTO total_active
        FROM public.budget_version_item bvi
        WHERE bvi.budget_version_id = v_active_version_id;
    END IF;

    -- Calculate total cost for import items and collect new WBS codes
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Calculate cost for this item
        total_import := total_import + (
            COALESCE((v_item->>'quantity')::numeric, 0) *
            COALESCE((v_item->>'unit_rate')::numeric, (v_item->>'material_rate')::numeric, 0) *
            COALESCE((v_item->>'factor')::numeric, 1)
        );

        -- Check if WBS code exists
        IF NOT EXISTS (
            SELECT 1 FROM public.wbs_library_item wli
            WHERE wli.wbs_library_id = v_wbs_library_id
            AND wli.code = (v_item->>'code')
        ) THEN
            v_new_wbs_codes := array_append(v_new_wbs_codes, v_item->>'code');
        END IF;
    END LOOP;

    -- Build added items (items in import but not in active version)
    result := jsonb_set(result, '{added}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', import_item->>'code',
                'description', import_item->>'description',
                'quantity', (import_item->>'quantity')::numeric,
                'unit', import_item->>'unit',
                'material_rate', (import_item->>'material_rate')::numeric,
                'labor_rate', (import_item->>'labor_rate')::numeric,
                'productivity_per_hour', (import_item->>'productivity_per_hour')::numeric,
                'unit_rate_manual_override', (import_item->>'unit_rate_manual_override')::boolean,
                'unit_rate', (import_item->>'unit_rate')::numeric,
                'factor', (import_item->>'factor')::numeric,
                'remarks', import_item->>'remarks'
            )
        ), '[]'::jsonb)
        FROM jsonb_array_elements(p_items) import_item
        WHERE v_active_version_id IS NULL
        OR NOT EXISTS (
            SELECT 1
            FROM public.budget_version_item bvi
            JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
            WHERE bvi.budget_version_id = v_active_version_id
            AND wli.code = (import_item->>'code')
        )
    ));

    -- Build removed items (items in active version but not in import)
    result := jsonb_set(result, '{removed}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', wli.code,
                'description', wli.description,
                'quantity', bvi.quantity,
                'unit', bvi.unit,
                'material_rate', bvi.material_rate,
                'labor_rate', bvi.labor_rate,
                'productivity_per_hour', bvi.productivity_per_hour,
                'unit_rate_manual_override', bvi.unit_rate_manual_override,
                'unit_rate', bvi.unit_rate,
                'factor', bvi.factor,
                'remarks', bvi.remarks
            )
        ), '[]'::jsonb)
        FROM public.budget_version_item bvi
        JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        WHERE bvi.budget_version_id = v_active_version_id
        AND NOT EXISTS (
            SELECT 1
            FROM jsonb_array_elements(p_items) import_item
            WHERE (import_item->>'code') = wli.code
        )
    ));

    -- Build changed items (items that exist in both but have different values)
    result := jsonb_set(result, '{changed}', (
        SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'code', wli.code,
                'description', wli.description,
                'quantity', bvi.quantity,
                'unit', bvi.unit,
                'material_rate', bvi.material_rate,
                'labor_rate', bvi.labor_rate,
                'productivity_per_hour', bvi.productivity_per_hour,
                'unit_rate_manual_override', bvi.unit_rate_manual_override,
                'unit_rate', bvi.unit_rate,
                'factor', bvi.factor,
                'remarks', bvi.remarks,
                'diff', jsonb_build_object(
                    'quantity', jsonb_build_object(
                        'old', bvi.quantity,
                        'new', (import_item->>'quantity')::numeric
                    ),
                    'unit', jsonb_build_object(
                        'old', bvi.unit,
                        'new', import_item->>'unit'
                    ),
                    'material_rate', jsonb_build_object(
                        'old', bvi.material_rate,
                        'new', (import_item->>'material_rate')::numeric
                    ),
                    'labor_rate', jsonb_build_object(
                        'old', bvi.labor_rate,
                        'new', (import_item->>'labor_rate')::numeric
                    ),
                    'productivity_per_hour', jsonb_build_object(
                        'old', bvi.productivity_per_hour,
                        'new', (import_item->>'productivity_per_hour')::numeric
                    ),
                    'unit_rate', jsonb_build_object(
                        'old', bvi.unit_rate,
                        'new', (import_item->>'unit_rate')::numeric
                    ),
                    'factor', jsonb_build_object(
                        'old', bvi.factor,
                        'new', (import_item->>'factor')::numeric
                    ),
                    'remarks', jsonb_build_object(
                        'old', bvi.remarks,
                        'new', import_item->>'remarks'
                    )
                )
            )
        ), '[]'::jsonb)
        FROM public.budget_version_item bvi
        JOIN public.wbs_library_item wli ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        JOIN jsonb_array_elements(p_items) import_item ON (import_item->>'code') = wli.code
        WHERE bvi.budget_version_id = v_active_version_id
        AND (
            COALESCE(bvi.quantity, 0) != COALESCE((import_item->>'quantity')::numeric, 0)
            OR COALESCE(bvi.unit, '') != COALESCE(import_item->>'unit', '')
            OR COALESCE(bvi.material_rate, 0) != COALESCE((import_item->>'material_rate')::numeric, 0)
            OR COALESCE(bvi.labor_rate, 0) != COALESCE((import_item->>'labor_rate')::numeric, 0)
            OR COALESCE(bvi.productivity_per_hour, 0) != COALESCE((import_item->>'productivity_per_hour')::numeric, 0)
            OR COALESCE(bvi.unit_rate, 0) != COALESCE((import_item->>'unit_rate')::numeric, 0)
            OR COALESCE(bvi.factor, 1) != COALESCE((import_item->>'factor')::numeric, 1)
            OR COALESCE(bvi.remarks, '') != COALESCE(import_item->>'remarks', '')
        )
    ));

    -- Add new WBS codes array
    result := jsonb_set(result, '{new_wbs_codes}', to_jsonb(v_new_wbs_codes));

    -- Summary
    result := jsonb_set(result, '{summary}', jsonb_build_object(
        'version_a', COALESCE(v_active_version_id::text, 'none'),
        'version_b', 'import_preview',
        'total_cost_a', total_active,
        'total_cost_b', total_import,
        'total_cost_delta', (total_import - total_active)
    ));

    RETURN result;
END;
$$;

COMMENT ON FUNCTION public.diff_active_vs_items (p_project_id uuid, p_items jsonb) IS 'Returns JSONB diff between active budget version and import items without persisting data.';

ALTER FUNCTION public.diff_active_vs_items (p_project_id uuid, p_items jsonb) OWNER TO "postgres";

-- ==========================================
-- get_active_budget_version_items
-- ==========================================
CREATE OR REPLACE FUNCTION public.get_active_budget_version_items (p_project_id uuid) RETURNS TABLE (
	budget_line_item_id uuid,
	project_id uuid,
	wbs_library_item_id uuid,
	quantity numeric,
	unit text,
	material_rate numeric,
	labor_rate numeric,
	productivity_per_hour numeric,
	unit_rate_manual_override boolean,
	unit_rate numeric,
	factor numeric,
	remarks text,
	cost_certainty numeric,
	design_certainty numeric,
	created_at timestamptz,
	updated_at timestamptz
) LANGUAGE plpgsql SECURITY INVOKER
SET
	search_path = '' AS $$
DECLARE
    v_active_version_id uuid;
BEGIN
    -- Ensure authenticated user
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
    END IF;

    -- Access check
    IF NOT public.can_access_project(p_project_id) THEN
        RAISE EXCEPTION 'Forbidden: no access to project %', p_project_id;
    END IF;

    -- Get the active budget version for this project
    SELECT p.active_budget_version_id INTO v_active_version_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    -- If no active version exists, return empty result
    IF v_active_version_id IS NULL THEN
        RETURN;
    END IF;

    -- Return budget version items in the same format as budget_line_item_current
    RETURN QUERY
    SELECT
        bvi.budget_version_item_id AS budget_line_item_id,
        p_project_id AS project_id,
        bvi.wbs_library_item_id,
        bvi.quantity,
        bvi.unit,
        bvi.material_rate,
        bvi.labor_rate,
        bvi.productivity_per_hour,
        bvi.unit_rate_manual_override,
        bvi.unit_rate,
        bvi.factor,
        bvi.remarks,
        bvi.cost_certainty,
        bvi.design_certainty,
        bvi.created_at,
        bvi.updated_at
    FROM public.budget_version_item bvi
    WHERE bvi.budget_version_id = v_active_version_id
    ORDER BY bvi.wbs_library_item_id;
END;
$$;

COMMENT ON FUNCTION public.get_active_budget_version_items (p_project_id uuid) IS 'Returns budget items from the active budget version for a project in budget_line_item_current compatible format.';

ALTER FUNCTION public.get_active_budget_version_items (p_project_id uuid) OWNER TO "postgres";

-- ==========================================
-- find_existing_import
-- ==========================================
CREATE OR REPLACE FUNCTION public.find_existing_import (p_project_id uuid, p_source_hash text) RETURNS TABLE (
	"exists" boolean,
	budget_import_id uuid,
	version_id uuid,
	created_at timestamptz,
	filename text
) LANGUAGE sql SECURITY INVOKER
SET
	search_path = '' AS $$
    -- Ensure authenticated user has access to project
    SELECT
        CASE WHEN bi.budget_import_id IS NOT NULL THEN true ELSE false END as exists,
        bi.budget_import_id,
        bi.new_version_id as version_id,
        bi.created_at,
        bi.source_filename as filename
    FROM public.budget_import bi
    WHERE bi.project_id = p_project_id
    AND bi.source_hash = p_source_hash
    AND public.can_access_project(p_project_id)
    ORDER BY bi.created_at DESC
    LIMIT 1;
$$;

COMMENT ON FUNCTION public.find_existing_import (p_project_id uuid, p_source_hash text) IS 'Checks if an import with the given source hash already exists for the project.';

ALTER FUNCTION public.find_existing_import (p_project_id uuid, p_source_hash text) OWNER TO "postgres";
