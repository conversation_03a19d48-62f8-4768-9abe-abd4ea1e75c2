-- Organization Management Functions
-- This file contains functions for organization operations
CREATE OR REPLACE FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text" DEFAULT NULL,
	"logo_url" "text" DEFAULT NULL
) RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	new_org public.organization;
	user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	-- Create organization
	INSERT INTO public.organization(name, description, logo_url, created_by_user_id)
	VALUES (name, description, logo_url, user_id)
	RETURNING * INTO new_org;

	-- Return JSON object
	RETURN json_build_object(
		'org_id', new_org.org_id,
		'name', new_org.name,
		'description', new_org.description,
		'logo_url', new_org.logo_url
	);
END;
$$;

ALTER FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) IS 'Creates a new organization and returns JSON with organization details';

CREATE OR REPLACE FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") RETURNS TABLE (
	"org_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT o.org_id, o.name, o.description, o.logo_url, o.created_by_user_id, o.created_at, o.updated_at
	FROM public.organization o
	WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('organization'::public.entity_type, o.org_id);
END;
$$;

ALTER FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") IS 'Gets organization details by name for users with access';

-- Function to get all organizations for the current user (with role)
CREATE OR REPLACE FUNCTION "public"."get_orgs_for_user" () RETURNS TABLE (
	"org_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "public"."membership_role"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF v_user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	RETURN QUERY
	SELECT o.org_id,
	       o.name,
	       o.description,
	       o.logo_url,
	       o.created_by_user_id,
	       o.created_at,
	       o.updated_at,
	       m.role
	FROM public.membership m
	JOIN public.organization o ON o.org_id = m.entity_id
	WHERE m.user_id = v_user_id
	  AND m.entity_type = 'organization'::public.entity_type
	ORDER BY o.updated_at DESC;
END;
$$;

ALTER FUNCTION "public"."get_orgs_for_user" () OWNER TO "postgres";

GRANT
EXECUTE ON FUNCTION "public"."get_orgs_for_user" () TO authenticated;

GRANT
EXECUTE ON FUNCTION "public"."get_orgs_for_user" () TO service_role;

COMMENT ON FUNCTION "public"."get_orgs_for_user" () IS 'Returns organizations (with role) that the current user belongs to, using auth.uid().';

-- Function to get external dashboard data (clients and projects user has access to outside this org)
CREATE OR REPLACE FUNCTION public.get_external_dashboard_data () RETURNS json LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	current_user_id uuid := auth.uid();
	external_projects_data json;
	external_clients_data json;
BEGIN
	-- Ensure user is authenticated
	IF current_user_id IS NULL THEN
		RAISE EXCEPTION 'User ID cannot be null';
	END IF;

	-- Get projects where user has direct access but is not an organization member
	SELECT json_agg(
		json_build_object(
			'project_id', project_data.project_id,
			'name', project_data.name,
			'description', project_data.description,
			'created_at', project_data.created_at,
			'updated_at', project_data.updated_at,
			'client', json_build_object(
				'name', project_data.client_name,
				'organization', json_build_object(
					'name', project_data.org_name
				)
			),
			'is_project_admin', public.has_entity_role(current_user_id, 'project', project_data.project_id, 'admin'),
			'is_client_admin', public.has_entity_role(current_user_id, 'client', project_data.client_id, 'admin'),
			'is_org_admin', public.has_entity_role(current_user_id, 'organization', project_data.org_id, 'admin'),
			'is_external_access', true
		)
	) INTO external_projects_data
	FROM (
		SELECT DISTINCT p.project_id, p.name, p.description, p.created_at, p.updated_at,
			   c.client_id, c.name as client_name, o.org_id, o.name as org_name
		FROM public.project p
		INNER JOIN public.client c ON c.client_id = p.client_id
		INNER JOIN public.organization o ON o.org_id = c.org_id
		INNER JOIN public.membership m ON (
			(m.entity_type = 'project' AND m.entity_id = p.project_id) OR
			(m.entity_type = 'client' AND m.entity_id = c.client_id)
		)
		WHERE m.user_id = current_user_id
		-- Exclude projects where user is an organization member
		AND NOT EXISTS (
			SELECT 1 FROM public.membership om
			WHERE om.user_id = current_user_id
			AND om.entity_type = 'organization'
			AND om.entity_id = o.org_id
		)
		ORDER BY p.updated_at DESC
		LIMIT 5
	) project_data;

	-- Get clients where user has direct access but is not an organization member
	SELECT json_agg(
		json_build_object(
			'client_id', client_data.client_id,
			'name', client_data.name,
			'description', client_data.description,
			'logo_url', client_data.logo_url,
			'client_url', client_data.client_url,
			'internal_url', client_data.internal_url,
			'internal_url_description', client_data.internal_url_description,
			'org_id', client_data.org_id,
			'created_at', client_data.created_at,
			'updated_at', client_data.updated_at,
			'created_by_user_id', client_data.created_by_user_id,
			'organization', json_build_object(
				'name', client_data.org_name
			),
			'project_count', (
				SELECT COUNT(*)
				FROM public.project p2
				WHERE p2.client_id = client_data.client_id
				AND public.has_entity_access(current_user_id, 'project'::public.entity_type, p2.project_id)
			),
			'is_client_admin', public.has_entity_role(current_user_id, 'client', client_data.client_id, 'admin'),
			'is_org_admin', public.has_entity_role(current_user_id, 'organization', client_data.org_id, 'admin'),
			'is_external_access', true
		)
	) INTO external_clients_data
	FROM (
		SELECT DISTINCT c.client_id, c.name, c.description, c.logo_url, c.client_url, c.internal_url,
			   c.internal_url_description, c.org_id, c.created_at, c.updated_at, c.created_by_user_id,
			   o.name as org_name
		FROM public.client c
		INNER JOIN public.organization o ON o.org_id = c.org_id
		INNER JOIN public.membership m ON m.entity_type = 'client' AND m.entity_id = c.client_id
		WHERE m.user_id = current_user_id
		-- Exclude clients where user is an organization member
		AND NOT EXISTS (
			SELECT 1 FROM public.membership om
			WHERE om.user_id = current_user_id
			AND om.entity_type = 'organization'
			AND om.entity_id = o.org_id
		)
		ORDER BY c.updated_at DESC
		LIMIT 5
	) client_data;

	-- Return combined external data
	RETURN json_build_object(
		'external_projects', COALESCE(external_projects_data, '[]'::json),
		'external_clients', COALESCE(external_clients_data, '[]'::json)
	);
END;
$function$;

ALTER FUNCTION public.get_external_dashboard_data () OWNER TO postgres;

GRANT
EXECUTE ON FUNCTION public.get_external_dashboard_data () TO authenticated;

GRANT
EXECUTE ON FUNCTION public.get_external_dashboard_data () TO service_role;

COMMENT ON FUNCTION public.get_external_dashboard_data () IS 'Gets dashboard data for clients and projects where user has direct access but is not an organization member';

-- Dashboard data function to get recent projects and clients with permissions
CREATE OR REPLACE FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") RETURNS "json" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	projects_data json;
	clients_data json;
	user_id uuid := auth.uid();
BEGIN
	-- Ensure user is authenticated
	IF user_id IS NULL THEN
		RAISE EXCEPTION 'Not authenticated';
	END IF;

	-- Verify user has access to the organization
	IF NOT public.current_user_has_entity_access('organization'::public.entity_type, org_id_param) THEN
		RAISE EXCEPTION 'Access denied to organization';
	END IF;

	-- Get recent projects with client and organization info
	SELECT json_agg(
		json_build_object(
			'project_id', project_data.project_id,
			'name', project_data.name,
			'description', project_data.description,
			'created_at', project_data.created_at,
			'updated_at', project_data.updated_at,
			'client', json_build_object(
				'name', project_data.client_name,
				'organization', json_build_object(
					'name', project_data.org_name
				)
			),
			'is_project_admin', public.current_user_has_entity_role('project', project_data.project_id, 'admin'),
			'is_client_admin', public.current_user_has_entity_role('client', project_data.client_id, 'admin'),
			'is_org_admin', public.current_user_has_entity_role('organization', project_data.org_id, 'admin')
		)
	) INTO projects_data
	FROM (
		SELECT p.project_id, p.name, p.description, p.created_at, p.updated_at,
			   c.client_id, c.name as client_name, o.org_id, o.name as org_name
		FROM public.project p
		INNER JOIN public.client c ON c.client_id = p.client_id
		INNER JOIN public.organization o ON o.org_id = c.org_id
		WHERE o.org_id = org_id_param
		AND public.current_user_has_entity_access('project'::public.entity_type, p.project_id)
		ORDER BY p.updated_at DESC
		LIMIT 3
	) project_data;

	-- Get recent clients with project count and permissions
	SELECT json_agg(
		json_build_object(
			'client_id', client_data.client_id,
			'name', client_data.name,
			'description', client_data.description,
			'logo_url', client_data.logo_url,
			'client_url', client_data.client_url,
			'internal_url', client_data.internal_url,
			'internal_url_description', client_data.internal_url_description,
			'org_id', client_data.org_id,
			'created_at', client_data.created_at,
			'updated_at', client_data.updated_at,
			'created_by_user_id', client_data.created_by_user_id,
			'organization', json_build_object(
				'name', client_data.org_name
			),
			'project_count', (
				SELECT COUNT(*)
				FROM public.project p2
				WHERE p2.client_id = client_data.client_id
				AND public.current_user_has_entity_access('project'::public.entity_type, p2.project_id)
			),
			'is_client_admin', public.current_user_has_entity_role('client', client_data.client_id, 'admin'),
			'is_org_admin', public.current_user_has_entity_role('organization', client_data.org_id, 'admin')
		)
	) INTO clients_data
	FROM (
		SELECT c.client_id, c.name, c.description, c.logo_url, c.client_url, c.internal_url,
			   c.internal_url_description, c.org_id, c.created_at, c.updated_at, c.created_by_user_id,
			   o.name as org_name
		FROM public.client c
		INNER JOIN public.organization o ON o.org_id = c.org_id
		WHERE c.org_id = org_id_param
		AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id)
		ORDER BY c.updated_at DESC
		LIMIT 3
	) client_data;

	-- Return combined data
	RETURN json_build_object(
		'projects', COALESCE(projects_data, '[]'::json),
		'clients', COALESCE(clients_data, '[]'::json)
	);
END;
$$;

ALTER FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") OWNER TO "postgres";

GRANT
EXECUTE ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") TO authenticated;

GRANT
EXECUTE ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") TO service_role;

COMMENT ON FUNCTION "public"."get_dashboard_data" ("org_id_param" "uuid") IS 'Gets dashboard data including recent projects and clients with permissions for an organization';
