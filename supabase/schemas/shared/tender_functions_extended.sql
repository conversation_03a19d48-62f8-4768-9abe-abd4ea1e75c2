-- Extended Tender Analysis Functions
-- Contains additional RPC functions for tender analysis workflow
-- Function to get project budget transfers
CREATE OR REPLACE FUNCTION "public"."get_project_budget_transfers" (
	"project_id_param" uuid,
	"line_item_filter" uuid DEFAULT NULL,
	"limit_param" integer DEFAULT 50,
	"offset_param" integer DEFAULT 0
) RETURNS TABLE (
	budget_transfer_id uuid,
	tender_name text,
	line_item_description text,
	from_wbs_code text,
	from_wbs_description text,
	to_wbs_code text,
	to_wbs_description text,
	transfer_amount numeric(20, 4),
	reason text,
	created_by_name text,
	created_at timestamptz
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        bt.budget_transfer_id,
        t.tender_name,
        tli.description as line_item_description,
        from_wli.wbs_code as from_wbs_code,
        from_wli.description as from_wbs_description,
        to_wli.wbs_code as to_wbs_code,
        to_wli.description as to_wbs_description,
        bt.transfer_amount,
        bt.reason,
        p.full_name as created_by_name,
        bt.created_at
    FROM public.budget_transfer bt
    LEFT JOIN public.tender_line_item tli ON bt.tender_line_item_id = tli.tender_line_item_id
    LEFT JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    LEFT JOIN public.tender t ON tr.tender_id = t.tender_id
    JOIN public.wbs_library_item from_wli ON bt.from_wbs_library_item_id = from_wli.wbs_library_item_id
    JOIN public.wbs_library_item to_wli ON bt.to_wbs_library_item_id = to_wli.wbs_library_item_id
    JOIN public.profile p ON bt.created_by_user_id = p.user_id
    WHERE bt.project_id = project_id_param
    AND (line_item_filter IS NULL OR bt.tender_line_item_id = line_item_filter)
    ORDER BY bt.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$;

ALTER FUNCTION "public"."get_project_budget_transfers" (uuid, uuid, integer, integer) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_project_budget_transfers" (uuid, uuid, integer, integer) IS 'Returns budget transfers for a project with optional filtering by line item';

-- Function to get tender comparison data
CREATE OR REPLACE FUNCTION "public"."get_tender_comparison_data" (
	"project_id_param" uuid,
	"tender_ids" uuid[] DEFAULT NULL,
	"status_filter" "public"."tender_status" [] DEFAULT NULL
) RETURNS TABLE (
	wbs_code text,
	wbs_description text,
	wbs_library_item_id uuid,
	budget_amount numeric(20, 4),
	tender_data jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    WITH project_wbs AS (
        -- Get all WBS items for the project with budget amounts
        SELECT 
            wli.wbs_library_item_id,
            wli.wbs_code,
            wli.description,
            bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1) as budget_amount
        FROM public.wbs_library_item wli
        JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
        JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
        JOIN public.project p ON bv.project_id = p.project_id
        WHERE p.project_id = project_id_param
        AND p.active_budget_version_id = bv.budget_version_id
    ),
    filtered_tenders AS (
        -- Get tenders based on filters
        SELECT t.tender_id, t.tender_name, t.status
        FROM public.tender t
        WHERE t.project_id = project_id_param
        AND (tender_ids IS NULL OR t.tender_id = ANY(tender_ids))
        AND (status_filter IS NULL OR t.status = ANY(status_filter))
    ),
    tender_line_data AS (
        -- Get line item data for each tender mapped to WBS items
        SELECT 
            twm.wbs_library_item_id,
            ft.tender_id,
            ft.tender_name,
            ft.status,
            jsonb_build_object(
                'tender_id', ft.tender_id,
                'tender_name', ft.tender_name,
                'line_items', jsonb_agg(
                    jsonb_build_object(
                        'line_item_id', tli.tender_line_item_id,
                        'description', tli.description,
                        'quantity', tli.quantity,
                        'unit', tli.unit,
                        'unit_rate', tli.unit_rate,
                        'subtotal', tli.subtotal,
                        'coverage_percentage', twm.coverage_percentage,
                        'coverage_quantity', twm.coverage_quantity,
                        'normalization_type', tli.normalization_type,
                        'normalization_amount', tli.normalization_amount,
                        'normalization_percentage', tli.normalization_percentage
                    ) ORDER BY tli.line_number
                )
            ) as tender_data
        FROM public.tender_wbs_mapping twm
        JOIN public.tender_line_item tli ON twm.tender_line_item_id = tli.tender_line_item_id
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id AND tr.is_current = true
        JOIN filtered_tenders ft ON tr.tender_id = ft.tender_id
        GROUP BY twm.wbs_library_item_id, ft.tender_id, ft.tender_name, ft.status
    )
    SELECT 
        pw.wbs_code,
        pw.description as wbs_description,
        pw.wbs_library_item_id,
        pw.budget_amount,
        COALESCE(
            jsonb_agg(tld.tender_data ORDER BY tld.tender_name) FILTER (WHERE tld.tender_data IS NOT NULL),
            '[]'::jsonb
        ) as tender_data
    FROM project_wbs pw
    LEFT JOIN tender_line_data tld ON pw.wbs_library_item_id = tld.wbs_library_item_id
    GROUP BY pw.wbs_library_item_id, pw.wbs_code, pw.description, pw.budget_amount
    ORDER BY pw.wbs_code;
END;
$$;

ALTER FUNCTION "public"."get_tender_comparison_data" (uuid, uuid[], "public"."tender_status" []) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_tender_comparison_data" (uuid, uuid[], "public"."tender_status" []) IS 'Returns tender comparison data organized by WBS items with optional filtering by tender IDs and status';
