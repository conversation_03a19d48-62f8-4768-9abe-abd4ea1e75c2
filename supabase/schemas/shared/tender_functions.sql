-- Tender Analysis Functions
-- Contains RPC functions for tender analysis workflow
-- Function to get project tenders with filtering and sorting
CREATE OR REPLACE FUNCTION "public"."get_project_tenders" (
	"project_id_param" uuid,
	"status_filter" "public"."tender_status" DEFAULT NULL,
	"vendor_filter" uuid DEFAULT NULL,
	"limit_param" integer DEFAULT 50,
	"offset_param" integer DEFAULT 0
) RETURNS TABLE (
	tender_id uuid,
	vendor_name text,
	tender_name text,
	description text,
	submission_date date,
	currency_code text,
	currency_symbol text,
	symbol_position text,
	status "public"."tender_status",
	notes text,
	current_revision_id uuid,
	revision_number integer,
	line_item_count bigint,
	total_amount numeric(20, 4),
	created_at timestamptz,
	updated_at timestamptz
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        t.tender_id,
        v.name as vendor_name,
        t.tender_name,
        t.description,
        t.submission_date,
        t.currency_code,
        c.symbol as currency_symbol,
        c.symbol_position,
        t.status,
        t.notes,
        tr.tender_revision_id as current_revision_id,
        tr.revision_number,
        COALESCE(line_counts.line_item_count, 0) as line_item_count,
        COALESCE(line_totals.total_amount, 0) as total_amount,
        t.created_at,
        t.updated_at
    FROM public.tender t
    JOIN public.vendor v ON t.vendor_id = v.vendor_id
    JOIN public.currency c ON t.currency_code = c.currency_code
    LEFT JOIN public.tender_revision tr ON t.tender_id = tr.tender_id AND tr.is_current = true
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            COUNT(tli.tender_line_item_id) as line_item_count
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_counts ON t.tender_id = line_counts.tender_id
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            SUM(tli.subtotal) as total_amount
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_totals ON t.tender_id = line_totals.tender_id
    WHERE t.project_id = project_id_param
    AND (status_filter IS NULL OR t.status = status_filter)
    AND (vendor_filter IS NULL OR t.vendor_id = vendor_filter)
    ORDER BY t.submission_date DESC, t.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$;

ALTER FUNCTION "public"."get_project_tenders" (
	uuid,
	"public"."tender_status",
	uuid,
	integer,
	integer
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_project_tenders" (
	uuid,
	"public"."tender_status",
	uuid,
	integer,
	integer
) IS 'Returns tenders for a project with optional filtering by status and vendor, including summary information';

-- Function to calculate normalization amount from percentage
CREATE OR REPLACE FUNCTION "public"."calculate_normalization_amount" (
	"tender_line_item_id_param" uuid,
	"normalization_percentage_param" numeric(5, 2)
) RETURNS TABLE (
	calculated_amount numeric(20, 4),
	total_budget_amount numeric(20, 4),
	mapping_count integer
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    project_id_var uuid;
    total_budget numeric(20, 4) := 0;
    mapping_count_var integer := 0;
BEGIN
    -- Get project ID for access control
    SELECT t.project_id INTO project_id_var
    FROM public.tender_line_item tli
    JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    JOIN public.tender t ON tr.tender_id = t.tender_id
    WHERE tli.tender_line_item_id = tender_line_item_id_param;

    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_var, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Calculate total budget amount from mapped WBS items with coverage
    SELECT 
        COALESCE(SUM(
            (bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)) * 
            COALESCE(twm.coverage_percentage / 100.0, 1.0)
        ), 0),
        COUNT(twm.tender_wbs_mapping_id)
    INTO total_budget, mapping_count_var
    FROM public.tender_wbs_mapping twm
    JOIN public.wbs_library_item wli ON twm.wbs_library_item_id = wli.wbs_library_item_id
    JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE twm.tender_line_item_id = tender_line_item_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    RETURN QUERY
    SELECT 
        (total_budget * normalization_percentage_param / 100.0) as calculated_amount,
        total_budget as total_budget_amount,
        mapping_count_var as mapping_count;
END;
$$;

ALTER FUNCTION "public"."calculate_normalization_amount" (uuid, numeric(5, 2)) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."calculate_normalization_amount" (uuid, numeric(5, 2)) IS 'Calculates normalization amount from percentage based on mapped WBS budget amounts with coverage';

-- Function to validate budget transfer
CREATE OR REPLACE FUNCTION "public"."validate_budget_transfer" (
	"project_id_param" uuid,
	"from_wbs_item_id" uuid,
	"to_wbs_item_id" uuid,
	"transfer_amount" numeric(20, 4)
) RETURNS TABLE (
	is_valid boolean,
	error_message text,
	from_available_amount numeric(20, 4),
	to_current_amount numeric(20, 4),
	existing_transfers numeric(20, 4)
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    from_budget_amount numeric(20, 4);
    from_transferred_out numeric(20, 4);
    from_transferred_in numeric(20, 4);
    available_amount numeric(20, 4);
    to_budget_amount numeric(20, 4);
    to_transferred_in numeric(20, 4);
    to_transferred_out numeric(20, 4);
    current_to_amount numeric(20, 4);
    existing_transfer_amount numeric(20, 4);
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Basic input validation before looking up budget data
    IF transfer_amount IS NULL THEN
        RETURN QUERY
        SELECT
            false,
            'Transfer amount is required',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    IF from_wbs_item_id = to_wbs_item_id THEN
        RETURN QUERY
        SELECT
            false,
            'Source and target WBS items must be different',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    -- Get original budget amount for from_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO from_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = from_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Get original budget amount for to_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO to_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = to_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate existing transfers
    SELECT
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO from_transferred_out, from_transferred_in
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    SELECT
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO to_transferred_in, to_transferred_out
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    -- Check for existing transfer between these specific items
    SELECT COALESCE(SUM(transfer_amount), 0)
    INTO existing_transfer_amount
    FROM public.budget_transfer
    WHERE project_id = project_id_param
    AND from_wbs_library_item_id = from_wbs_item_id
    AND to_wbs_library_item_id = to_wbs_item_id;

    -- Calculate available amount (original budget + transfers in - transfers out)
    available_amount := COALESCE(from_budget_amount, 0) + COALESCE(from_transferred_in, 0) - COALESCE(from_transferred_out, 0);
    current_to_amount := COALESCE(to_budget_amount, 0) + COALESCE(to_transferred_in, 0) - COALESCE(to_transferred_out, 0);

    -- Validate transfer
    RETURN QUERY
    SELECT
        CASE
            WHEN from_budget_amount IS NULL THEN false
            WHEN to_budget_amount IS NULL THEN false
            WHEN transfer_amount <= 0 THEN false
            WHEN transfer_amount > available_amount THEN false
            ELSE true
        END as is_valid,
        CASE
            WHEN from_budget_amount IS NULL THEN 'Source WBS item not found in budget'
            WHEN to_budget_amount IS NULL THEN 'Target WBS item not found in budget'
            WHEN transfer_amount <= 0 THEN 'Transfer amount must be positive'
            WHEN transfer_amount > available_amount THEN 'Transfer amount exceeds available budget'
            ELSE NULL
        END as error_message,
        available_amount as from_available_amount,
        current_to_amount as to_current_amount,
        existing_transfer_amount as existing_transfers;
END;
$$;

ALTER FUNCTION "public"."validate_budget_transfer" (uuid, uuid, uuid, numeric(20, 4)) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."validate_budget_transfer" (uuid, uuid, uuid, numeric(20, 4)) IS 'Validates budget transfer between WBS items and returns validation results with current amounts';

-- Function to create budget transfer
CREATE OR REPLACE FUNCTION "public"."create_budget_transfer" (
	"project_id_param" uuid,
	"line_item_id_param" uuid,
	"from_wbs_item_id" uuid,
	"to_wbs_item_id" uuid,
	"transfer_amount" numeric(20, 4),
	"reason" text
) RETURNS TABLE (
	budget_transfer_id uuid,
	is_valid boolean,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    validation_result record;
    new_transfer_id uuid;
    line_item_project_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Ensure the optional tender line item belongs to the same project
    IF line_item_id_param IS NOT NULL THEN
        SELECT t.project_id
        INTO line_item_project_id
        FROM public.tender_line_item tli
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
        JOIN public.tender t ON tr.tender_id = t.tender_id
        WHERE tli.tender_line_item_id = line_item_id_param;

        IF line_item_project_id IS NULL THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item not found';
            RETURN;
        ELSIF line_item_project_id <> project_id_param THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item does not belong to project';
            RETURN;
        END IF;
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid as budget_transfer_id,
            false as is_valid,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true as is_valid,
        NULL::text as error_message;
END;
$$;

ALTER FUNCTION "public"."create_budget_transfer" (uuid, uuid, uuid, uuid, numeric(20, 4), text) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_budget_transfer" (uuid, uuid, uuid, uuid, numeric(20, 4), text) IS 'Creates a budget transfer after validation and returns the result';
