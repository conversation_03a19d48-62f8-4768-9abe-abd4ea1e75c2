-- Project Stage Management Functions
-- This file contains functions for project stage operations
CREATE OR REPLACE FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text" DEFAULT NULL
) RETURNS UUID LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_snapshot_id UUID;
	v_project_id UUID;
	v_is_ready BOOLEAN;
	v_stage_version_id UUID;
	v_active_version_id UUID;
	v_user_id UUID;
	v_stage_name TEXT;
    -- No new working version is created here; we only freeze a stage version
BEGIN
	-- Get current user ID
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		RAISE EXCEPTION 'Unauthorized: auth.uid() is NULL';
	END IF;

	-- Get project_id and stage name for permission check and labeling
	SELECT ps.project_id, ps.name INTO v_project_id, v_stage_name
	FROM public.project_stage ps
	WHERE ps.project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;

	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;

	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;

	-- Get current active budget version for lineage
	SELECT active_budget_version_id INTO v_active_version_id
	FROM public.project
	WHERE project_id = v_project_id;

	-- Create a budget version for this stage completion
	INSERT INTO public.budget_version (
		project_id,
		label,
		kind,
		stage_id,
		prev_version_id,
		created_by_user_id
	) VALUES (
		v_project_id,
		'Stage Completion: ' || v_stage_name,
		'stage'::public.budget_version_kind,
		p_project_stage_id,
		v_active_version_id,
		v_user_id
	) RETURNING budget_version_id INTO v_stage_version_id;

	-- Copy budget items from active version to the new stage version
	INSERT INTO public.budget_version_item (
		budget_version_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
	SELECT
		v_stage_version_id,
		bvi.wbs_library_item_id,
		bvi.quantity,
		bvi.unit,
		bvi.material_rate,
		bvi.labor_rate,
		bvi.productivity_per_hour,
		bvi.unit_rate_manual_override,
		bvi.unit_rate,
		bvi.factor,
		bvi.remarks,
		bvi.cost_certainty,
		bvi.design_certainty
	FROM public.budget_version_item bvi
	WHERE bvi.budget_version_id = v_active_version_id;

	-- Update the stage
	UPDATE public.project_stage
	SET
		date_completed = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;

    -- Create a budget snapshot linked to the stage version (read-only record of the stage)
    SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes, v_stage_version_id) INTO v_snapshot_id;

    -- Do not change the active budget version here to avoid creating redundant
    -- "working" versions per stage. The existing active version continues as the
    -- working copy, while the new stage version + snapshot preserve history.

	RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) IS 'Completes a project stage if all checklist items are complete';

-- Function that depends on project table - moved here to resolve dependency order
CREATE OR REPLACE FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") RETURNS TABLE (
	"client_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"client_url" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"org_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"created_by_user_id" "uuid",
	"organization_name" "text",
	"project_count" bigint,
	"is_client_admin" boolean,
	"is_org_admin" boolean
) LANGUAGE "sql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
SELECT c.client_id,
	c.name,
	c.description,
	c.logo_url,
	c.client_url,
	c.internal_url,
	c.internal_url_description,
	c.org_id,
	c.created_at,
	c.updated_at,
	c.created_by_user_id,
	o.name AS organization_name,
	(
		SELECT COUNT(*)
		FROM public.project p
		WHERE p.client_id = c.client_id
	) AS project_count,
	public.current_user_has_entity_role('client', c.client_id, 'admin') AS is_client_admin,
	public.current_user_has_entity_role('organization', c.org_id, 'admin') AS is_org_admin
FROM public.client c
	INNER JOIN public.organization o ON o.org_id = c.org_id
WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id)
ORDER BY c.name;
$$;

ALTER FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") IS 'Gets clients with user permissions for an organization';

-- Function to check if the current project stage is the construction stage
CREATE OR REPLACE FUNCTION "public"."is_construction_stage" ("project_id_param" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_construction_stage_id UUID;
	v_current_stage_id UUID;
BEGIN
	-- Check if user can access this project
	IF NOT public.can_access_project(project_id_param) THEN
		RAISE EXCEPTION 'Insufficient permissions to access this project';
	END IF;
	
	-- Get the construction stage ID for this project
	SELECT construction_stage_id INTO v_construction_stage_id
	FROM public.project
	WHERE project_id = project_id_param;
	
	-- If no construction stage is set, return false
	IF v_construction_stage_id IS NULL THEN
		RETURN FALSE;
	END IF;
	
	-- Find the first incomplete stage (ordered by stage_order)
	-- If this returns NULL, then all stages are completed (no current stage)
	SELECT project_stage_id INTO v_current_stage_id
	FROM public.project_stage
	WHERE project_id = project_id_param
	AND date_completed IS NULL
	ORDER BY stage_order
	LIMIT 1;
	
	-- If no incomplete stages found, all stages are completed (no current stage)
	IF v_current_stage_id IS NULL THEN
		RETURN FALSE;
	END IF;
	
	-- Return true if current stage is the construction stage
	RETURN v_current_stage_id = v_construction_stage_id;
END;
$$;

ALTER FUNCTION "public"."is_construction_stage" ("project_id_param" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."is_construction_stage" ("project_id_param" "uuid") IS 'Checks if the current active project stage is the construction stage';
