-- Shared Audit Functions
-- Contains audit trigger functions used by multiple tables
-- Vendor Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_vendor_changes" () R<PERSON>UR<PERSON> "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.vendor_id, OLD.name, OLD.description, OLD.org_id, OLD.client_id, OLD.project_id,
            OLD.contact_name, OLD.contact_email, OLD.contact_phone, OLD.contact_address, OLD.website,
            OLD.vendor_type, OLD.tax_id, OLD.payment_terms, OLD.payment_terms_days, OLD.credit_limit,
            OLD.currency, OLD.is_active, OLD.certification_info, OLD.insurance_info, OLD.additional_data,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_vendor_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_vendor_changes" () IS 'Audit trigger function for vendor table';

-- Purchase Order Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_purchase_order_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.purchase_order_id, OLD.po_number, OLD.description, OLD.po_date, OLD.project_id, OLD.vendor_id,
            OLD.account, OLD.original_amount, OLD.co_amount, OLD.freight, OLD.tax, OLD.other,
            OLD.notes, OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id, work_package_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id, NEW.work_package_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id, work_package_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id, NEW.work_package_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_purchase_order_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_purchase_order_changes" () IS 'Audit trigger function for purchase_order table';

-- Work Package Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_work_package_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.work_package_id, OLD.name, OLD.description, OLD.project_id,
            OLD.wbs_library_item_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id,
            NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            work_package_id, name, description, project_id,
            wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id,
            NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_work_package_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_work_package_changes" () IS 'Audit trigger function for work_package table';

-- Invoice Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_invoice_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.invoice_id, OLD.purchase_order_id, OLD.description, OLD.invoice_date,
            OLD.account, OLD.amount, OLD.period, OLD.post_date, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.invoice_id, NEW.purchase_order_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.invoice_audit (
            operation_type, changed_by, changed_at, new_values,
            invoice_id, purchase_order_id, description, invoice_date,
            account, amount, period, post_date, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.invoice_id, NEW.purchase_order_id, NEW.description, NEW.invoice_date,
            NEW.account, NEW.amount, NEW.period, NEW.post_date, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_invoice_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_invoice_changes" () IS 'Audit trigger function for invoice table';

-- Tender Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_id, OLD.project_id, OLD.vendor_id, OLD.tender_name, OLD.description,
            OLD.submission_date, OLD.currency_code, OLD.status, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_id, NEW.project_id, NEW.vendor_id, NEW.tender_name, NEW.description,
            NEW.submission_date, NEW.currency_code, NEW.status, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_id, project_id, vendor_id, tender_name, description,
            submission_date, currency_code, status, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_id, NEW.project_id, NEW.vendor_id, NEW.tender_name, NEW.description,
            NEW.submission_date, NEW.currency_code, NEW.status, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_changes" () IS 'Audit trigger function for tender table changes';

-- Tender Revision Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_revision_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_revision_id, OLD.tender_id, OLD.revision_number, OLD.is_current, OLD.revision_notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_revision_id, NEW.tender_id, NEW.revision_number, NEW.is_current, NEW.revision_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_revision_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_revision_id, tender_id, revision_number, is_current, revision_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_revision_id, NEW.tender_id, NEW.revision_number, NEW.is_current, NEW.revision_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_revision_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_revision_changes" () IS 'Audit trigger function for tender_revision table changes';

-- Tender Line Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_line_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_line_item_id, OLD.tender_revision_id, OLD.line_number, OLD.description, OLD.quantity,
            OLD.unit, OLD.unit_rate, OLD.material_rate, OLD.labor_rate, OLD.productivity_factor, OLD.subtotal,
            OLD.normalization_type, OLD.normalization_amount, OLD.normalization_percentage, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_line_item_id, NEW.tender_revision_id, NEW.line_number, NEW.description, NEW.quantity,
            NEW.unit, NEW.unit_rate, NEW.material_rate, NEW.labor_rate, NEW.productivity_factor, NEW.subtotal,
            NEW.normalization_type, NEW.normalization_amount, NEW.normalization_percentage, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_line_item_id, tender_revision_id, line_number, description, quantity,
            unit, unit_rate, material_rate, labor_rate, productivity_factor, subtotal,
            normalization_type, normalization_amount, normalization_percentage, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_line_item_id, NEW.tender_revision_id, NEW.line_number, NEW.description, NEW.quantity,
            NEW.unit, NEW.unit_rate, NEW.material_rate, NEW.labor_rate, NEW.productivity_factor, NEW.subtotal,
            NEW.normalization_type, NEW.normalization_amount, NEW.normalization_percentage, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_line_item_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_line_item_changes" () IS 'Audit trigger function for tender_line_item table changes';

-- Tender WBS Mapping Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_wbs_mapping_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_wbs_mapping_id, OLD.tender_line_item_id, OLD.wbs_library_item_id,
            OLD.coverage_percentage, OLD.coverage_quantity, OLD.notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_wbs_mapping_id, NEW.tender_line_item_id, NEW.wbs_library_item_id,
            NEW.coverage_percentage, NEW.coverage_quantity, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_wbs_mapping_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_wbs_mapping_id, tender_line_item_id, wbs_library_item_id,
            coverage_percentage, coverage_quantity, notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_wbs_mapping_id, NEW.tender_line_item_id, NEW.wbs_library_item_id,
            NEW.coverage_percentage, NEW.coverage_quantity, NEW.notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_wbs_mapping_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_wbs_mapping_changes" () IS 'Audit trigger function for tender_wbs_mapping table changes';

-- Budget Transfer Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_budget_transfer_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_transfer_id, OLD.project_id, OLD.tender_line_item_id,
            OLD.from_wbs_library_item_id, OLD.to_wbs_library_item_id, OLD.transfer_amount, OLD.reason,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_transfer_id, NEW.project_id, NEW.tender_line_item_id,
            NEW.from_wbs_library_item_id, NEW.to_wbs_library_item_id, NEW.transfer_amount, NEW.reason,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_transfer_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_transfer_id, project_id, tender_line_item_id,
            from_wbs_library_item_id, to_wbs_library_item_id, transfer_amount, reason,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_transfer_id, NEW.project_id, NEW.tender_line_item_id,
            NEW.from_wbs_library_item_id, NEW.to_wbs_library_item_id, NEW.transfer_amount, NEW.reason,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_transfer_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_budget_transfer_changes" () IS 'Audit trigger function for budget_transfer table changes';

-- Tender Scoring Criteria Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_scoring_criteria_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_scoring_criteria_id, OLD.project_id, OLD.criteria_name, OLD.description,
            OLD.weight, OLD.max_score, OLD.is_active,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_scoring_criteria_id, NEW.project_id, NEW.criteria_name, NEW.description,
            NEW.weight, NEW.max_score, NEW.is_active,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_scoring_criteria_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_scoring_criteria_id, project_id, criteria_name, description,
            weight, max_score, is_active,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_scoring_criteria_id, NEW.project_id, NEW.criteria_name, NEW.description,
            NEW.weight, NEW.max_score, NEW.is_active,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_scoring_criteria_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_scoring_criteria_changes" () IS 'Audit trigger function for tender_scoring_criteria table changes';

-- Tender Score Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_score_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_score_id, OLD.tender_id, OLD.tender_scoring_criteria_id, OLD.score, OLD.comments,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_score_id, NEW.tender_id, NEW.tender_scoring_criteria_id, NEW.score, NEW.comments,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_score_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_score_id, tender_id, tender_scoring_criteria_id, score, comments,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_score_id, NEW.tender_id, NEW.tender_scoring_criteria_id, NEW.score, NEW.comments,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_score_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_score_changes" () IS 'Audit trigger function for tender_score table changes';

-- Tender Work Package Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_tender_work_package_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.tender_work_package_id, OLD.tender_id, OLD.work_package_name, OLD.work_package_description,
            OLD.conversion_date, OLD.conversion_notes,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.tender_work_package_id, NEW.tender_id, NEW.work_package_name, NEW.work_package_description,
            NEW.conversion_date, NEW.conversion_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.tender_work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            tender_work_package_id, tender_id, work_package_name, work_package_description,
            conversion_date, conversion_notes,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.tender_work_package_id, NEW.tender_id, NEW.work_package_name, NEW.work_package_description,
            NEW.conversion_date, NEW.conversion_notes,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_tender_work_package_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_tender_work_package_changes" () IS 'Audit trigger function for tender_work_package table changes';
