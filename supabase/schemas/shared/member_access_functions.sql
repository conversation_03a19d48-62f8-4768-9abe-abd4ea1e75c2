-- Member Access Functions
-- This file contains functions for getting member information and access
CREATE OR REPLACE FUNCTION "public"."get_organization_members" ("_org_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "text",
	"membership_id" "uuid"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare
	_org_id uuid;
	_has_access boolean;
begin
	-- Get the org_id for the organization name
	select org_id into _org_id
	from public.organization
	where name = _org_name;

	if _org_id is null then
		raise exception 'Organization not found: %', _org_name;
	end if;

	-- Check if current user has access to this organization
	select public.current_user_has_entity_access('organization', _org_id) into _has_access;

	if not _has_access then
		raise exception 'Access denied: no access to organization';
	end if;

	-- Return organization members
	return query
	select
		p.user_id,
		p.email,
		p.full_name,
		p.avatar_url,
		p.created_at,
		p.updated_at,
		m.role::text as role,
		m.membership_id
	from public.profile p
	join public.membership m on p.user_id = m.user_id
	where m.entity_type = 'organization'
		and m.entity_id = _org_id
	order by m.role desc, m.created_at asc; -- Admin first, then by creation time
end;
$$;

ALTER FUNCTION "public"."get_organization_members" ("_org_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_organization_members" ("_org_name" "text") IS 'Gets all members of an organization by organization name';

CREATE OR REPLACE FUNCTION "public"."get_client_members" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "text",
	"membership_id" "uuid",
	"access_via" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
declare _client_id uuid;
_has_admin_access boolean;
begin -- Get the client_id for the client name
select client_id into _client_id
from public.client
where name = _client_name;
if _client_id is null then raise exception 'Client not found: %',
_client_name;
end if;
select public.current_user_has_entity_role('client', _client_id, 'admin') into _has_admin_access;
if not _has_admin_access then raise exception 'Access denied: must be a client admin';
end if;
return query
select *
from public.profiles_with_client_access(_client_name);
end;
$$;

ALTER FUNCTION "public"."get_client_members" ("_client_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_client_members" ("_client_name" "text") IS 'Gets all members of a client by client name';

CREATE OR REPLACE FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"role" "text",
	"membership_id" "uuid",
	"access_via" "text"
) LANGUAGE "sql" STABLE SECURITY DEFINER
SET
	"search_path" TO '' AS $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'organization'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization'
	and m.entity_id = c.org_id
where c.name = _client_name
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'client'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client'
	and m.entity_id = c.client_id
where c.name = _client_name
order by created_at desc;
$$;

ALTER FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") IS 'Gets all profiles with access to a specific client';

CREATE OR REPLACE FUNCTION "public"."profiles_with_project_access" ("_project_id" "uuid") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"access_via" "text",
	"role" "text"
) LANGUAGE "sql" STABLE SECURITY DEFINER
SET
	"search_path" TO '' AS $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'organization'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization' and m.entity_id = c.org_id
	join public.project pr on pr.client_id = c.client_id
where pr.project_id = _project_id
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'client'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client' and m.entity_id = c.client_id
	join public.project pr on pr.client_id = c.client_id
where pr.project_id = _project_id
union all
/* ── via PROJECT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'project'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.project pr on m.entity_type = 'project' and m.entity_id = pr.project_id
where pr.project_id = _project_id
order by created_at desc;
$$;

ALTER FUNCTION "public"."profiles_with_project_access" ("_project_id" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."profiles_with_project_access" ("_project_id" "uuid") IS 'Gets all profiles with access to a specific project';
